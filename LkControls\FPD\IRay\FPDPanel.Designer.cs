﻿namespace InduVision.LkControls.FPD.IRay
{
    partial class FPDPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            connectDevice1 = new ConnectDevice();
            staticOperatePanel1 = new StaticOperatePanel();
            dynamicOperatePanel1 = new DynamicOperatePanel();
            SuspendLayout();
            // 
            // connectDevice1
            // 
            connectDevice1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            connectDevice1.Dock = System.Windows.Forms.DockStyle.Top;
            connectDevice1.Location = new System.Drawing.Point(5, 5);
            connectDevice1.Name = "connectDevice1";
            connectDevice1.Size = new System.Drawing.Size(314, 551);
            connectDevice1.TabIndex = 0;
            connectDevice1.ConnectedDevice += ConnectDevice1_ConnectedDevice;
            // 
            // staticOperatePanel1
            // 
            staticOperatePanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            staticOperatePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            staticOperatePanel1.Location = new System.Drawing.Point(5, 556);
            staticOperatePanel1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            staticOperatePanel1.Name = "staticOperatePanel1";
            staticOperatePanel1.Size = new System.Drawing.Size(314, 600);
            staticOperatePanel1.TabIndex = 1;
            staticOperatePanel1.Visible = false;
            staticOperatePanel1.DisconnectedDevice += DisconnectedDevice;
            // 
            // dynamicOperatePanel1
            // 
            dynamicOperatePanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            dynamicOperatePanel1.Dock = System.Windows.Forms.DockStyle.Top;
            dynamicOperatePanel1.Location = new System.Drawing.Point(5, 872);
            dynamicOperatePanel1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            dynamicOperatePanel1.Name = "dynamicOperatePanel1";
            dynamicOperatePanel1.Size = new System.Drawing.Size(314, 600);
            dynamicOperatePanel1.TabIndex = 2;
            dynamicOperatePanel1.Visible = false;
            dynamicOperatePanel1.DisconnectedDevice += DisconnectedDevice;
            // 
            // FPDPanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            Controls.Add(dynamicOperatePanel1);
            Controls.Add(staticOperatePanel1);
            Controls.Add(connectDevice1);
            Name = "FPDPanel";
            Padding = new System.Windows.Forms.Padding(5);
            Size = new System.Drawing.Size(324, 1186);
            ResumeLayout(false);
        }

        #endregion

        private ConnectDevice connectDevice1;
        public StaticOperatePanel staticOperatePanel1;
        public DynamicOperatePanel dynamicOperatePanel1;
    }
}
