﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace LkControls.Utils
{
    internal class Histogram
    {
        // 计算直方图
        public static int[] CalculateHistogram(ushort[] image)
        {
            int[] histogram = new int[65536];  // 对应16位图像的灰度级数（0-65535）

            foreach (var pixel in image)
            {
                histogram[pixel]++;
            }

            return histogram;
        }

        // 根据直方图计算窗宽窗位
        public static (double windowLevel, double windowWidth, double maxGrayValue, double minGrayValue) CalculateWindowLevelAndWidth(Mat rawMat)
        {
            // 计算直方图
            int[] histSize = { 65536 }; // 16位=65536个灰度级
            Rangef[] ranges = { new Rangef(0, 65536) };
            using var hist = new Mat();
            Cv2.CalcHist(new[] { rawMat }, new[] { 0 }, null, hist, 1, histSize, ranges);

            // 计算累积直方图
            double totalPixels = rawMat.Rows * rawMat.Cols;
            double sumPixels = 0;
            double lowerThreshold = totalPixels * 0.03; // 2.5%
            double upperThreshold = totalPixels * 0.9; // 97.5%

            int minGrayValue = 0;
            int maxGrayValue = 65535;

            // 找到包含95%像素的灰度值范围
            for (int i = 0; i < hist.Rows; i++)
            {
                sumPixels += hist.Get<float>(i);
                if (sumPixels >= lowerThreshold && minGrayValue == 0)
                {
                    minGrayValue = i;
                }
                if (sumPixels >= upperThreshold)
                {
                    maxGrayValue = i;
                    break;
                }
            }

            // 计算窗宽和窗位
            double windowWidth = maxGrayValue - minGrayValue;
            double windowLevel = minGrayValue + (windowWidth / 2);

            if (windowWidth <= 0) {
                windowWidth = 1;
            }

            return (windowLevel, windowWidth, maxGrayValue, minGrayValue);
        }

        public static (double windowLevel, double windowWidth) cal_win_level(Mat rawMat)
        {
            ushort[] upixels = new ushort[rawMat.Rows * rawMat.Cols];
            rawMat.GetArray(out upixels);

            int img_h = rawMat.Rows;
            int img_w = rawMat.Cols;

            int[] array = new int[65536];
            for (int i = 0; i < array.Length; i++)
            {
                array[i] = 0;
            }
            int num = 0;
            for (int j = img_h / 10; j < img_h * 9 / 10; j++)
            {
                for (int k = img_w / 10; k < img_w * 9 / 10; k++)
                {
                    ushort num2 = upixels[j * img_w + k];
                    array[(int)num2]++;
                    num++;
                }
            }
            int num3 = (int)((double)num * 0.05);
            int num4 = 0;
            long num5;
            for (num5 = 32768L; num5 >= 0L; num5 -= 1L)
            {
                num4 += array[(int)(checked((IntPtr)num5))];
                bool flag = num4 > num3 * 2;
                if (flag)
                {
                    break;
                }
            }
            num4 = 0;
            long num6;
            for (num6 = 0L; num6 < (long)array.Length; num6 += 1L)
            {
                num4 += array[(int)(checked((IntPtr)num6))];
                bool flag2 = (double)num4 > (double)num3 * 0.05;
                if (flag2)
                {
                    break;
                }
            }
            num5 -= 100L;
            double windowLevel = (int)((num6 + num5) / 2L);
            double windowWidth = (int)(num5 - num6) * 2;

            return (windowLevel, windowWidth);
        }


    }
}
