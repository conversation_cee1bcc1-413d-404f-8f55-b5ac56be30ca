using System;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.Windows.Forms;
using License;

namespace InduVision.LicenseRegistration
{
    /// <summary>
    /// 时间验证器 - 防止系统时间篡改（离线环境优化）
    /// </summary>
    internal class TimeValidator
    {
        private static readonly NLog.Logger Log = NLog.LogManager.GetLogger("TimeValidator");
        private static DateTime? _lastValidTime = null;
        private static readonly string TimeRecordFile = "Config/time_record.dat";
        private static readonly string TimeAnchorFile = "Config/time_anchor.dat";
        private static readonly string LicenseTimeFile = "Config/license_time.dat";

        /// <summary>
        /// 验证当前系统时间是否可信（多重锚点机制）
        /// </summary>
        public static bool ValidateSystemTime()
        {
            try
            {
                DateTime currentTime = DateTime.Now;

                // 1. 检查时间是否合理（不能是明显错误的时间）
                if (currentTime.Year < 2020 || currentTime.Year > 2050)
                {
                    Log.Warn("系统时间异常：年份超出合理范围 {0}", currentTime.Year);
                    return false;
                }

                // 2. 检查与多个时间锚点的关系
                if (!ValidateTimeAnchors(currentTime))
                {
                    Log.Warn("时间锚点验证失败");
                    return false;
                }

                // 3. 检查与上次记录时间的关系
                if (CheckTimeJump(currentTime))
                {
                    Log.Warn("检测到系统时间跳跃");
                    return false;
                }

                // 4. 检查与文件系统时间的一致性
                if (!CheckFileSystemTimeConsistency(currentTime))
                {
                    Log.Warn("系统时间与文件系统时间不一致");
                    return false;
                }

                // 5. 检查授权文件时间锚点
                if (!ValidateLicenseTimeAnchor(currentTime))
                {
                    Log.Warn("授权文件时间锚点验证失败");
                    return false;
                }

                // 更新所有时间记录
                UpdateTimeRecords(currentTime);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "时间验证失败");
                return false;
            }
        }

        /// <summary>
        /// 检查时间跳跃
        /// </summary>
        private static bool CheckTimeJump(DateTime currentTime)
        {
            try
            {
                DateTime? lastTime = ReadLastTimeRecord();
                if (lastTime.HasValue)
                {
                    // 如果当前时间比上次记录时间早超过1小时，认为是时间回退
                    if (currentTime < lastTime.Value.AddHours(-1))
                    {
                        Log.Warn("检测到时间回退：当前时间 {0}，上次记录 {1}", currentTime, lastTime.Value);
                        return true;
                    }

                    // 如果时间跳跃超过24小时，也认为异常
                    if (currentTime > lastTime.Value.AddHours(24))
                    {
                        Log.Warn("检测到时间大幅跳跃：当前时间 {0}，上次记录 {1}", currentTime, lastTime.Value);
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false; // 读取失败时不认为是跳跃
            }
        }

        /// <summary>
        /// 检查文件系统时间一致性
        /// </summary>
        private static bool CheckFileSystemTimeConsistency(DateTime currentTime)
        {
            try
            {
                // 创建临时文件检查文件系统时间
                string tempFile = Path.GetTempFileName();
                File.WriteAllText(tempFile, "time_check");
                DateTime fileTime = File.GetCreationTime(tempFile);
                File.Delete(tempFile);

                // 允许5分钟的误差
                TimeSpan diff = Math.Abs((currentTime - fileTime).TotalMinutes) > 5 ? currentTime - fileTime : TimeSpan.Zero;
                if (diff.TotalMinutes > 5)
                {
                    Log.Warn("系统时间与文件时间差异过大：{0}分钟", diff.TotalMinutes);
                    return false;
                }
                return true;
            }
            catch
            {
                return true; // 检查失败时不认为是不一致
            }
        }

        /// <summary>
        /// 读取上次时间记录
        /// </summary>
        private static DateTime? ReadLastTimeRecord()
        {
            try
            {
                if (File.Exists(TimeRecordFile))
                {
                    string timeStr = File.ReadAllText(TimeRecordFile);
                    if (DateTime.TryParse(timeStr, out DateTime lastTime))
                    {
                        return lastTime;
                    }
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 验证时间锚点（多重文件时间戳，支持自动恢复）
        /// </summary>
        private static bool ValidateTimeAnchors(DateTime currentTime)
        {
            try
            {
                // 创建多个隐藏的时间锚点文件
                string[] anchorFiles = {
                    Path.Combine(Path.GetTempPath(), ".time_anchor1"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), ".time_anchor2"),
                    TimeAnchorFile
                };

                int validAnchors = 0;
                int missingAnchors = 0;
                DateTime? earliestAnchor = null;
                DateTime? latestAnchor = null;

                foreach (string anchorFile in anchorFiles)
                {
                    try
                    {
                        if (File.Exists(anchorFile))
                        {
                            DateTime anchorTime = File.GetLastWriteTime(anchorFile);

                            // 如果当前时间比锚点时间早超过1小时，认为时间被回退
                            if (currentTime < anchorTime.AddHours(-1))
                            {
                                Log.Warn("检测到时间回退，锚点文件: {0}, 锚点时间: {1}, 当前时间: {2}",
                                    Path.GetFileName(anchorFile), anchorTime, currentTime);
                                return false;
                            }

                            if (!earliestAnchor.HasValue || anchorTime < earliestAnchor.Value)
                            {
                                earliestAnchor = anchorTime;
                            }
                            if (!latestAnchor.HasValue || anchorTime > latestAnchor.Value)
                            {
                                latestAnchor = anchorTime;
                            }
                            validAnchors++;
                        }
                        else
                        {
                            missingAnchors++;
                            Log.Info("锚点文件缺失: {0}", Path.GetFileName(anchorFile));
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Warn(ex, "读取锚点文件失败: {0}", anchorFile);
                        missingAnchors++;
                    }
                }

                // 处理锚点文件缺失的情况
                if (missingAnchors > 0)
                {
                    Log.Info("检测到 {0} 个锚点文件缺失，{1} 个有效", missingAnchors, validAnchors);

                    // 如果所有锚点都缺失，使用降级验证
                    if (validAnchors == 0)
                    {
                        return HandleAllAnchorsMissing(currentTime);
                    }

                    // 如果部分锚点缺失，尝试从现有锚点恢复
                    RecoverMissingAnchors(anchorFiles, latestAnchor ?? currentTime);
                }

                // 如果有有效锚点，检查时间进展的合理性
                if (validAnchors > 0 && earliestAnchor.HasValue)
                {
                    // 时间不能倒退超过1小时
                    if (currentTime < earliestAnchor.Value.AddHours(-1))
                    {
                        Log.Error("时间验证失败：当前时间早于最早锚点时间超过1小时");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "时间锚点验证异常");
                return true; // 验证异常时不阻止，但记录日志
            }
        }

        /// <summary>
        /// 处理所有锚点文件都缺失的情况
        /// </summary>
        private static bool HandleAllAnchorsMissing(DateTime currentTime)
        {
            try
            {
                Log.Warn("所有时间锚点文件都缺失，启用降级验证模式");

                // 检查授权文件的时间锚点是否存在
                if (File.Exists(LicenseTimeFile))
                {
                    string timeData = File.ReadAllText(LicenseTimeFile);
                    string[] parts = timeData.Split('|');

                    if (parts.Length >= 1 && DateTime.TryParse(parts[0], out DateTime licenseCreateTime))
                    {
                        // 当前时间不能早于授权创建时间
                        if (currentTime < licenseCreateTime)
                        {
                            Log.Error("降级验证失败：当前时间早于授权创建时间");
                            return false;
                        }

                        Log.Info("降级验证通过，基于授权创建时间: {0}", licenseCreateTime);

                        // 重新创建锚点文件
                        UpdateTimeAnchors(currentTime);
                        return true;
                    }
                }

                // 如果连授权时间锚点也没有，检查授权文件本身的时间戳
                if (File.Exists("Config/license.license"))
                {
                    DateTime licenseFileTime = File.GetCreationTime("Config/license.license");
                    if (currentTime < licenseFileTime.AddHours(-1))
                    {
                        Log.Error("降级验证失败：当前时间早于授权文件创建时间");
                        return false;
                    }

                    Log.Info("降级验证通过，基于授权文件时间戳: {0}", licenseFileTime);

                    // 重新创建所有锚点文件
                    UpdateTimeAnchors(currentTime);
                    InitializeLicenseTimeAnchor();
                    return true;
                }

                // 最后的降级：允许通过但记录警告
                Log.Warn("无法找到任何时间参考，允许通过但标记为高风险");
                UpdateTimeAnchors(currentTime);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "降级验证处理失败");
                return true; // 异常时允许通过
            }
        }

        /// <summary>
        /// 恢复缺失的锚点文件
        /// </summary>
        private static void RecoverMissingAnchors(string[] anchorFiles, DateTime referenceTime)
        {
            try
            {
                Log.Info("开始恢复缺失的锚点文件，参考时间: {0}", referenceTime);

                foreach (string anchorFile in anchorFiles)
                {
                    if (!File.Exists(anchorFile))
                    {
                        try
                        {
                            string directory = Path.GetDirectoryName(anchorFile);
                            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                            {
                                Directory.CreateDirectory(directory);
                            }

                            File.WriteAllText(anchorFile, referenceTime.ToString("O"));
                            File.SetAttributes(anchorFile, FileAttributes.Hidden);

                            Log.Info("已恢复锚点文件: {0}", Path.GetFileName(anchorFile));
                        }
                        catch (Exception ex)
                        {
                            Log.Warn(ex, "恢复锚点文件失败: {0}", anchorFile);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "恢复锚点文件过程失败");
            }
        }

        /// <summary>
        /// 验证授权文件时间锚点
        /// </summary>
        private static bool ValidateLicenseTimeAnchor(DateTime currentTime)
        {
            try
            {
                if (File.Exists(LicenseTimeFile))
                {
                    string timeData = File.ReadAllText(LicenseTimeFile);
                    string[] parts = timeData.Split('|');

                    if (parts.Length >= 2)
                    {
                        if (DateTime.TryParse(parts[0], out DateTime licenseCreateTime) &&
                            DateTime.TryParse(parts[1], out DateTime lastAccessTime))
                        {
                            // 当前时间不能早于授权创建时间
                            if (currentTime < licenseCreateTime)
                            {
                                Log.Warn("当前时间早于授权创建时间");
                                return false;
                            }

                            // 检查与上次访问时间的关系
                            if (currentTime < lastAccessTime.AddHours(-1))
                            {
                                Log.Warn("检测到时间回退，上次访问: {0}", lastAccessTime);
                                return false;
                            }
                        }
                    }
                }
                return true;
            }
            catch
            {
                return true;
            }
        }

        /// <summary>
        /// 更新所有时间记录
        /// </summary>
        private static void UpdateTimeRecords(DateTime currentTime)
        {
            try
            {
                string directory = Path.GetDirectoryName(TimeRecordFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 更新主时间记录
                File.WriteAllText(TimeRecordFile, currentTime.ToString("O"));

                // 更新时间锚点文件
                UpdateTimeAnchors(currentTime);

                // 更新授权时间记录
                UpdateLicenseTimeAnchor(currentTime);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "更新时间记录失败");
            }
        }

        /// <summary>
        /// 更新时间锚点文件
        /// </summary>
        private static void UpdateTimeAnchors(DateTime currentTime)
        {
            string[] anchorFiles = {
                Path.Combine(Path.GetTempPath(), ".time_anchor1"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), ".time_anchor2"),
                TimeAnchorFile
            };

            foreach (string anchorFile in anchorFiles)
            {
                try
                {
                    string directory = Path.GetDirectoryName(anchorFile);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    File.WriteAllText(anchorFile, currentTime.ToString("O"));
                    // 设置为隐藏文件
                    File.SetAttributes(anchorFile, FileAttributes.Hidden);
                }
                catch
                {
                    // 单个锚点更新失败不影响整体
                }
            }
        }

        /// <summary>
        /// 更新授权时间锚点
        /// </summary>
        private static void UpdateLicenseTimeAnchor(DateTime currentTime)
        {
            try
            {
                string licenseCreateTime = currentTime.ToString("O");
                string lastAccessTime = currentTime.ToString("O");

                // 如果文件已存在，保留创建时间，只更新访问时间
                if (File.Exists(LicenseTimeFile))
                {
                    string existingData = File.ReadAllText(LicenseTimeFile);
                    string[] parts = existingData.Split('|');
                    if (parts.Length >= 1)
                    {
                        licenseCreateTime = parts[0]; // 保留原创建时间
                    }
                }

                string timeData = $"{licenseCreateTime}|{lastAccessTime}";
                File.WriteAllText(LicenseTimeFile, timeData);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "更新授权时间锚点失败");
            }
        }

        /// <summary>
        /// 初始化授权时间锚点（仅在首次授权成功时调用）
        /// </summary>
        public static void InitializeLicenseTimeAnchor()
        {
            try
            {
                DateTime currentTime = DateTime.Now;

                // 强制创建新的授权时间锚点
                string timeData = $"{currentTime:O}|{currentTime:O}";

                string directory = Path.GetDirectoryName(LicenseTimeFile);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(LicenseTimeFile, timeData);
                Log.Info("授权时间锚点已初始化: {0}", currentTime);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化授权时间锚点失败");
            }
        }
    }

    /// <summary>
    /// 注册信息管理器（封装License.dll API）
    /// </summary>
    public class LicenseManager
    {
        public static readonly string LicenseFileName = "Config/license.license";

        private static readonly NLog.Logger Log = NLog.LogManager.GetLogger("LicenseManager");

        public LicenseManager()
        {
            Reload();
        }

        /// <summary>
        /// 许可证状态枚举
        /// </summary>
        public enum Status
        {
            /// <summary>
            /// 当前注册成功
            /// </summary>
            Success = -1,

            /// <summary>
            /// 没有注册, 应该是没有注册文件, 或者非法的授权文件
            /// </summary>
            UnRegistry = 1,

            /// <summary>
            /// 硬件被更换了, 或者注册文件中的硬件序列号不匹配当前设备
            /// </summary>
            DiffHardware, 

            /// <summary>
            /// 注册文件过期
            /// </summary>
            RegistryExpire,
        }

        /// <summary>
        /// 是否已经授权
        /// </summary>
        public bool Licensed => State == Status.Success;

        /// <summary>
        /// 注册状态
        /// </summary>
        public Status State { private set; get; } = Status.UnRegistry;

        /// <summary>
        /// 获取设备硬件序列号
        /// </summary>
        public string HardwareID => License.Status.GetHardwareID(true, true, true, false);

        /// <summary>
        /// 获取授权信息中的失效时间
        /// </summary>
        public DateTime ExpireDate => License.Status.Expiration_Date;

        /// <summary>
        /// 获取授权信息中的创建时间
        /// </summary>
        public DateTime CreateDate { private set; get; }

        /// <summary>
        /// 获取授权信息中的创建人员
        /// </summary>
        public string CreateAuthor { private set; get; }

        /// <summary>
        /// 授权中提供的超级管理员账号默认初始密码
        /// </summary>
        public string AdminPassword { private set; get; } = "123456";

        /// <summary>
        /// 保存授权信息中的设备序列号
        /// </summary>
        public Dictionary<string, string> EquipmentSerial { private set; get; }

        /// <summary>
        /// 注册信息中的授权模块列表
        /// </summary>
        public Dictionary<string, string> Modules { private set; get; }

        /// <summary>
        /// 返回当前注册中是否有给予设备序列号
        /// </summary>
        /// <param name="model">设备型号</param>
        /// <param name="no">序列号</param>
        /// <returns>是否授权</returns>
        public bool GrantEquipment(string model, string no)
        {
            if (EquipmentSerial == null) return false;
            return EquipmentSerial.ContainsKey(model) && string.Equals(EquipmentSerial[model], no);
        }

        /// <summary>
        /// 重新加载许可证
        /// </summary>
        public void Reload()
        {
            Reload(LicenseFileName);
        }

        /// <summary>
        /// 重新加载当前的授权信息
        /// </summary>
        /// <param name="path">许可证文件路径</param>
        public void Reload(string path)
        {
            // 调试模式下可以跳过授权检查
#if DEBUG
            State = Status.Success;
            return;
#endif
            
            State = Status.UnRegistry;

            if (!File.Exists(path))
                return;

            try
            {
                // 使用License.dll API加载授权文件
                License.Status.LoadLicense(path);

                Log.Info("====检测本地授权文件是否有效====");
                Log.Info("授权文件路径 : {0}", path);
                Log.Info("授权文件硬件码 : {0}", License.Status.License_HardwareID);
                Log.Info("本机硬件码 : {0}", HardwareID);
                Log.Info("授权是否有效 : {0}", License.Status.Licensed);
                Log.Info("硬件是否不匹配 : {0}", !CompareHardwareIDs());
                Log.Info("是否启用硬件锁 : {0}", License.Status.Hardware_Lock_Enabled);
                Log.Info("是否启用过期时间锁 : {0}", License.Status.Expiration_Date_Lock_Enable);
                Log.Info("授权文件是否已过期 : {0}", IsExpired());
                Log.Info("授权过期日期 : {0}", License.Status.Expiration_Date);
                Log.Info("当前系统日期 : {0}", DateTime.Now);

                // 检查是否过期
                if (IsExpired())
                {
                    State = Status.RegistryExpire;
                    return;
                }

                // 检查硬件是否匹配
                if (!CompareHardwareIDs())
                {
                    State = Status.DiffHardware;
                    return;
                }

                // 检查授权状态
                if (!License.Status.Licensed)
                {
                    State = !CompareHardwareIDs() ? Status.DiffHardware : 
                           IsExpired() ? Status.RegistryExpire : Status.UnRegistry;
                    return;
                }
                
                // 提取注册文件中的附加信息
                var keyValueList = License.Status.KeyValueList;
                if (keyValueList != null)
                {
                    Log.Info("附加信息存在！");
                    
                    // 获取创建人员信息
                    if (keyValueList.ContainsKey("create_author"))
                    {
                        Log.Info("CreateAuthor存在！");
                        CreateAuthor = keyValueList["create_author"] as string;
                        Log.Info("CreateAuthor : {0}", CreateAuthor);
                    }

                    // 获取注册的创建时间
                    if (keyValueList.ContainsKey("create_date"))
                    {
                        Log.Info("create_date存在！");
                        string dateStr = keyValueList["create_date"] as string;
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            CreateDate = DateTime.Parse(dateStr);
                            Log.Info("create_date : {0}", CreateDate);
                        }
                    }

                    // 加载设备序列号
                    if (keyValueList.ContainsKey("eqp_serial"))
                    {
                        Log.Info("eqp_serial存在！");
                        string json = keyValueList["eqp_serial"] as string;
                        Log.Info("eqp_serial : {0}", json);
                        if (!string.IsNullOrEmpty(json))
                        {
                            EquipmentSerial = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                        }
                    }
                }

                // 注册成功
                State = Status.Success;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "加载许可证文件失败");
                State = Status.UnRegistry;
                MessageBox.Show("加载许可证文件失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据注册码重新加载许可证
        /// </summary>
        /// <param name="registrationCode">注册码（Base64格式）</param>
        public void ReloadLicense(string registrationCode)
        {
            State = Status.UnRegistry;

            if (string.IsNullOrEmpty(registrationCode))
                return;

            try
            {
                // 将注册码转换为字节数组
                byte[] licenseData = Convert.FromBase64String(registrationCode);

                // 使用License.dll API加载授权数据
                License.Status.LoadLicense(licenseData);

                Log.Info("====进入手动注册逻辑====");
                Log.Info("授权文件硬件码 : {0}", License.Status.License_HardwareID);
                Log.Info("本机硬件码 : {0}", HardwareID);
                Log.Info("授权是否有效 : {0}", License.Status.Licensed);
                Log.Info("硬件是否不匹配 : {0}", !CompareHardwareIDs());
                Log.Info("是否启用硬件锁 : {0}", License.Status.Hardware_Lock_Enabled);
                Log.Info("是否启用过期时间锁 : {0}", License.Status.Expiration_Date_Lock_Enable);
                Log.Info("授权文件是否已过期 : {0}", IsExpired());
                Log.Info("授权过期日期 : {0}", License.Status.Expiration_Date);
                Log.Info("当前系统日期 : {0}", DateTime.Now);

                // 检查是否过期
                if (IsExpired())
                {
                    State = Status.RegistryExpire;
                    return;
                }

                // 检查硬件是否匹配
                if (!CompareHardwareIDs())
                {
                    State = Status.DiffHardware;
                    return;
                }

                // 检查授权状态
                if (!License.Status.Licensed)
                {
                    State = !CompareHardwareIDs() ? Status.DiffHardware :
                           IsExpired() ? Status.RegistryExpire : Status.UnRegistry;
                    return;
                }

                // 提取附加信息
                ExtractKeyValueInfo();

                // 授权成功后才保存文件
                State = Status.Success;
                SaveLicenseFile(licenseData);

                // 初始化授权时间锚点
                TimeValidator.InitializeLicenseTimeAnchor();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "加载注册码失败");
                State = Status.UnRegistry;
                MessageBox.Show("加载注册码失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 提取授权文件中的附加信息
        /// </summary>
        private void ExtractKeyValueInfo()
        {
            try
            {
                var keyValueList = License.Status.KeyValueList;
                if (keyValueList != null)
                {
                    // 获取创建人员信息
                    if (keyValueList.ContainsKey("create_author"))
                    {
                        CreateAuthor = keyValueList["create_author"] as string;
                    }

                    // 获取注册的创建时间
                    if (keyValueList.ContainsKey("create_date"))
                    {
                        string dateStr = keyValueList["create_date"] as string;
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            CreateDate = DateTime.Parse(dateStr);
                        }
                    }

                    // 加载设备序列号
                    if (keyValueList.ContainsKey("eqp_serial"))
                    {
                        string json = keyValueList["eqp_serial"] as string;
                        if (!string.IsNullOrEmpty(json))
                        {
                            EquipmentSerial = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "提取授权信息失败");
            }
        }

        /// <summary>
        /// 保存授权文件（原子性操作）
        /// </summary>
        private void SaveLicenseFile(byte[] licenseData)
        {
            if (licenseData == null || licenseData.Length == 0)
            {
                Log.Warn("授权数据为空，跳过保存");
                return;
            }

            try
            {
                // 确保目录存在
                string directory = Path.GetDirectoryName(LicenseFileName);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 使用临时文件实现原子性写入
                string tempFile = LicenseFileName + ".tmp";

                // 写入临时文件
                File.WriteAllBytes(tempFile, licenseData);

                // 验证写入的文件
                if (File.Exists(tempFile) && new FileInfo(tempFile).Length == licenseData.Length)
                {
                    // 原子性替换
                    if (File.Exists(LicenseFileName))
                    {
                        File.Delete(LicenseFileName);
                    }
                    File.Move(tempFile, LicenseFileName);
                    Log.Info("授权文件保存成功");
                }
                else
                {
                    Log.Error("临时文件验证失败");
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "保存授权文件失败");
                // 清理临时文件
                try
                {
                    string tempFile = LicenseFileName + ".tmp";
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }
                catch { }
            }
        }

        /// <summary>
        /// 检查授权是否已过期（包含时间验证）
        /// </summary>
        private bool IsExpired()
        {
            if (!License.Status.Expiration_Date_Lock_Enable)
                return false;

            // 首先验证系统时间是否可信
            if (!TimeValidator.ValidateSystemTime())
            {
                Log.Warn("系统时间验证失败，拒绝授权");
                return true; // 时间不可信时认为已过期
            }

            return License.Status.Expiration_Date < DateTime.Now;
        }

        /// <summary>
        /// 比较硬件ID是否匹配（强制验证）
        /// </summary>
        private bool CompareHardwareIDs()
        {
            try
            {
                // 强制进行硬件验证，不依赖授权文件中的硬件锁配置
                string currentHardwareID = License.Status.HardwareID;
                string licenseHardwareID = License.Status.License_HardwareID;

                Log.Info("硬件验证 - 当前硬件ID: {0}", currentHardwareID);
                Log.Info("硬件验证 - 授权硬件ID: {0}", licenseHardwareID);

                // 如果授权文件中没有硬件ID，则拒绝
                if (string.IsNullOrEmpty(licenseHardwareID))
                {
                    Log.Warn("授权文件中缺少硬件ID");
                    return false;
                }

                // 严格比较硬件ID
                bool isMatch = string.Equals(currentHardwareID, licenseHardwareID, StringComparison.OrdinalIgnoreCase);

                if (!isMatch)
                {
                    Log.Warn("硬件ID不匹配");
                }

                return isMatch;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "硬件ID验证失败");
                return false;
            }
        }
    }
} 