using System;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.Windows.Forms;
using License;

namespace InduVision.LicenseRegistration
{
    /// <summary>
    /// 注册信息管理器（封装License.dll API）
    /// </summary>
    public class LicenseManager
    {
        public static readonly string LicenseFileName = "license.license";

        private static readonly NLog.Logger Log = NLog.LogManager.GetLogger("LicenseManager");

        public LicenseManager()
        {
            Reload();
        }

        /// <summary>
        /// 许可证状态枚举
        /// </summary>
        public enum Status
        {
            /// <summary>
            /// 当前注册成功
            /// </summary>
            Success = -1,

            /// <summary>
            /// 没有注册, 应该是没有注册文件, 或者非法的授权文件
            /// </summary>
            UnRegistry = 1,

            /// <summary>
            /// 硬件被更换了, 或者注册文件中的硬件序列号不匹配当前设备
            /// </summary>
            DiffHardware, 

            /// <summary>
            /// 注册文件过期
            /// </summary>
            RegistryExpire,
        }

        /// <summary>
        /// 是否已经授权
        /// </summary>
        public bool Licensed => State == Status.Success;

        /// <summary>
        /// 注册状态
        /// </summary>
        public Status State { private set; get; } = Status.UnRegistry;

        /// <summary>
        /// 获取设备硬件序列号
        /// </summary>
        public string HardwareID => License.Status.GetHardwareID(true, true, true, false);

        /// <summary>
        /// 获取授权信息中的失效时间
        /// </summary>
        public DateTime ExpireDate => License.Status.Expiration_Date;

        /// <summary>
        /// 获取授权信息中的创建时间
        /// </summary>
        public DateTime CreateDate { private set; get; }

        /// <summary>
        /// 获取授权信息中的创建人员
        /// </summary>
        public string CreateAuthor { private set; get; }

        /// <summary>
        /// 授权中提供的超级管理员账号默认初始密码
        /// </summary>
        public string AdminPassword { private set; get; } = "123456";

        /// <summary>
        /// 保存授权信息中的设备序列号
        /// </summary>
        public Dictionary<string, string> EquipmentSerial { private set; get; }

        /// <summary>
        /// 注册信息中的授权模块列表
        /// </summary>
        public Dictionary<string, string> Modules { private set; get; }

        /// <summary>
        /// 返回当前注册中是否有给予设备序列号
        /// </summary>
        /// <param name="model">设备型号</param>
        /// <param name="no">序列号</param>
        /// <returns>是否授权</returns>
        public bool GrantEquipment(string model, string no)
        {
            if (EquipmentSerial == null) return false;
            return EquipmentSerial.ContainsKey(model) && string.Equals(EquipmentSerial[model], no);
        }

        /// <summary>
        /// 重新加载许可证
        /// </summary>
        public void Reload()
        {
            Reload(LicenseFileName);
        }

        /// <summary>
        /// 重新加载当前的授权信息
        /// </summary>
        /// <param name="path">许可证文件路径</param>
        public void Reload(string path)
        {
            // 调试模式下可以跳过授权检查
#if DEBUG
            State = Status.Success;
            return;
#endif
            
            State = Status.UnRegistry;

            if (!File.Exists(path))
                return;

            try
            {
                // 使用License.dll API加载授权文件
                License.Status.LoadLicense(path);

                Log.Info("====检测本地授权文件是否有效====");
                Log.Info("授权文件路径 : {0}", path);
                Log.Info("授权文件硬件码 : {0}", License.Status.License_HardwareID);
                Log.Info("本机硬件码 : {0}", HardwareID);
                Log.Info("授权是否有效 : {0}", License.Status.Licensed);
                Log.Info("硬件是否不匹配 : {0}", !CompareHardwareIDs());
                Log.Info("是否启用硬件锁 : {0}", License.Status.Hardware_Lock_Enabled);
                Log.Info("是否启用过期时间锁 : {0}", License.Status.Expiration_Date_Lock_Enable);
                Log.Info("授权文件是否已过期 : {0}", IsExpired());
                Log.Info("授权过期日期 : {0}", License.Status.Expiration_Date);
                Log.Info("当前系统日期 : {0}", DateTime.Now);

                // 检查是否过期
                if (IsExpired())
                {
                    State = Status.RegistryExpire;
                    return;
                }

                // 检查硬件是否匹配
                if (!CompareHardwareIDs())
                {
                    State = Status.DiffHardware;
                    return;
                }

                // 检查授权状态
                if (!License.Status.Licensed)
                {
                    State = !CompareHardwareIDs() ? Status.DiffHardware : 
                           IsExpired() ? Status.RegistryExpire : Status.UnRegistry;
                    return;
                }
                
                // 提取注册文件中的附加信息
                var keyValueList = License.Status.KeyValueList;
                if (keyValueList != null)
                {
                    Log.Info("附加信息存在！");
                    
                    // 获取创建人员信息
                    if (keyValueList.ContainsKey("create_author"))
                    {
                        Log.Info("CreateAuthor存在！");
                        CreateAuthor = keyValueList["create_author"] as string;
                        Log.Info("CreateAuthor : {0}", CreateAuthor);
                    }

                    // 获取注册的创建时间
                    if (keyValueList.ContainsKey("create_date"))
                    {
                        Log.Info("create_date存在！");
                        string dateStr = keyValueList["create_date"] as string;
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            CreateDate = DateTime.Parse(dateStr);
                            Log.Info("create_date : {0}", CreateDate);
                        }
                    }

                    // 加载设备序列号
                    if (keyValueList.ContainsKey("eqp_serial"))
                    {
                        Log.Info("eqp_serial存在！");
                        string json = keyValueList["eqp_serial"] as string;
                        Log.Info("eqp_serial : {0}", json);
                        if (!string.IsNullOrEmpty(json))
                        {
                            EquipmentSerial = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                        }
                    }
                }

                // 注册成功
                State = Status.Success;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "加载许可证文件失败");
                State = Status.UnRegistry;
                MessageBox.Show("加载许可证文件失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据注册码重新加载许可证
        /// </summary>
        /// <param name="registrationCode">注册码（Base64格式）</param>
        public void ReloadLicense(string registrationCode)
        {
            State = Status.UnRegistry;

            if (string.IsNullOrEmpty(registrationCode))
                return;

            try
            {
                // 将注册码转换为字节数组
                byte[] licenseData = Convert.FromBase64String(registrationCode);

                // 使用License.dll API加载授权数据
                License.Status.LoadLicense(licenseData);

                Log.Info("====进入手动注册逻辑====");
                Log.Info("授权文件硬件码 : {0}", License.Status.License_HardwareID);
                Log.Info("本机硬件码 : {0}", HardwareID);
                Log.Info("授权是否有效 : {0}", License.Status.Licensed);
                Log.Info("硬件是否不匹配 : {0}", !CompareHardwareIDs());
                Log.Info("是否启用硬件锁 : {0}", License.Status.Hardware_Lock_Enabled);
                Log.Info("是否启用过期时间锁 : {0}", License.Status.Expiration_Date_Lock_Enable);
                Log.Info("授权文件是否已过期 : {0}", IsExpired());
                Log.Info("授权过期日期 : {0}", License.Status.Expiration_Date);
                Log.Info("当前系统日期 : {0}", DateTime.Now);

                // 检查是否过期
                if (IsExpired())
                {
                    State = Status.RegistryExpire;
                    return;
                }

                // 检查硬件是否匹配
                if (!CompareHardwareIDs())
                {
                    State = Status.DiffHardware;
                    return;
                }

                // 检查授权状态
                if (!License.Status.Licensed)
                {
                    State = !CompareHardwareIDs() ? Status.DiffHardware :
                           IsExpired() ? Status.RegistryExpire : Status.UnRegistry;
                    return;
                }

                // 提取附加信息
                ExtractKeyValueInfo();

                // 授权成功后才保存文件
                State = Status.Success;
                SaveLicenseFile(licenseData);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "加载注册码失败");
                State = Status.UnRegistry;
                MessageBox.Show("加载注册码失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 提取授权文件中的附加信息
        /// </summary>
        private void ExtractKeyValueInfo()
        {
            try
            {
                var keyValueList = License.Status.KeyValueList;
                if (keyValueList != null)
                {
                    // 获取创建人员信息
                    if (keyValueList.ContainsKey("create_author"))
                    {
                        CreateAuthor = keyValueList["create_author"] as string;
                    }

                    // 获取注册的创建时间
                    if (keyValueList.ContainsKey("create_date"))
                    {
                        string dateStr = keyValueList["create_date"] as string;
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            CreateDate = DateTime.Parse(dateStr);
                        }
                    }

                    // 加载设备序列号
                    if (keyValueList.ContainsKey("eqp_serial"))
                    {
                        string json = keyValueList["eqp_serial"] as string;
                        if (!string.IsNullOrEmpty(json))
                        {
                            EquipmentSerial = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "提取授权信息失败");
            }
        }

        /// <summary>
        /// 保存授权文件（原子性操作）
        /// </summary>
        private void SaveLicenseFile(byte[] licenseData)
        {
            if (licenseData == null || licenseData.Length == 0)
            {
                Log.Warn("授权数据为空，跳过保存");
                return;
            }
            try
            {
                // 确保目录存在
                string directory = Path.GetDirectoryName(LicenseFileName);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 使用临时文件实现原子性写入
                string tempFile = LicenseFileName + ".tmp";

                // 写入临时文件
                File.WriteAllBytes(tempFile, licenseData);

                // 验证写入的文件
                if (File.Exists(tempFile) && new FileInfo(tempFile).Length == licenseData.Length)
                {
                    // 原子性替换
                    if (File.Exists(LicenseFileName))
                    {
                        File.Delete(LicenseFileName);
                    }
                    File.Move(tempFile, LicenseFileName);
                    Log.Info("授权文件保存成功");
                }
                else
                {
                    Log.Error("临时文件验证失败");
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "保存授权文件失败");
                // 清理临时文件
                try
                {
                    string tempFile = LicenseFileName + ".tmp";
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }
                catch { }
            }
        }

        /// <summary>
        /// 检查授权是否已过期
        /// </summary>
        private bool IsExpired()
        {
            return License.Status.Expiration_Date_Lock_Enable && License.Status.Expiration_Date < DateTime.Now;
        }

        /// <summary>
        /// 比较硬件ID是否匹配
        /// </summary>
        private bool CompareHardwareIDs()
        {
            // 如果未启用硬件锁，则视为匹配
            if (!License.Status.Hardware_Lock_Enabled)
                return true;

            return License.Status.HardwareID == License.Status.License_HardwareID;
        }
    }
} 