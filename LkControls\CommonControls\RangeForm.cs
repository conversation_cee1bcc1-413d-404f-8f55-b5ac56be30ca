﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.DirectoryServices;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace InduVision.RecordDialogs
{
    public partial class RangeForm : Form
    {
        public string mm = String.Empty;

        public RangeForm(string pixel)
        {
            InitializeComponent();

            label1.Text = pixel;
            label1.AutoSize = true;

            textBox1.KeyDown += TextBox1_KeyDown;
        }

        private void TextBox1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                mm = textBox1.Text;

                if (float.TryParse(textBox1.Text, out float _mm))
                {
                    textBox1.Text = Math.Max(0, _mm).ToString();
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show(
                            "请输入有效的整数或小数",                // 错误信息
                            "操作异常",                  // 弹窗标题
                            MessageBoxButtons.OK,    // 按钮类型：只有一个“确定”按钮
                            MessageBoxIcon.Information    // 弹窗图标：错误图标
                        );
                }
            }
            
        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            mm = textBox1.Text;

            if (float.TryParse(textBox1.Text, out float _mm))
            {
                textBox1.Text = Math.Max(0, _mm).ToString();
                DialogResult = DialogResult.OK;
                Close();
            }
            else {
                MessageBox.Show(
                        "请输入有效的整数或小数",                // 错误信息
                        "操作异常",                  // 弹窗标题
                        MessageBoxButtons.OK,    // 按钮类型：只有一个“确定”按钮
                        MessageBoxIcon.Information    // 弹窗图标：错误图标
                    );
            }
        }
    }
}
