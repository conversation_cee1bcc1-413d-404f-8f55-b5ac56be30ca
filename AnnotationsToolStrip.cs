﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

using DemosCommonCode.Imaging;

#if !REMOVE_ANNOTATION_PLUGIN
using Vintasoft.Imaging.Annotation.Dicom;
using Vintasoft.Imaging.Annotation.UI;
using Vintasoft.Imaging.Annotation.UI.VisualTools; 
#endif
using Vintasoft.Imaging.UI.VisualTools;
using Vintasoft.Imaging.UI;
using System.Diagnostics;
using LkControls.LayoutControls;
using Vintasoft.Imaging.Annotation;
using Vintasoft.Imaging.Dicom.UI.VisualTools;
using Vintasoft.Imaging.Codecs.ImageFiles.Dicom;
using Vintasoft.Imaging.Annotation.Dicom.UI;
using Newtonsoft.Json;
using System.Collections.Generic;
using InduVision.LkControls.CommonControls;
using Vintasoft.Imaging.ImageColors;
using Vintasoft.Imaging.Annotation.Dicom.UI.VisualTools;
using LkControls.common;
using LkControls.Utils;
using OpenCvSharp;
using InduVision.RecordDialogs;
using System.IO;
using Vintasoft.Imaging.Annotation.Measurements;
using static OpenCvSharp.LineIterator;
using InduVision;
using InduVision.LkControls.Utils;
using DicomViewerDemo.RecordDialogs;
using System.Linq;
using Vintasoft.Imaging;
using static System.Net.Mime.MediaTypeNames;
using System.Runtime.InteropServices;
using FellowOakDicom;
using System.Reflection;
using OxyPlot.Annotations;
using System.Drawing.Drawing2D;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Vintasoft.Imaging.Drawing;


namespace DicomViewerDemo
{
    /// <summary>
    /// Toolstrip with DICOM annotations.
    /// </summary>
    public partial class AnnotationsToolStrip : ToolStrip
    {

        #region Fields

        public bool isCancel = false;

        public bool applyDuplexIQI = false;

        public DuplexIQIForm duplexIQIForm = null;

        public static ushort[,] DuplexIQIPixelData;

        ToolStripButton _buildingAnnotationButton;

        const string SEPARATOR = "SEPARATOR";
        const string PointButtonName = "Point";
        const string CircleButtonName = "Circle";
        const string PolylineButtonName = "Polyline";
        const string InterpolatedButtonName = "Interpolated";

        const string EllipseButtonName = "Ellipse";
        const string MultilineButtonName = "Multiline";
        const string RangelineButtonName = "Rangeline";
        const string InifinitelineButtonName = "Infiniteline";
        const string CutlineButtonName = "Cutline";
        const string ArrowButtonName = "Arrow";
        const string RectangleButtonName = "Rectangle";
        const string AxisButtonName = "Axis";
        const string RulerButtonName = "Ruler";
        const string CrosshairButtonName = "Crosshair";

        const string TextButtonName = "Text";

        const string NsnrButtonName = "Nsnr";

        const string RegionButtonName = "Region";

        const string ScalingButtonName = "Scaling";

        const string DuplexIQIButtonName = "DuplexIQI";

        const string WallThicknessButtonName = "WallThickness";

        WallAnnotationData uniQueWallAnnotationData;

        string[] AnnotationNames = {
            PointButtonName,
            CircleButtonName,
            PolylineButtonName,
            InterpolatedButtonName,
            SEPARATOR,

            RectangleButtonName,
            EllipseButtonName,
            MultilineButtonName,
            RangelineButtonName,
            InifinitelineButtonName,
            CutlineButtonName,
            ArrowButtonName,
            AxisButtonName,
            RulerButtonName,
            CrosshairButtonName,
            SEPARATOR,

            TextButtonName,
            NsnrButtonName,
            RegionButtonName,
            DuplexIQIButtonName,
            WallThicknessButtonName,
        };

#if !REMOVE_ANNOTATION_PLUGIN
        AnnotationVisualTool _annotationTool;
#endif

        #endregion



        #region Constructors

        /// <summary>
        /// Initializes a new instance of the <see cref="AnnotationsToolStrip"/> class.
        /// </summary>
        public AnnotationsToolStrip()
            : base()
        {
            ComponentResourceManager resources = new ComponentResourceManager(typeof(AnnotationsToolStrip));

            for (int i = 0; i < AnnotationNames.Length; i++)
            {
                string name = AnnotationNames[i];
                if (name == SEPARATOR)
                {
                    Items.Add(new ToolStripSeparator());
                }
                else
                {
                    ToolStripButton button = new ToolStripButton(name);
                    button.ImageTransparentColor = Color.Magenta;
                    button.ToolTipText = name;
                    button.Click += new EventHandler(buildAnnotationButton_Click);
                    button.Image = (System.Drawing.Image)(resources.GetObject(name));
                    button.DisplayStyle = ToolStripItemDisplayStyle.Image;
                    button.ImageScaling = ToolStripItemImageScaling.None;
                    Items.Add(button);
                }
            }

            Viewer = null;
        }

        #endregion



        #region Properties

        public AnnotationVisualTool AnnotationVisualTool{
            get {
                return _annotationTool;
            }
        }

        ImageViewer _viewer = null;
        /// <summary>
        /// Gets or sets the <see cref="AnnotationViewer"/> associated with
        /// this <see cref="AnnotationsToolStrip"/>.
        /// </summary>        
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public ImageViewer Viewer
        {
            get
            {
                return _viewer;
            }
            set
            {
#if !REMOVE_ANNOTATION_PLUGIN
                if (_annotationTool != null)
                {
                    _annotationTool.AnnotationBuildingFinished -= viewer_AnnotationBuildingFinished;
                    _annotationTool.AnnotationBuildingCanceled -= viewer_AnnotationBuildingCanceled;
                    _annotationTool.AnnotationTransformingFinished -= viewer_AnnotationTransformingFinished;
                    _annotationTool.AnnotationTransformingStarted -= _annotationTool_AnnotationTransformingStarted;
                }

                _annotationTool = null;
                _viewer = value;
                if (_viewer != null)
                    _annotationTool = GetAnnotationVisualTool(_viewer.VisualTool);

                if (_annotationTool != null)
                {
                    _annotationTool.AnnotationBuildingFinished += new EventHandler<AnnotationViewEventArgs>(viewer_AnnotationBuildingFinished);
                    _annotationTool.AnnotationBuildingCanceled += new EventHandler<AnnotationViewEventArgs>(viewer_AnnotationBuildingCanceled);
                    _annotationTool.AnnotationTransformingFinished += new EventHandler<AnnotationViewEventArgs>(viewer_AnnotationTransformingFinished);
                    _annotationTool.FocusedAnnotationViewChanged += _annotationTool_FocusedAnnotationViewChanged;
                    _annotationTool.AnnotationTransformingStarted += _annotationTool_AnnotationTransformingStarted;
                } 
#endif
            }
        }


        private void _annotationTool_FocusedAnnotationViewChanged(object sender, AnnotationViewChangedEventArgs e)
        {
            DicomAnnotationTool dicomAnnotationTool = (DicomAnnotationTool)sender;
            if (dicomAnnotationTool.FocusedAnnotationData != null) {
                if (dicomAnnotationTool.FocusedAnnotationData.GetType() == typeof(NsnrAnnotationData))
                {
                    DicomRectangleAnnotationView dicomRectangleAnnotationView = (DicomRectangleAnnotationView)dicomAnnotationTool.FocusedAnnotationView;

                    RectangleF boundingBox = dicomAnnotationTool.FocusedAnnotationView.GetBoundingBox();

                    Rectangle boundingBoxInt = new Rectangle(
                        (int)boundingBox.Left,
                        (int)boundingBox.Top,
                        (int)boundingBox.Width,
                        (int)boundingBox.Height);

                    if ((boundingBoxInt.Left > 0 && boundingBoxInt.Right < _viewer.Image.Width) && (boundingBoxInt.Top > 0 && boundingBoxInt.Bottom < _viewer.Image.Height))
                    {
                        long sum = 0;
                        long sumOfSquares = 0;
                        int pixelCount = 0;
                        int fpdPixel = 100;

                        ushort minVal = ushort.MaxValue, maxVal = ushort.MinValue;

                        for (int y = boundingBoxInt.Top; y < boundingBoxInt.Bottom; y++)
                        {
                            for (int x = boundingBoxInt.Left; x <= boundingBoxInt.Right; x++)
                            {
                                ushort gray = MainForm.RawMat.At<ushort>(y, x);

                                sum += gray;
                                sumOfSquares += gray * gray;
                                pixelCount++;
                                minVal = Math.Min(minVal, gray);
                                maxVal = Math.Max(maxVal, gray);
                            }
                        }

                        double mean = (double)sum / pixelCount;
                        double variance = ((double)sumOfSquares / pixelCount) - (mean * mean);
                        double stdDev = Math.Sqrt(variance);

                        // Assuming signal-to-noise ratio (SNR) calculation, SNR = mean / stdDev (avoid division by zero)
                        double snr = stdDev != 0 ? mean / stdDev : double.PositiveInfinity;
                        double nsnr = snr * 88.6 / fpdPixel;

                        mainForm.nsnrStripStatusLabel1.Text = string.Concat("测量宽度:", boundingBox.Width.ToString("0"), " 测量高度:", boundingBox.Height.ToString("0"), " 平均值:", mean.ToString("0"), " 标准偏差: ", stdDev.ToString("0"), " 信噪比: ", snr.ToString("0"), " 归一化信噪比: ", nsnr.ToString("0"));
                        mainForm.separator1.Visible = true;
                    }
                }
                else if (dicomAnnotationTool.FocusedAnnotationData.GetType() == typeof(WallAnnotationData))
                {
                    
                }
                else
                {
                    mainForm.nsnrStripStatusLabel1.Text = "";
                    mainForm.separator1.Visible = false;
                }
            }
        }

        MainForm mainForm = null;

        public MainForm MainForm
        {
            set { 
                mainForm = value;
            }
        }

        #endregion



        #region Methods

#if !REMOVE_ANNOTATION_PLUGIN
        /// <summary>
        /// Returns an annotation object by the annotation type name.
        /// </summary>
        protected virtual AnnotationView GetAnnotation(string annotationName)
        {
            DicomAnnotationData data = null;

            switch (annotationName)
            {
                case PointButtonName:
                    data = new DicomPointAnnotationData();
                    break;

                case CircleButtonName:
                    data = new DicomCircleAnnotationData();
                    break;

                case PolylineButtonName:
                    data = new DicomPolylineAnnotationData();
                    break;

                case InterpolatedButtonName:
                    data = new DicomPolylineAnnotationData();
                    ((DicomPolylineAnnotationData)data).UseInterpolation = true;
                    break;

                case EllipseButtonName:
                    data = new DicomEllipseAnnotationData();
                    break;

                case MultilineButtonName:
                    data = new DicomMultilineAnnotationData();
                    break;

                case RangelineButtonName:
                    data = new DicomRangeLineAnnotationData();
                    break;

                case InifinitelineButtonName:
                    data = new DicomInfiniteLineAnnotationData();
                    break;

                case CutlineButtonName:
                    data = new DicomCutLineAnnotationData();
                    break;

                case ArrowButtonName:
                    data = new DicomArrowAnnotationData();
                    break;

                case RectangleButtonName:
                    data = new DicomRectangleAnnotationData();
                    break;

                case AxisButtonName:
                    data = new DicomAxisAnnotationData();
                    break;

                case RulerButtonName:
                    data = new DicomRulerAnnotationData();
                    break;

                case CrosshairButtonName:
                    data = new DicomCrosshairAnnotationData();
                    break;

                case TextButtonName:
                    data = new DicomTextAnnotationData();
                    ((DicomTextAnnotationData)data).UnformattedTextValue = "Text";
                    break;

                case NsnrButtonName:
                    data = new NsnrAnnotationData();
                    ((NsnrAnnotationData)data).LineStyle.PatternOnColor = new LabColor(50, 17, -70);
                    break;

                case RegionButtonName:
                    data = new RegionAnnotationData();
                    ((RegionAnnotationData)data).LineStyle.PatternOnColor = new LabColor(50, 17, -70);
                    break;
                case ScalingButtonName:
                    data = new ScalingAnnotationData();
                    break;
                case DuplexIQIButtonName:
                    data = new DuplexIQIAnnotationData();
                    ((DuplexIQIAnnotationData)data).LineStyle.LineThickness = MainForm.DUPLEXIQI_WIDTH;
                    ((DuplexIQIAnnotationData)data).LineStyle.PatternOnOpacity = 0.2f;
                    ((DuplexIQIAnnotationData)data).Border = true;
                    break;
                case WallThicknessButtonName:
                    if (uniQueWallAnnotationData != null) {
                        mainForm.getDicomAnnotatedViewerTool().DicomAnnotationTool.AnnotationDataCollection.Remove(uniQueWallAnnotationData);
                    }

                    data = new WallAnnotationData();
                    uniQueWallAnnotationData = (WallAnnotationData)data;


                    break;
            }

            return AnnotationViewFactory.CreateView(data);
        } 
#endif

        /// <summary>
        /// The annotation building is started.
        /// </summary>
        private void buildAnnotationButton_Click(object sender, EventArgs e)
        {
#if !REMOVE_ANNOTATION_PLUGIN
            ToolStripButton annotationButton = (ToolStripButton)sender;
            annotationButton.Checked = true;

            if (_annotationTool.FocusedAnnotationView != null &&
                _annotationTool.FocusedAnnotationView.InteractionController ==
                _annotationTool.FocusedAnnotationView.Builder)
                _annotationTool.CancelAnnotationBuilding();

            if (_buildingAnnotationButton != null)
                _buildingAnnotationButton.Checked = false;

            if (annotationButton == _buildingAnnotationButton)
            {
                _buildingAnnotationButton = null;
            }
            else
            {
                if (_annotationTool.AnnotationInteractionMode != AnnotationInteractionMode.Author)
                    _annotationTool.AnnotationInteractionMode = AnnotationInteractionMode.Author;

                AnnotationView annotationView = BuildAnnotation(annotationButton.ToolTipText);

                if (annotationView != null)
                    _buildingAnnotationButton = annotationButton;
                else
                    _buildingAnnotationButton = null;
            } 
#endif
        }

#if !REMOVE_ANNOTATION_PLUGIN
        /// <summary>
        /// Adds an annotation to an image and starts building of annotation.
        /// </summary>
        public AnnotationView BuildAnnotation(string annotationName)
        {
            if (Viewer == null || Viewer.Image == null)
                return null;

            AnnotationView annotationView = null;
            try
            {
                annotationView = GetAnnotation(annotationName);

                if (annotationView != null)
                {
                    if (annotationView is DicomRulerAnnotationView) {
                        ((DicomRulerAnnotationView)annotationView).TextSize = 32;
                        ((DicomRulerAnnotationView)annotationView).LineStyle.LineThickness = 1;
                    }

                    _annotationTool.AddAndBuildAnnotation(annotationView);
                }
            }
            catch (InvalidOperationException ex)
            {
                MessageBox.Show(ex.Message, InduVision.Localization.Strings.DICOMVIEWERDEMO_BUILDING_ANNOTATION, MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }

            return annotationView;
        }

        private void _annotationTool_AnnotationTransformingStarted(object sender, AnnotationViewEventArgs e)
        {
            DicomAnnotationTool dicomAnnotationTool = (DicomAnnotationTool)sender;

            AnnotationData focusedAnnotation = dicomAnnotationTool.FocusedAnnotationData;

            if (focusedAnnotation.GetType() == typeof(WallAnnotationData))
            {
                AnnotationDataCollection annotations = mainForm.getDicomAnnotatedViewerTool().DicomAnnotationTool.AnnotationDataController.GetAnnotations(mainForm.imageViewer1.FocusedIndex);
                if (annotations != null && annotations.Count > 1)
                {
                    for (int i = annotations.Count - 1; i > 0; i--)
                    {
                        AnnotationData annotationData = (AnnotationData)annotations[i];
                        if (annotationData.Intent == "WallThickness")
                        {
                            annotations.RemoveAt(i);
                        }
                    }
                }
            }

            
        }

        private void viewer_AnnotationTransformingFinished(object sender, AnnotationViewEventArgs e)
        {
            DicomAnnotationTool dicomAnnotationTool = (DicomAnnotationTool)sender;

            AnnotationData focusedAnnotation = dicomAnnotationTool.FocusedAnnotationData;
            if (focusedAnnotation.GetType() == typeof(NsnrAnnotationData))
            {


                DicomRectangleAnnotationView dicomRectangleAnnotationView = (DicomRectangleAnnotationView)e.AnnotationView;

                RectangleF boundingBox = e.AnnotationView.GetBoundingBox();

                Rectangle boundingBoxInt = new Rectangle(
                    (int)boundingBox.Left,
                    (int)boundingBox.Top,
                    (int)boundingBox.Width,
                    (int)boundingBox.Height);

                if ((boundingBoxInt.Left > 0 && boundingBoxInt.Right < _viewer.Image.Width) && (boundingBoxInt.Top > 0 && boundingBoxInt.Bottom < _viewer.Image.Height))
                {
                    long sum = 0;
                    long sumOfSquares = 0;
                    int pixelCount = 0;
                    int fpdPixel = 100;


                    for (int y = boundingBoxInt.Top; y < boundingBoxInt.Bottom; y++)
                    {
                        for (int x = boundingBoxInt.Left; x <= boundingBoxInt.Right; x++)
                        {
                            ushort gray = MainForm.RawMat.At<ushort>(y, x);

                            sum += gray;
                            sumOfSquares += gray * gray;
                            pixelCount++;
                        }
                    }

                    double mean = (double)sum / pixelCount;
                    double variance = ((double)sumOfSquares / pixelCount) - (mean * mean);
                    double stdDev = Math.Sqrt(variance);

                    // Assuming signal-to-noise ratio (SNR) calculation, SNR = mean / stdDev (avoid division by zero)
                    double snr = stdDev != 0 ? mean / stdDev : double.PositiveInfinity;
                    double nsnr = snr * 88.6 / fpdPixel;

                    mainForm.nsnrStripStatusLabel1.Text = string.Concat("测量宽度:", boundingBox.Width.ToString("0"), " 测量高度:", boundingBox.Height.ToString("0"), " 平均值:", mean.ToString("0"), " 标准偏差: ", stdDev.ToString("0"), " 信噪比: ", snr.ToString("0"), " 归一化信噪比: ", nsnr.ToString("0"));
                    mainForm.separator1.Visible = true;
                }
            }
            else if (focusedAnnotation.GetType() == typeof(DuplexIQIAnnotationData))
            {
                if (duplexIQIForm != null && _viewer.Image != null)
                {
                    DuplexIQIAnnotationData duplexIQIAnnotationData = (DuplexIQIAnnotationData)focusedAnnotation;

                    PointF[] linePoint = duplexIQIAnnotationData.GetLinePointsInImageSpace();

                    float left = linePoint[0].X;
                    float top = linePoint[0].Y;
                    float right = linePoint[1].X;
                    float bottom = linePoint[1].Y;

                    if (left < 0)
                    {
                        left = 0;
                    }

                    if (right > _viewer.Image.Width)
                    {
                        right = _viewer.Image.Width;
                    }

                    if (top < 0)
                    {
                        top = 0;
                    }

                    if (bottom > _viewer.Image.Height)
                    {
                        bottom = _viewer.Image.Height;
                    }

                    // 起点和终点
                    var start = ((int)left, (int)top);
                    var end = ((int)right, (int)bottom);

                    PointF[] points = duplexIQIAnnotationData.GetLinePointsInImageSpace();

                    if (points != null && points.Length >= 2)
                    {
                        if (points[0].X > points[1].X && points[0].Y > points[1].Y)
                        {
                            start = ((int)left, (int)top);
                            end = ((int)right, (int)bottom);
                        }
                        else if (points[0].X < points[1].X && points[0].Y > points[1].Y)
                        {
                            start = ((int)right, (int)top);
                            end = ((int)left, (int)bottom);
                        }
                        else if (points[0].X < points[1].X && points[0].Y < points[1].Y)
                        {
                            start = ((int)right, (int)bottom);
                            end = ((int)left, (int)top);
                        }
                        else if (points[0].X > points[1].X && points[0].Y < points[1].Y)
                        {
                            start = ((int)left, (int)bottom);
                            end = ((int)right, (int)top);
                        }
                    }

                    int distance = (int)duplexIQIAnnotationData.LineStyle.LineThickness / 2;

                    double[] grayArray = Utils.getDuplexIQIGrayArray(MainForm.RawMat, start, end, distance);

                    if (grayArray != null && grayArray.Length > 0)
                    {
                        duplexIQIForm.reCalculate(grayArray);
                    }

                }
            }
            else if (focusedAnnotation.GetType() == typeof(WallAnnotationData))
            {
                WallAnnotationData wallAnnotationData = (WallAnnotationData)focusedAnnotation;

                

                mainForm.getDicomAnnotatedViewerTool().DicomAnnotationTool.AnnotationDataCollection.Changed -= AnnotationDataCollection_Changed;
                mainForm.getDicomAnnotatedViewerTool().DicomAnnotationTool.AnnotationDataCollection.Changed += AnnotationDataCollection_Changed;

                PointF[] linePoint = wallAnnotationData.GetLinePointsInImageSpace();

                DicomPolylineAnnotationData data = new DicomPolylineAnnotationData();
                data.CanSelect = false;
                data.UseInterpolation = true;
                data.LineStyle.PatternOffColor = new LabColor(90, -11, 87);
                data.LineStyle.PatternOnColor = new LabColor(90, -11, 87);
                data.Intent = "WallThickness";

                System.Drawing.Point start = new System.Drawing.Point((int)linePoint[0].X, (int)linePoint[0].Y);
                System.Drawing.Point end = new System.Drawing.Point((int)linePoint[1].X, (int)linePoint[1].Y);

                float dx = end.X - start.X;
                float dy = end.Y - start.Y;
                float length = (float)Math.Sqrt(dx * dx + dy * dy);
                float dirX = dx / length;
                float dirY = dy / length;

                // 法向量
                float normalX = -dirY;
                float normalY = dirX;

                var linePoints = Utils.BresenhamLine(start.X, start.Y, end.X, end.Y);
                double[] grayValues = Utils.getLineGrayArray(MainForm.ChangedRawMat, linePoints);

                // 5. 计算曲线坐标点（显示在直线上方 N 像素）
                float offsetNormal = -100; // 向上偏移20像素
                float scaleY = 0.01f; // 灰度值缩放系数（根据需求调整）

                // 法线方向偏移
                float offsetX = normalX * offsetNormal;
                float offsetY = normalY * offsetNormal;

                float startX = 0;
                float startY = 0;
                float endX = 0;
                float endY = 0;
                float rulerStartX = 0;
                float rulerStartY = 0;
                float rulerEndX = 0;
                float rulerEndY = 0;

                double threshold1 = 5000; // 边缘阈值
                double threshold2 = 10; // 边缘阈值
                int smoothWindowSize = 3; // 平滑窗口大小

                // 平滑处理
                double[] grays = Smooth(grayValues.Select(x => (double)x).ToArray(), smoothWindowSize);

                //for (int i = 0; i < grayValues.Length; i++)
                //{
                //    double v = grayValues[i];
                //    Debug.WriteLine(v);
                //}

                // 左侧查找
                (int leftEdge, int rightEdge) = FindLeftRegion(grays, threshold1, threshold2);
                if (leftEdge >= 0 && rightEdge >= 0 && rightEdge > leftEdge)
                {
                }
                else
                {
                    (int rightLeftEdge, int rightRightEdge) = FindRightRegion(grays, threshold1, threshold2);
                    if (rightLeftEdge >= 0 && rightRightEdge >= 0 && rightRightEdge > rightLeftEdge)
                    {
                        leftEdge = rightLeftEdge;
                        rightEdge = rightRightEdge;
                    }
                }

                for (int i = 0; i < linePoints.Count; i++)
                {
                    float x = linePoints[i].Item1;
                    float y = linePoints[i].Item2;

                    // 灰度值沿法线偏移
                    float grayOffsetX = normalX * (float)(grayValues[i] * scaleY);
                    float grayOffsetY = normalY * (float)(grayValues[i] * scaleY);

                    float finalX = x + offsetX - grayOffsetX;
                    float finalY = y + offsetY - grayOffsetY;

                    if (i == 0)
                    {
                        startX = finalX;
                        startY = finalY;
                    }
                    else if (i == linePoints.Count - 1)
                    {
                        endX = finalX;
                        endY = finalY;
                    }

                    if (i == leftEdge)
                    {
                        rulerStartX = x + offsetX * 0.5f;
                        rulerStartY = y + offsetY * 0.5f;
                    }

                    if (i == rightEdge)
                    {
                        rulerEndX = x + offsetX * 0.5f;
                        rulerEndY = y + offsetY * 0.5f;
                    }

                    PointF currentPoint = new PointF(finalX, finalY);

                    data.Points.Add(currentPoint);
                }

                DicomRangeLineAnnotationData dicomRangeLineAnnotationData1 = new DicomRangeLineAnnotationData();
                dicomRangeLineAnnotationData1.CanSelect = false;
                dicomRangeLineAnnotationData1.LineStyle.PatternOffColor = new LabColor(90, -11, 87);
                dicomRangeLineAnnotationData1.LineStyle.PatternOnColor = new LabColor(90, -11, 87);
                dicomRangeLineAnnotationData1.Points.Add(new PointF(start.X, start.Y));
                dicomRangeLineAnnotationData1.Points.Add(new PointF(startX, startY));
                dicomRangeLineAnnotationData1.Intent = "WallThickness";

                DicomRangeLineAnnotationData dicomRangeLineAnnotationData2 = new DicomRangeLineAnnotationData();
                dicomRangeLineAnnotationData2.CanSelect = false;
                dicomRangeLineAnnotationData2.LineStyle.PatternOffColor = new LabColor(90, -11, 87);
                dicomRangeLineAnnotationData2.LineStyle.PatternOnColor = new LabColor(90, -11, 87);
                dicomRangeLineAnnotationData2.Points.Add(new PointF(end.X, end.Y));
                dicomRangeLineAnnotationData2.Points.Add(new PointF(endX, endY));
                dicomRangeLineAnnotationData2.Intent = "WallThickness";

                AnnotationDataCollection annotations = mainForm.getDicomAnnotatedViewerTool().DicomAnnotationTool.AnnotationDataController.GetAnnotations(mainForm.imageViewer1.FocusedIndex);
                if (annotations != null && annotations.Count > 1)
                {
                    for (int i = annotations.Count - 1; i > 0; i--)
                    {
                        AnnotationData annotationData = (AnnotationData)annotations[i];
                        if (annotationData.Intent == "WallThickness")
                        {
                            annotations.RemoveAt(i);
                        }
                    }
                }
                annotations.Add(data);
                annotations.Add(dicomRangeLineAnnotationData1);
                annotations.Add(dicomRangeLineAnnotationData2);

                if (rulerStartX != 0 && rulerEndX != 0)
                {
                    DicomRulerAnnotationData dicomRulerAnnotationData = new DicomRulerAnnotationData();
                    dicomRulerAnnotationData.CanSelect = false;
                    dicomRulerAnnotationData.LineStyle.PatternOffColor = new LabColor(30, 24, -66);
                    dicomRulerAnnotationData.LineStyle.PatternOnColor = new LabColor(30, 24, -66);
                    dicomRulerAnnotationData.Points.Add(new PointF(rulerStartX, rulerStartY));
                    dicomRulerAnnotationData.Points.Add(new PointF(rulerEndX, rulerEndY));
                    dicomRulerAnnotationData.Intent = "WallThickness";
                    annotations.Add(dicomRulerAnnotationData);
                }

                AnnotationViewCollection annotationViews = mainForm.getDicomAnnotatedViewerTool().DicomAnnotationTool.AnnotationViewCollection;
                for (int i = 0; i < annotationViews.Count; i++)
                {
                    AnnotationView annotationView = annotationViews[i];
                    AnnotationData annotationData = annotationView.Data;
                    if (annotationData.Intent == "WallThickness")
                    {
                        if (annotationView is DicomRulerAnnotationView)
                        {
                            ((DicomRulerAnnotationView)annotationView).TextSize = 32;
                        }

                        ((DicomSimpleAnnotationViewBase)annotationView).LineStyle.LineThickness = 1;
                    }
                }
            } 
            else
            {
                mainForm.nsnrStripStatusLabel1.Text = "";
                mainForm.separator1.Visible = false;
            }
        }

        private void AnnotationDataCollection_Changed(object sender, CollectionChangeEventArgs<AnnotationData> e)
        {
            if (e.OldValue is WallAnnotationData && e.Action == CollectionChangeActionType.RemoveItem) {
                uniQueWallAnnotationData = null;

                AnnotationDataCollection annotations = mainForm.getDicomAnnotatedViewerTool().DicomAnnotationTool.AnnotationDataController.GetAnnotations(mainForm.imageViewer1.FocusedIndex);
                if (annotations != null && annotations.Count > 1)
                {
                    for (int i = annotations.Count - 1; i >= 0; i--)
                    {
                        AnnotationData annotationData = annotations[i];
                        if (annotationData.Intent == "WallThickness") {
                            annotations.RemoveAt(i);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 平滑
        /// </summary>
        static double[] Smooth(double[] data, int windowSize)
        {
            double[] result = new double[data.Length];
            int half = windowSize / 2;
            for (int i = 0; i < data.Length; i++)
            {
                int start = Math.Max(0, i - half);
                int end = Math.Min(data.Length - 1, i + half);
                double sum = 0;
                for (int j = start; j <= end; j++) sum += data[j];
                result[i] = sum / (end - start + 1);
            }
            return result;
        }

        /// <summary>
        /// 从左往右：找到下降边缘后，再找到上升边缘
        /// </summary>
        static (int, int) FindLeftRegion(double[] grayValues, double threshold1, double threshold2)
        {
            int leftEdge = -1;
            int rightEdge = -1;

            for (int i = 1; i < grayValues.Length; i++)
            {
                double diff = grayValues[i - 1] - grayValues[i];
                Debug.WriteLine("left:" + grayValues[i - 1] + ":" + grayValues[i] + ":" + diff + ":" + leftEdge + ":" + threshold1);
                if (leftEdge < 0 && diff > threshold1)
                {
                    leftEdge = i;
                }
                else if (leftEdge >= 0)
                {
                    diff = grayValues[i] - grayValues[i - 1];
                    if (diff > threshold2)
                    {
                        rightEdge = i;
                        break;
                    }
                }
            }
            return (leftEdge, rightEdge);
        }

        /// <summary>
        /// 从右往左：找到上升边缘后，再找到下降边缘
        /// </summary>
        static (int, int) FindRightRegion(double[] grayValues, double threshold1, double threshold2)
        {
            int rightEdge = -1;
            int leftEdge = -1;

            for (int i = grayValues.Length - 1; i > 0; i--)
            {
                double diff = grayValues[i] - grayValues[i - 1];
                Debug.WriteLine("right:" + grayValues[i] + ":" + grayValues[i - 1] + ":" + diff + ":" + rightEdge + ":" + threshold1);
                if (rightEdge < 0 && diff > threshold1)
                {
                    rightEdge = i;
                }
                else if (rightEdge >= 0)
                {
                    diff = grayValues[i - 1] - grayValues[i];
                    if (diff > threshold2)
                    {
                        leftEdge = i;
                        break;
                    }
                }
            }
            return (leftEdge, rightEdge);
        }

        /// <summary>
        /// Annotation building is finished.
        /// </summary>
        private void viewer_AnnotationBuildingFinished(object sender, AnnotationViewEventArgs e)
        {
            if (isCancel) {
                isCancel = false;
                return;
            }

            ToolStripButton buildingAnnotationButton = _buildingAnnotationButton;
            if (buildingAnnotationButton != null)
            {
                _buildingAnnotationButton = null;
                buildingAnnotationButton.Checked = false;
            }

            DicomAnnotationTool dicomAnnotationTool = (DicomAnnotationTool)sender;

            if (dicomAnnotationTool.FocusedAnnotationData != null) {
                if (dicomAnnotationTool.FocusedAnnotationData.GetType() == typeof(RegionAnnotationData))
                {
                    AnnotationData focusedAnnotation = dicomAnnotationTool.FocusedAnnotationData;

                    RectangleF boundingBox = focusedAnnotation.GetBoundingBox();

                    if (boundingBox.Width != 0 && boundingBox.Height != 0)
                    {
                        Rectangle boundingBoxInt = new Rectangle(
                        (int)boundingBox.Left,
                        (int)boundingBox.Top,
                        (int)boundingBox.Width,
                        (int)boundingBox.Height);

                        if ((boundingBoxInt.Left > 0 && boundingBoxInt.Right < _viewer.Image.Width) && (boundingBoxInt.Top > 0 && boundingBoxInt.Bottom < _viewer.Image.Height))
                        {
                            ushort min = 0;
                            ushort max = 0;

                            for (int y = boundingBoxInt.Top; y < boundingBoxInt.Bottom; y++)
                            {
                                for (int x = boundingBoxInt.Left; x <= boundingBoxInt.Right; x++)
                                {
                                    MainForm.matLock.EnterReadLock();
                                    ushort gray = MainForm.ChangedRawMat.At<ushort>(y, x);
                                    MainForm.matLock.ExitReadLock();
                                    min = Math.Min(min, gray);
                                    max = Math.Max(max, gray);
                                }
                            }

                            Rect roi = new Rect((int)boundingBox.Left, (int)boundingBox.Top, (int)boundingBox.Width, (int)boundingBox.Height);

                            Mat croppedImg = new Mat(MainForm.ChangedRawMat, roi);

                            //用直方图处理
                            var (windowLevel, windowWidth, maxGrayValue, minGrayValue) = Histogram.CalculateWindowLevelAndWidth(croppedImg);

                            double ww = maxGrayValue - minGrayValue;  // 窗宽
                            double wl = (maxGrayValue + minGrayValue) / 2;  // 窗位

                            if (ww == 0)
                            {
                                ww = 1;
                            }

                            mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(wl, ww);
                        }
                    }

                    if (_viewer != null && _viewer.FocusedIndex >= 0 && _viewer.FocusedIndex < dicomAnnotationTool.AnnotationDataController.Images.Count)
                    {
                        AnnotationDataCollection annotations = dicomAnnotationTool.AnnotationDataController[_viewer.FocusedIndex];
                        annotations.Remove(dicomAnnotationTool.FocusedAnnotationData);
                    }

                    RoundButton roundButton = mainForm.toolsPanel.activedButton;
                    BuildAnnotation("Region");
                }
                else if (dicomAnnotationTool.FocusedAnnotationData.GetType() == typeof(ScalingAnnotationData))
                {
                    AnnotationData focusedAnnotation = dicomAnnotationTool.FocusedAnnotationData;

                    RectangleF boundingBox = focusedAnnotation.GetBoundingBox();

                    DicomRangeLineAnnotationView dicomAxisAnnotationView = (DicomRangeLineAnnotationView)dicomAnnotationTool.FocusedAnnotationView;

                    float width = dicomAxisAnnotationView.Data.Size.Width;
                    float height = dicomAxisAnnotationView.Data.Size.Height;

                    double length = Math.Round(Math.Sqrt(Math.Pow(width, 2) + Math.Pow(height, 2)));

                    if (boundingBox.Width != 0 && boundingBox.Height != 0)
                    {
                        using (RangeForm rangeForm = new RangeForm(length.ToString()))
                        {
                            rangeForm.StartPosition = FormStartPosition.Manual;
                            rangeForm.Location = Cursor.Position;

                            DialogResult dialogResult = rangeForm.ShowDialog();
                            if (dialogResult == DialogResult.OK)
                            {
                                float mm = float.Parse(rangeForm.mm);
                                MainForm.SCALING = (float)(mm / length) * 10f;

                                ImageMeasureToolAction imageMeasureToolAction = mainForm.getDicomAnnotatedViewerToolStrip1().FindAction<ImageMeasureToolAction>();

                                imageMeasureToolAction._visualTool.Calibration = MainForm.SCALING;
                            }

                        }
                    }

                    if (_viewer != null && _viewer.FocusedIndex >= 0 && _viewer.FocusedIndex < dicomAnnotationTool.AnnotationDataController.Images.Count)
                    {
                        AnnotationDataCollection annotations = dicomAnnotationTool.AnnotationDataController[_viewer.FocusedIndex];
                        annotations.Remove(dicomAnnotationTool.FocusedAnnotationData);
                    }
                }
                else if (dicomAnnotationTool.FocusedAnnotationData.GetType() == typeof(DuplexIQIAnnotationData))
                {
                    if (_viewer.Image != null) {
                        DuplexIQIAnnotationData duplexIQIAnnotationData = (DuplexIQIAnnotationData)dicomAnnotationTool.FocusedAnnotationData;

                        RectangleF boundingBox = duplexIQIAnnotationData.GetBoundingBox();

                        float left = boundingBox.Left;
                        float top = boundingBox.Top;
                        float right = boundingBox.Right;
                        float bottom = boundingBox.Bottom;

                        if (left < 0)
                        {
                            left = 0;
                        }

                        if (right > _viewer.Image.Width)
                        {
                            right = _viewer.Image.Width;
                        }

                        if (top < 0)
                        {
                            top = 0;
                        }

                        if (bottom > _viewer.Image.Height)
                        {
                            bottom = _viewer.Image.Height;
                        }

                        // 起点和终点
                        var start = ((int)boundingBox.X, (int)boundingBox.Y);
                        var end = ((int)boundingBox.X, (int)boundingBox.Y);

                        PointF[] points = duplexIQIAnnotationData.GetLinePointsInContentSpace();

                        if (points != null && points.Length >= 2) {
                            if (points[0].X > points[1].X && points[0].Y > points[1].Y)
                            {
                                start = ((int)boundingBox.Left, (int)boundingBox.Top);
                                end = ((int)boundingBox.Right, (int)boundingBox.Bottom);
                            }
                            else if (points[0].X < points[1].X && points[0].Y > points[1].Y)
                            {
                                start = ((int)boundingBox.Right, (int)boundingBox.Top);
                                end = ((int)boundingBox.Left, (int)boundingBox.Bottom);
                            }
                            else if (points[0].X < points[1].X && points[0].Y < points[1].Y)
                            {
                                start = ((int)boundingBox.Right, (int)boundingBox.Bottom);
                                end = ((int)boundingBox.Left, (int)boundingBox.Top);
                            }
                            else if (points[0].X > points[1].X && points[0].Y < points[1].Y)
                            {
                                start = ((int)boundingBox.Left, (int)boundingBox.Bottom);
                                end = ((int)boundingBox.Right, (int)boundingBox.Top);
                            }

                            int distance = (int)duplexIQIAnnotationData.LineStyle.LineThickness / 2;

                            //VintasoftImage vintasoftImage = mainForm.getDicomViewerTool().GetDisplayedImage();
                            //VintasoftImageToPixelData(vintasoftImage);

                            double[] grayArray = Utils.getDuplexIQIGrayArray(MainForm.RawMat, start, end, distance);

                            //for (global::System.Int32 i = 0; i < grayArray.Length; i++)
                            //{
                            //    Debug.Write(grayArray[i] + ",");
                            //}

                            duplexIQIForm = new DuplexIQIForm(grayArray);
                            duplexIQIForm.FormClosed += (sender, e) => {
                                AnnotationDataCollection annotations = dicomAnnotationTool.AnnotationDataController[_viewer.FocusedIndex];
                                annotations.Remove(dicomAnnotationTool.FocusedAnnotationData);

                                if (!applyDuplexIQI)
                                {
                                    mainForm.toolsPanel.splitContainer1.Panel2Collapsed = true;
                                    foreach (Control ctrl in mainForm.toolsPanel.splitContainer1.Panel2.Controls.Cast<Control>().ToList())
                                    {
                                        mainForm.toolsPanel.splitContainer1.Panel2.Controls.Remove(ctrl); // 从 Panel2 中移除
                                        ctrl.Dispose();  // 释放资源
                                    }
                                }
                                else
                                {
                                    applyDuplexIQI = false;
                                }
                                duplexIQIForm.Dispose();
                                duplexIQIForm = null;
                            };
                            duplexIQIForm.TopMost = true;
                            duplexIQIForm.Show();
                        }
                        
                    }
                }
            }

        }

        public void VintasoftImageToPixelData(VintasoftImage image)
        {
            // get the PixelManipulator object
            Vintasoft.Imaging.PixelManipulator pixelManipulator = image.OpenPixelManipulator();
            // set the lock area to full image size
            System.Drawing.Rectangle lockRectangle =
                new System.Drawing.Rectangle(0, 0, image.Width, image.Height);
            // lock pixels for read and write
            pixelManipulator.LockPixels(lockRectangle, Vintasoft.Imaging.BitmapLockMode.ReadWrite);
            // remebmer the stride for performance purposes
            int width = image.Width;
            int height = image.Height;
            DuplexIQIPixelData = new ushort[height, width];

            for (int y = 0; y < height; y++)
            {
                byte[] row = pixelManipulator.ReadRowData(y);
                for (int x = 0; x < width; x++)
                {
                    int byteIndex = x * 2;
                    ushort gray = BitConverter.ToUInt16(row, byteIndex);
                    DuplexIQIPixelData[y, x] = gray;
                }
            }

            // unlock pixels
            pixelManipulator.UnlockPixels();
            // close PixelManipulator and generate the Vintasoft.Imaging.VintasoftImage.Changed event
            image.ClosePixelManipulator(true);
        }

        /// <summary>
        /// Annotation building is canceled.
        /// </summary>
        private void viewer_AnnotationBuildingCanceled(object sender, AnnotationViewEventArgs e)
        {
            ToolStripButton buildingAnnotationButton = _buildingAnnotationButton;
            if (buildingAnnotationButton != null)
            {
                _buildingAnnotationButton = null;
                buildingAnnotationButton.Checked = false;
            }
        }

        private AnnotationVisualTool GetAnnotationVisualTool(VisualTool visualTool)
        {
            if (visualTool is CompositeVisualTool)
            {
                CompositeVisualTool compositeVisualTool = (CompositeVisualTool)visualTool;
                foreach (VisualTool tool in compositeVisualTool)
                {
                    AnnotationVisualTool result = GetAnnotationVisualTool(tool);
                    if (result != null)
                        return result;
                }

                return null;
            }

            return visualTool as AnnotationVisualTool;
        }

        public void cancelBuilding() {
            isCancel = true;
            _annotationTool.CancelAnnotationBuilding();
        }

#endif

        #endregion

    }
}
