﻿using Newtonsoft.Json.Linq;
using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Vintasoft.Imaging.UI;
using Vintasoft.Imaging;
using DicomViewerDemo;
using LkControls.Utils;
using InduVision;
using static System.Net.Mime.MediaTypeNames;
using InduVision.LkControls.FPD.IRay;
using iDetector;
using FellowOakDicom.Imaging;
using System.Security.Policy;
using FellowOakDicom.IO.Buffer;
using FellowOakDicom;
using System.Xml.Linq;

namespace InduVision.LkControls.FPD.IRay
{
    public partial class FPDPanel : IFPDPanel
    {
        
        private int captureType = 0; //采图模式
        private static readonly object imageLock = new object();
        private static bool taskLock = false;
        public static int overlayMode = 0;

        public FPDPanel()
        {
            InitializeComponent();
        }

        public override void setMainForm(MainForm value)
        {
            mainForm = value;
            staticOperatePanel1.MainForm = value;
            dynamicOperatePanel1.MainForm = value;
        }

        public override void DynamicStop() { 
            dynamicOperatePanel1.stop();

            MainForm.matCache.Clear();
        }

        public override void StaticStop()
        {
            staticOperatePanel1.capture();

            MainForm.matCache.Clear();
        }

        public override bool DynamicIsStart()
        {
            return dynamicOperatePanel1.isStarted;
        }

        public override bool DynamicIsCapturing()
        {
            return dynamicOperatePanel1.isCapturing;
        }

        public override bool StaticIsCapturing()
        {
            return staticOperatePanel1.isCapturing;
        }

        public override bool StaticIsPressed()
        {
            return staticOperatePanel1.saveButton.isPressed;
        }

        public override string StaticNameTextBox()
        {
            return staticOperatePanel1.nameTextBox.Text;
        }

        private void ConnectDevice1_ConnectedDevice(JObject jObject)
        {
            mainForm.topPanel1.updateFPDInfo("连接中...", "连接中...");

            if (mainForm.imageViewer1.Image != null && mainForm.imageViewer1.Image.Width < 100)
            {
                mainForm.imageViewer1.VisualTool = mainForm.getDicomAnnotatedViewerTool();
                mainForm.imageViewer1.Images.Remove(mainForm.imageViewer1.Image);
            }

            deviceType = jObject["deviceType"].ToObject<int>();

            if (deviceType == 0)
            {
                StaticFPD.ImageRecv += ImageRecv;

                Task.Run(() =>
                {
                    var (result, message, width, height) = StaticFPD.connect(connectDevice1.pcIpText.Text, int.Parse(connectDevice1.pcPortText.Text), connectDevice1.FPDIpText.Text, int.Parse(connectDevice1.FPDPortText.Text));

                    if (result == SdkInterface.Err_OK)
                    {
                        MainForm.DEVICE_WIDTH = width;
                        MainForm.DEVICE_HEIGHT = height;

                        MainForm.IMAGE_WIDTH = width;
                        MainForm.IMAGE_HEIGHT = height;

                        this.BeginInvoke(new Action<string>(updateImageInfo), "未启用");

                        this.BeginInvoke(new Action<JObject>(connectSuccess), jObject);
                    }
                    else
                    {
                        this.BeginInvoke(new Action<string, string>(updateFPDInfo), "连接失败", message);
                    }
                });
            }
            else if (deviceType == 1) {
                DynamicFPD.ImageRecv += ImageRecv;

                var (result, message, width, height) = DynamicFPD.connect(connectDevice1.pcIpText.Text, int.Parse(connectDevice1.pcPortText.Text), connectDevice1.FPDIpText.Text, int.Parse(connectDevice1.FPDPortText.Text));

                if (result == SdkInterface.Err_OK)
                {
                    MainForm.DEVICE_WIDTH = width;
                    MainForm.DEVICE_HEIGHT = height;

                    MainForm.IMAGE_WIDTH = width;
                    MainForm.IMAGE_HEIGHT = height;

                    mainForm.topPanel1.updateImageInfo(width, height, "未启用");

                    connectSuccess(jObject);
                }
                else
                {
                    mainForm.topPanel1.updateFPDInfo(message, "连接失败");
                }
            }

            
        }

        private void DisconnectedDevice(Newtonsoft.Json.Linq.JObject obj)
        {
            deviceType = 0;
            captureType = 0;

            mainForm.topPanel1.updateFPDInfo("未连接", "未连接");
            mainForm.topPanel1.updateCaptureInfo("未开始");
            mainForm.topPanel1.updateImageInfo(0, 0, "未启用");

            connectDevice1.Visible = true;
            staticOperatePanel1.Visible = false;
            dynamicOperatePanel1.Visible = false;

            StaticFPD.ImageRecv -= ImageRecv;

            MainForm.matCache.Clear();
        }

        private void ImageRecv(short[] rawData)
        {
            Mat rawMat = new Mat(MainForm.DEVICE_HEIGHT, MainForm.DEVICE_WIDTH, MainForm.IMAGE_MATTYPE);

            IntPtr ptr = rawMat.Data;  // 获取 Mat 的数据指针
            Marshal.Copy(rawData, 0, ptr, rawData.Length);

            Mat processedMat = rawMat.IsContinuous() ? rawMat : new Mat(rawMat.Size(), rawMat.Type());
            Mat cacheMat = processedMat.Clone();
            if (!rawMat.IsContinuous())
            {
                rawMat.CopyTo(processedMat);
                cacheMat = processedMat.Clone();
            }

            if (MainForm.isOverlay) {
                if (MainForm.matCache.Count < MainForm.overlayNumber - 1)
                {
                    MainForm.matCache.Add(cacheMat);
                    this.BeginInvoke(new Action<string>(updateImageInfo), string.Format("{0} / {1}", MainForm.matCache.Count, MainForm.overlayNumber));
                }
                else {
                    this.BeginInvoke(new Action<string>(updateImageInfo), "进行合成");
                    if (!FPDPanel.taskLock) {
                        FPDPanel.taskLock = true;

                        if (FPDPanel.overlayMode == 0)
                        {
                            Task task = Task.Run(() =>
                            {
                                // 使用 CV_32FC1 来避免溢出
                                Mat sumMat = new Mat(MainForm.IMAGE_HEIGHT, MainForm.IMAGE_WIDTH, MatType.CV_32FC1, Scalar.All(0));

                                foreach (var frame in MainForm.matCache)
                                {
                                    Mat temp;
                                    frame.ConvertTo(temp = new Mat(), MatType.CV_32FC1); // 转为 float
                                    Cv2.Add(sumMat, temp, sumMat); // 累加
                                    temp.Dispose();
                                }

                                // 平均化（也可以省略这一步仅做总和）
                                sumMat /= MainForm.matCache.Count;

                                // 转回 16 位图像
                                Mat result = new Mat();
                                sumMat.ConvertTo(result, MatType.CV_16UC1); // 可加缩放系数防止溢出
                                sumMat.Dispose();

                                MainForm.RawMat = result;

                                if (!result.IsContinuous())
                                    result = result.Clone(); // 确保数据连续

                                int size = (int)(result.Total() * result.ElemSize());
                                byte[] rawBytes = new byte[size];
                                System.Runtime.InteropServices.Marshal.Copy(result.Data, rawBytes, 0, size);

                                string name = DateTime.Now.ToString("yyyyMMddHHmmss");

                                string rawName = name + "_" + MainForm.overlayNumber + "_combine.dcm";
                                string thumbnailName = name + "_" + MainForm.overlayNumber + "_combine.jpg";
                                string projectPath = Path.Combine(mainForm.WorkDirectory, ".visionnova");
                                string thumbnailPath = Path.Combine(projectPath, "thumbnail");
                                string thumbnailFile = Path.Combine(thumbnailPath, thumbnailName);
                                string destFile = Path.Combine(mainForm.WorkDirectory, rawName);


                                var dataset = new DicomDataset(FellowOakDicom.DicomTransferSyntax.ImplicitVRLittleEndian)
                                {
                                    //{ DicomTag.PatientID, "20240919" },
                                    { DicomTag.StudyDate, DateTime.Now.ToString("yyyyMMdd") },
                                    { DicomTag.StudyTime, DateTime.Now.ToString("HHmmss") },
                                    { DicomTag.SpecificCharacterSet,  "GB18030"},
                                    { DicomTag.StudyInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                                    { DicomTag.SeriesInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                                    { DicomTag.SOPInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                                    { DicomTag.SOPClassUID, DicomUID.SecondaryCaptureImageStorage },
                                    { DicomTag.PhotometricInterpretation, FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2.Value },
                                    { DicomTag.Rows, (ushort)MainForm.IMAGE_HEIGHT },
                                    { DicomTag.Columns, (ushort)MainForm.IMAGE_WIDTH },
                                    { DicomTag.BitsAllocated, (ushort)16 },
                                    { DicomTag.BitsStored, (ushort)16 },
                                    { DicomTag.HighBit, (ushort)15 },
                                    { DicomTag.PixelRepresentation, (ushort)0 }, // 0 = unsigned, 1 = signed
                                    { DicomTag.SamplesPerPixel, (ushort)1 }
                                };

                                // Add pixel data
                                var pixelData = FellowOakDicom.Imaging.DicomPixelData.Create(dataset, true);
                                //pixelData.BitsAllocated = 16;
                                pixelData.BitsStored = 16;
                                pixelData.HighBit = 15;
                                pixelData.PixelRepresentation = FellowOakDicom.Imaging.PixelRepresentation.Unsigned;
                                pixelData.SamplesPerPixel = 1;
                                pixelData.PlanarConfiguration = FellowOakDicom.Imaging.PlanarConfiguration.Interleaved;
                                pixelData.Width = (ushort)MainForm.IMAGE_WIDTH;
                                pixelData.Height = (ushort)MainForm.IMAGE_HEIGHT;
                                pixelData.PhotometricInterpretation = FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2;

                                // Assign image data to DICOM pixel data
                                pixelData.AddFrame(new MemoryByteBuffer(rawBytes));

                                // Save the DICOM file
                                FellowOakDicom.DicomFile dicomFile = new FellowOakDicom.DicomFile(dataset);

                                dicomFile.Save(destFile);

                                //生成缩略图
                                Mat rawMat = new Mat(MainForm.IMAGE_HEIGHT, MainForm.IMAGE_WIDTH, MainForm.IMAGE_MATTYPE);

                                int length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH;

                                if (MainForm.IMAGE_MATTYPE == MatType.CV_16U)
                                {
                                    length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH;
                                }

                                if (mainForm.leftPanel.fpdPanel1.deviceType == 0)
                                {
                                    Marshal.Copy(StaticFPD.imageData, 0, rawMat.Data, length);
                                }
                                else
                                {
                                    Marshal.Copy(DynamicFPD.imageData, 0, rawMat.Data, length);
                                }


                                // 计算新的宽度和高度
                                int newWidth = (int)(MainForm.IMAGE_WIDTH * 0.1);
                                int newHeight = (int)(MainForm.IMAGE_HEIGHT * 0.1);

                                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                                Cv2.ImWrite(thumbnailFile, rawMat);

                                byte[] jpegData;
                                // 将16位灰度图转换为8位灰度图
                                rawMat.ConvertTo(rawMat, MatType.CV_8U, 255.0 / 65535.0); // 缩放因子：65535 -> 255

                                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                                Cv2.ImEncode(".jpg", rawMat, out jpegData);

                                // 保存为文件或其他处理
                                File.WriteAllBytes(thumbnailFile, jpegData);

                                MainForm.matCache.Clear();

                                FPDPanel.taskLock = false;

                                this.BeginInvoke(new Action<string>(updateImageInfo), "合成完成");

                                this.BeginInvoke(new Action<Mat>(showImage), result);
                            });
                        }
                        else if (FPDPanel.overlayMode == 1)
                        {
                            Task task = Task.Run(() =>
                            {
                                Mat result = MatProcesser.VarianceWeightedFusion(MainForm.matCache);

                                if (!result.IsContinuous())
                                    result = result.Clone(); // 确保数据连续

                                int size = (int)(result.Total() * result.ElemSize());
                                byte[] rawBytes = new byte[size];
                                System.Runtime.InteropServices.Marshal.Copy(result.Data, rawBytes, 0, size);

                                string name = DateTime.Now.ToString("yyyyMMddHHmmss");

                                string rawName = name + "_" + MainForm.overlayNumber + "_combine.dcm";
                                string thumbnailName = name + "_" + MainForm.overlayNumber + "_combine.jpg";
                                string projectPath = Path.Combine(mainForm.WorkDirectory, ".visionnova");
                                string thumbnailPath = Path.Combine(projectPath, "thumbnail");
                                string thumbnailFile = Path.Combine(thumbnailPath, thumbnailName);
                                string destFile = Path.Combine(mainForm.WorkDirectory, rawName);


                                var dataset = new DicomDataset(FellowOakDicom.DicomTransferSyntax.ImplicitVRLittleEndian)
                                {
                                    //{ DicomTag.PatientID, "20240919" },
                                    { DicomTag.StudyDate, DateTime.Now.ToString("yyyyMMdd") },
                                    { DicomTag.StudyTime, DateTime.Now.ToString("HHmmss") },
                                    { DicomTag.SpecificCharacterSet,  "GB18030"},
                                    { DicomTag.StudyInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                                    { DicomTag.SeriesInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                                    { DicomTag.SOPInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                                    { DicomTag.SOPClassUID, DicomUID.SecondaryCaptureImageStorage },
                                    { DicomTag.PhotometricInterpretation, FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2.Value },
                                    { DicomTag.Rows, (ushort)MainForm.IMAGE_HEIGHT },
                                    { DicomTag.Columns, (ushort)MainForm.IMAGE_WIDTH },
                                    { DicomTag.BitsAllocated, (ushort)16 },
                                    { DicomTag.BitsStored, (ushort)16 },
                                    { DicomTag.HighBit, (ushort)15 },
                                    { DicomTag.PixelRepresentation, (ushort)0 }, // 0 = unsigned, 1 = signed
                                    { DicomTag.SamplesPerPixel, (ushort)1 }
                                };

                                // Add pixel data
                                var pixelData = FellowOakDicom.Imaging.DicomPixelData.Create(dataset, true);
                                //pixelData.BitsAllocated = 16;
                                pixelData.BitsStored = 16;
                                pixelData.HighBit = 15;
                                pixelData.PixelRepresentation = FellowOakDicom.Imaging.PixelRepresentation.Unsigned;
                                pixelData.SamplesPerPixel = 1;
                                pixelData.PlanarConfiguration = FellowOakDicom.Imaging.PlanarConfiguration.Interleaved;
                                pixelData.Width = (ushort)MainForm.IMAGE_WIDTH;
                                pixelData.Height = (ushort)MainForm.IMAGE_HEIGHT;
                                pixelData.PhotometricInterpretation = FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2;

                                // Assign image data to DICOM pixel data
                                pixelData.AddFrame(new MemoryByteBuffer(rawBytes));

                                // Save the DICOM file
                                FellowOakDicom.DicomFile dicomFile = new FellowOakDicom.DicomFile(dataset);

                                dicomFile.Save(destFile);

                                //生成缩略图
                                Mat rawMat = new Mat(MainForm.IMAGE_HEIGHT, MainForm.IMAGE_WIDTH, MainForm.IMAGE_MATTYPE);

                                int length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH * 2;

                                if (MainForm.IMAGE_MATTYPE == MatType.CV_16U)
                                {
                                    length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH * 2;
                                }

                                if (deviceType == 0)
                                {
                                    Marshal.Copy(StaticFPD.imageData, 0, rawMat.Data, length);
                                }
                                else if (deviceType == 1)
                                {
                                    Marshal.Copy(DynamicFPD.imageData, 0, rawMat.Data, length);
                                }

                                // 计算新的宽度和高度
                                int newWidth = (int)(MainForm.IMAGE_WIDTH * 0.1);
                                int newHeight = (int)(MainForm.IMAGE_HEIGHT * 0.1);

                                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                                byte[] jpegData;
                                // 将16位灰度图转换为8位灰度图
                                rawMat.ConvertTo(rawMat, MatType.CV_8U, 255.0 / 65535.0); // 缩放因子：65535 -> 255

                                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                                Cv2.ImEncode(".jpg", rawMat, out jpegData);

                                // 保存为文件或其他处理
                                File.WriteAllBytes(thumbnailFile, jpegData);

                                MainForm.matCache.Clear();

                                FPDPanel.taskLock = false;

                                this.BeginInvoke(new Action<string>(updateImageInfo), "合成完成");

                                this.BeginInvoke(new Action<Mat>(showImage), result);
                            });
                        }

                    }
                }
            }

            if (mainForm.leftPanel.fpdPanel1.deviceType == 0) {
                //判断是否只显示合成图片
                if (staticOperatePanel1.onlyOverlay.Checked)
                {
                    return;
                }
            }
            else if (mainForm.leftPanel.fpdPanel1.deviceType == 1)
            {
                //判断是否只显示合成图片
                if (dynamicOperatePanel1.onlyOverlay.Checked)
                {
                    return;
                }
            }


            if (MainForm.canRefreshImage == false) { return; }
            //Debug.WriteLine("触发 FPDPanel ImageRecv 事件...");

            showImage(processedMat);
        }

        private void showImage(Mat processedMat) {
            lock (imageLock)
            {
                if (!this.IsDisposed && !this.Disposing && this.IsHandleCreated)
                {
                    this.BeginInvoke(new Action<string>(updateCaptureInfo), "开始生成图像");
                }

                MatProcesser.RotateImage(processedMat);

                if (mainForm.imageViewer1.Image == null || mainForm.imageViewer1.Image.Width < 100 || mainForm.imageViewer1.Image.Width != processedMat.Width)
                {
                    if (mainForm.leftPanel.fpdPanel1.deviceType == 0)
                    {
                        var (windowLevel, windowWidth, maxGrayValue, minGrayValue) = Histogram.CalculateWindowLevelAndWidth(processedMat);
                        mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(windowLevel, windowWidth);
                    }
                    else
                    {
                        mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(32767, 65535);
                    }

                    byte[] imageData1;
                    Cv2.ImEncode(".tiff", processedMat, out imageData1, new int[] { (int)ImwriteFlags.TiffCompression, 1 });
                    MemoryStream memoryStream1 = new MemoryStream(imageData1);
                    Vintasoft.Imaging.VintasoftImage vintasoftImage = new VintasoftImage(memoryStream1, true);

                    // save reference to the previously captured image
                    VintasoftImage oldImage = mainForm.imageViewer1.Image;
                    // show captured image in the preview viewer
                    vintasoftImage.Resolution = new Vintasoft.Imaging.Resolution(250f, 250f);
                    mainForm.imageViewer1.Image = vintasoftImage;

                    if (mainForm.imageViewer1.Zoom > 1000)
                    {
                        mainForm.imageViewer1.Zoom = 32;
                    }
                }
                else
                {
                    Vintasoft.Imaging.VintasoftImage image = mainForm.imageViewer1.Image;

                    Vintasoft.Imaging.PixelManipulator pixelManipulator = image.OpenPixelManipulator();
                    // unlock pixels
                    pixelManipulator.UnlockPixels();
                    // close PixelManipulator and generate the Vintasoft.Imaging.VintasoftImage.Changed event
                    image.ClosePixelManipulator(true);
                    InduVision.LkControls.Utils.Utils.RenderImage(image, processedMat);

                    this.BeginInvoke(new Action(UpdateImage));


                }

                MainForm.matLock.EnterWriteLock();
                if (MainForm.RawMat != null)
                {
                    MainForm.RawMat.Dispose();
                    MainForm.RawMat = null;
                }
                MainForm.matLock.ExitWriteLock();

                MainForm.RawMat = processedMat.Clone();
                MainForm.ChangedRawMat = processedMat.Clone();

                processedMat.Dispose();
                processedMat = null;

                //非动态版采集图像中显示
                if (mainForm.leftPanel.fpdPanel1.deviceType == 0 && MainForm.lockVoiLut == false)
                {
                    var (windowLevel, windowWidth, maxGrayValue, minGrayValue) = Histogram.CalculateWindowLevelAndWidth(processedMat);
                    mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(windowLevel, windowWidth);
                }

                this.BeginInvoke(new Action<string>(updateCaptureInfo), "图像已生成");

                this.BeginInvoke(new Action(openWindow));
            }
        }

        private void openWindow()
        {
            if (staticOperatePanel1.isCapturing)
            {
                staticOperatePanel1.openWindow();
            }
        }

        private void UpdateImage()
        {
            mainForm.getDicomViewerTool().UpdateImage();
        }

        private void updateCaptureInfo(string text) {
            staticOperatePanel1.saveButton.CustomEnabled = true;
            staticOperatePanel1.saveButton.IsPressed = true;
            mainForm.topPanel1.updateCaptureInfo(text);
        }

        private void updateFPDInfo(string phase, string message)
        {
            mainForm.topPanel1.updateFPDInfo(phase, message);
        }

        private void updateImageInfo(string text)
        {
            mainForm.topPanel1.updateImageInfo(MainForm.IMAGE_WIDTH, MainForm.IMAGE_HEIGHT, text);
        }

        private void connectSuccess(JObject jObject)
        {
            deviceType = jObject["deviceType"].ToObject<int>();
            captureType = jObject["captureType"].ToObject<int>();

            switch (deviceType)
            {
                case 0:
                    {
                        switch (captureType)
                        {
                            case 0:
                                {
                                    staticOperatePanel1.manualMode();
                                }
                                break;
                            case 1:
                                {
                                    staticOperatePanel1.halfAutoMode();
                                }
                                break;
                        }
                        staticOperatePanel1.Visible = true;
                    }
                    break;
                case 1:
                    {
                        dynamicOperatePanel1.Visible = true;
                    }
                    break;
            }

            mainForm.topPanel1.updateFPDInfo("已连接", "已连接");
            mainForm.topPanel1.updateImageInfo(MainForm.IMAGE_WIDTH, MainForm.IMAGE_HEIGHT, "未启用");

            connectDevice1.Visible = false;
            
        }

    }
}
