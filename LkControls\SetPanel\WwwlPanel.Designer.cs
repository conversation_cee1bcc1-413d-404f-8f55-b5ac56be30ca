﻿using System.Windows.Forms;

namespace InduVision.LkControls.SetPanel
{
    partial class WwwlPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            groupBox1 = new GroupBox();
            wLTextBox = new NumericUpDown();
            wWTextBox = new NumericUpDown();
            centerMaxLabel = new Label();
            centerMinLabel = new Label();
            widthMaxLabel = new Label();
            widthMinLabel = new Label();
            centerTrackBar = new TrackBar();
            label2 = new Label();
            widthTrackBar = new TrackBar();
            label1 = new Label();
            pictureBox1 = new PictureBox();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)wLTextBox).BeginInit();
            ((System.ComponentModel.ISupportInitialize)wWTextBox).BeginInit();
            ((System.ComponentModel.ISupportInitialize)centerTrackBar).BeginInit();
            ((System.ComponentModel.ISupportInitialize)widthTrackBar).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(pictureBox1);
            groupBox1.Controls.Add(wLTextBox);
            groupBox1.Controls.Add(wWTextBox);
            groupBox1.Controls.Add(centerMaxLabel);
            groupBox1.Controls.Add(centerMinLabel);
            groupBox1.Controls.Add(widthMaxLabel);
            groupBox1.Controls.Add(widthMinLabel);
            groupBox1.Controls.Add(centerTrackBar);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(widthTrackBar);
            groupBox1.Controls.Add(label1);
            groupBox1.ForeColor = System.Drawing.Color.White;
            groupBox1.Location = new System.Drawing.Point(15, 9);
            groupBox1.Margin = new Padding(3, 4, 3, 4);
            groupBox1.Name = "groupBox1";
            groupBox1.Padding = new Padding(3, 4, 3, 4);
            groupBox1.Size = new System.Drawing.Size(343, 502);
            groupBox1.TabIndex = 18;
            groupBox1.TabStop = false;
            groupBox1.Text = "窗宽窗位设置";
            // 
            // wLTextBox
            // 
            wLTextBox.Location = new System.Drawing.Point(86, 365);
            wLTextBox.Maximum = new decimal(new int[] { 65535, 0, 0, 0 });
            wLTextBox.Minimum = new decimal(new int[] { 200, 0, 0, 0 });
            wLTextBox.Name = "wLTextBox";
            wLTextBox.Size = new System.Drawing.Size(150, 27);
            wLTextBox.TabIndex = 23;
            wLTextBox.Value = new decimal(new int[] { 200, 0, 0, 0 });
            // 
            // wWTextBox
            // 
            wWTextBox.Location = new System.Drawing.Point(86, 227);
            wWTextBox.Maximum = new decimal(new int[] { 65535, 0, 0, 0 });
            wWTextBox.Minimum = new decimal(new int[] { 200, 0, 0, 0 });
            wWTextBox.Name = "wWTextBox";
            wWTextBox.Size = new System.Drawing.Size(150, 27);
            wWTextBox.TabIndex = 22;
            wWTextBox.Value = new decimal(new int[] { 200, 0, 0, 0 });
            // 
            // centerMaxLabel
            // 
            centerMaxLabel.AutoSize = true;
            centerMaxLabel.ForeColor = System.Drawing.Color.White;
            centerMaxLabel.Location = new System.Drawing.Point(273, 467);
            centerMaxLabel.Margin = new Padding(4, 0, 4, 0);
            centerMaxLabel.Name = "centerMaxLabel";
            centerMaxLabel.Size = new System.Drawing.Size(54, 20);
            centerMaxLabel.TabIndex = 17;
            centerMaxLabel.Text = "65535";
            // 
            // centerMinLabel
            // 
            centerMinLabel.AutoSize = true;
            centerMinLabel.ForeColor = System.Drawing.Color.White;
            centerMinLabel.Location = new System.Drawing.Point(29, 463);
            centerMinLabel.Margin = new Padding(4, 0, 4, 0);
            centerMinLabel.Name = "centerMinLabel";
            centerMinLabel.Size = new System.Drawing.Size(18, 20);
            centerMinLabel.TabIndex = 16;
            centerMinLabel.Text = "0";
            // 
            // widthMaxLabel
            // 
            widthMaxLabel.AutoSize = true;
            widthMaxLabel.ForeColor = System.Drawing.Color.White;
            widthMaxLabel.Location = new System.Drawing.Point(276, 326);
            widthMaxLabel.Margin = new Padding(4, 0, 4, 0);
            widthMaxLabel.Name = "widthMaxLabel";
            widthMaxLabel.Size = new System.Drawing.Size(54, 20);
            widthMaxLabel.TabIndex = 15;
            widthMaxLabel.Text = "65535";
            widthMaxLabel.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // widthMinLabel
            // 
            widthMinLabel.AutoSize = true;
            widthMinLabel.ForeColor = System.Drawing.Color.White;
            widthMinLabel.Location = new System.Drawing.Point(21, 322);
            widthMinLabel.Margin = new Padding(4, 0, 4, 0);
            widthMinLabel.Name = "widthMinLabel";
            widthMinLabel.Size = new System.Drawing.Size(18, 20);
            widthMinLabel.TabIndex = 14;
            widthMinLabel.Text = "0";
            widthMinLabel.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // centerTrackBar
            // 
            centerTrackBar.Location = new System.Drawing.Point(13, 427);
            centerTrackBar.Margin = new Padding(4);
            centerTrackBar.Name = "centerTrackBar";
            centerTrackBar.Size = new System.Drawing.Size(314, 56);
            centerTrackBar.TabIndex = 13;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            label2.ForeColor = System.Drawing.Color.White;
            label2.Location = new System.Drawing.Point(21, 365);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(57, 27);
            label2.TabIndex = 12;
            label2.Text = "窗位:";
            // 
            // widthTrackBar
            // 
            widthTrackBar.Location = new System.Drawing.Point(13, 286);
            widthTrackBar.Margin = new Padding(4);
            widthTrackBar.Name = "widthTrackBar";
            widthTrackBar.Size = new System.Drawing.Size(314, 56);
            widthTrackBar.TabIndex = 11;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            label1.ForeColor = System.Drawing.Color.White;
            label1.Location = new System.Drawing.Point(19, 227);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(57, 27);
            label1.TabIndex = 10;
            label1.Text = "窗宽:";
            // 
            // pictureBox1
            // 
            pictureBox1.Location = new System.Drawing.Point(21, 37);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new System.Drawing.Size(306, 165);
            pictureBox1.TabIndex = 24;
            pictureBox1.TabStop = false;
            pictureBox1.Paint += PictureBox_Paint;
            // 
            // WwwlPanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            Controls.Add(groupBox1);
            Margin = new Padding(4);
            Name = "WwwlPanel";
            Size = new System.Drawing.Size(375, 540);
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)wLTextBox).EndInit();
            ((System.ComponentModel.ISupportInitialize)wWTextBox).EndInit();
            ((System.ComponentModel.ISupportInitialize)centerTrackBar).EndInit();
            ((System.ComponentModel.ISupportInitialize)widthTrackBar).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label centerMaxLabel;
        private System.Windows.Forms.Label centerMinLabel;
        private System.Windows.Forms.Label widthMaxLabel;
        private System.Windows.Forms.Label widthMinLabel;
        private System.Windows.Forms.TrackBar centerTrackBar;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TrackBar widthTrackBar;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown wLTextBox;
        private System.Windows.Forms.NumericUpDown wWTextBox;
        private System.Windows.Forms.PictureBox pictureBox1;
    }
}
