/**
* File: IRayAttrDef.cs
*
* Purpose: IRay attribute definition
*
*
* <AUTHOR>
* @version 1.0 2015/4/23
*
* Copyright (C) 2009, 2015, iRay Technology (Shanghai) Ltd.
*
*/
namespace iDetector
{
	public partial class SdkInterface
	{
		public const int Cfg_ProtocolEdition = 1;
		public const int Cfg_ProductNo = 2;
		public const int Cfg_SN = 3;
		public const int Cfg_UseServiceProcess = 8;
		public const int Cfg_LogLevel = 7;
		public const int Cfg_HostIP = 101;
		public const int Cfg_HostPort = 102;
		public const int Cfg_RemoteIP = 103;
		public const int Cfg_RemotePort = 104;
		public const int Cfg_ComPort = 105;
		public const int Cfg_PleoraConnStr = 106;
		public const int Cfg_PleoraPacketSize = 107;
		public const int Cfg_WinpcapConnStr = 108;
		public const int Cfg_PleoraMaxFps = 109;
		public const int Cfg_HostImagePort = 110;
		public const int Cfg_HostImageAckPort = 111;
		public const int Cfg_PCIEBoardIndex = 112;
		public const int Cfg_E5_ResendCnt = 113;
		public const int Cfg_E5_ResendInterval = 114;
		public const int Cfg_HeartBeatTimeOutDuration = 115;
		public const int Cfg_AcquireImage_ThreadPriority = 116;
		public const int Cfg_AcquireImage_AffinityCpuId = 117;
		public const int Cfg_USBConnStr = 151;
		public const int Cfg_FTP_Download_HostIP = 201;
		public const int Cfg_FTP_Download_HostPort = 202;
		public const int Cfg_FTP_Download_User = 203;
		public const int Cfg_FTP_Download_PWD = 204;
		public const int Cfg_FTP_Download_LocalPath = 205;
		public const int Cfg_FTP_Upload_HostIP = 206;
		public const int Cfg_FTP_Upload_HostPort = 207;
		public const int Cfg_FTP_Upload_User = 208;
		public const int Cfg_FTP_Upload_PWD = 209;
		public const int Cfg_FTP_Upload_LocalPath = 210;
		public const int Cfg_OffsetAlarmMinute = 301;
		public const int Cfg_GainAlarmTime = 302;
		public const int Cfg_DefectAlarmTime = 303;
		public const int Cfg_CaliValidity_PreWarnMinute = 304;
		public const int Cfg_CaliValidity_Enable = 305;
		public const int Cfg_DefaultSubset = 306;
		public const int Cfg_DefaultCorrectOption = 307;
		public const int Cfg_CalibrationFlow = 312;
		public const int Cfg_GridRemovalPreProcess_Enable = 314;
		public const int Cfg_MultiEnergy_Process = 315;
		public const int Cfg_Calib_GainPoints_FactoryMode = 316;
		public const int Cfg_Calib_GainFrames_FactoryMode = 317;
		public const int Cfg_MultiEnergy_FullEnergyType = 318;
		public const int Cfg_DigitalGainEnable = 319;
		public const int Cfg_ClearAcqParam_DelayTime = 501;
		public const int Cfg_FWUpdTimeOut = 504;
		public const int Cfg_ResetTimeout = 507;
		public const int Cfg_PreviewImage_Enable = 508;
		public const int Cfg_AllowMismatchSN = 513;
		public const int Cfg_SeqAcq_AutoStopToSyncExp = 520;
		public const int Cfg_DetectorSleepMode = 522;
		public const int Cfg_SkipFirmwareCheck = 523;
		public const int Cfg_TimingOutput = 524;
		public const int Cfg_RetransferImageEnable = 525;
		public const int Cfg_AecPrevBinningMode = 526;
		public const int Cfg_ExpEnableDelayTime = 532;
		public const int Cfg_FwUpgradeMethod = 534;
		public const int Cfg_MFSA_AcqParam_FrameNumber = 536;
		public const int Attr_Prod_Name = 1001;
		public const int Attr_Prod_Description = 1002;
		public const int Attr_Prod_FullWidth = 1003;
		public const int Attr_Prod_FullHeight = 1004;
		public const int Attr_Prod_PhysicalPixelSize = 1005;
		public const int Attr_Prod_BitDepth = 1006;
		public const int Attr_Prod_DummyTop = 1009;
		public const int Attr_Prod_DummyBottom = 1010;
		public const int Attr_Prod_DummyLeft = 1011;
		public const int Attr_Prod_DummyRight = 1012;
		public const int Attr_Prod_PhysicalGateSize = 1021;
		public const int Attr_Prod_PhysicalAfeChSize = 1022;
		public const int Attr_Prod_PhysicalGateTopEdgeSize = 1023;
		public const int Attr_Prod_PhysicalGateBottomEdgeSize = 1024;
		public const int Attr_Prod_PhysicalAfeChLeftEdgeSize = 1025;
		public const int Attr_Prod_PhysicalAfeChRightEdgeSize = 1026;
		public const int Attr_UROM_ProductNo = 2001;
		public const int Attr_UROM_MainVersion = 2002;
		public const int Attr_UROM_ReadVersion = 2003;
		public const int Attr_UROM_McuVersion = 2004;
		public const int Attr_UROM_ArmVersion = 2005;
		public const int Attr_UROM_KernelVersion = 2006;
		public const int Attr_UROM_MasterBuildTime = 2008;
		public const int Attr_UROM_SlaveBuildTime = 2009;
		public const int Attr_UROM_McuBuildTime = 2010;
		public const int Attr_UROM_IntegrateTime = 2013;
		public const int Attr_UROM_ZoomMode = 2014;
		public const int Attr_UROM_ExpEnable_SignalLevel = 2015;
		public const int Attr_UROM_SelfClearEnable = 2016;
		public const int Attr_UROM_SelfClearSpanTime = 2017;
		public const int Attr_UROM_SequenceIntervalTime = 2018;
		public const int Attr_UROM_TriggerMode = 2019;
		public const int Attr_UROM_DynamicFlag = 2020;
		public const int Attr_UROM_TubeReadyTime = 2021;
		public const int Attr_UROM_SequenceIntervalTime_HighPrecision = 2022;
		public const int Attr_UROM_SetDelayTime = 2023;
		public const int Attr_UROM_ExpWindowTime = 2025;
		public const int Attr_UROM_SyncExpTime = 2027;
		public const int Attr_UROM_SyncExpTime_HighPrecision = 2028;
		public const int Attr_UROM_PGA = 2030;
		public const int Attr_UROM_PrepCapMode = 2032;
		public const int Attr_UROM_SelfCapEnable = 2033;
		public const int Attr_UROM_FluroSync = 2034;
		public const int Attr_UROM_SrcPort = 2035;
		public const int Attr_UROM_SrcIP = 2036;
		public const int Attr_UROM_SrcMAC = 2037;
		public const int Attr_UROM_DestPort = 2038;
		public const int Attr_UROM_DestIP = 2039;
		public const int Attr_UROM_DestMAC = 2040;
		public const int Attr_UROM_SyncboxIP = 2041;
		public const int Attr_UROM_PreviewImgMode = 2044;
		public const int Attr_UROM_HWOffsetType = 2045;
		public const int Attr_UROM_AcquireDelayTime = 2046;
		public const int Attr_UROM_BinningMode = 2047;
		public const int Attr_UROM_ExpMode = 2050;
		public const int Attr_UROM_AecMainTime = 2051;
		public const int Attr_UROM_DynaOffsetGapTime = 2052;
		public const int Attr_UROM_DynaOffsetEnable = 2053;
		public const int Attr_UROM_ImagePktGapTime = 2054;
		public const int Attr_UROM_OutModeCapTrig = 2069;
		public const int Attr_UROM_HvgPrepOn = 2055;
		public const int Attr_UROM_HvgXRayEnable = 2056;
		public const int Attr_UROM_HvgXRayOn = 2057;
		public const int Attr_UROM_HvgXRaySyncOut = 2058;
		public const int Attr_UROM_HvgXRaySyncIn = 2059;
		public const int Attr_UROM_CbxBuildTime = 2060;
		public const int Attr_UROM_SubProductNo = 2061;
		public const int Attr_UROM_SerialNo = 2062;
		public const int Attr_UROM_ImageChType = 2063;
		public const int Attr_UROM_ImageChProtocol = 2064;
		public const int Attr_UROM_HWGainEnable = 2065;
		public const int Attr_UROM_ExpTimeValidPercent = 2066;
		public const int Attr_UROM_FreesyncCenterThreshold = 2067;
		public const int Attr_UROM_FreesyncEdgeThreshold = 2068;
		public const int Attr_UROM_FreesyncSubFlow = 2070;
		public const int Attr_UROM_PowSeriesCorrectEnable = 2071;
		public const int Attr_UROM_PulseClearTimes = 2072;
		public const int Attr_UROM_ROIColStartPos = 2073;
		public const int Attr_UROM_ROIColEndPos = 2074;
		public const int Attr_UROM_ROIRowStartPos = 2075;
		public const int Attr_UROM_ROIRowEndPos = 2076;
		public const int Attr_UROM_FullWell = 2077;
		public const int Attr_UROM_InnerSubFlow = 2078;
		public const int Attr_UROM_SoftwareSubFlow = 2079;
		public const int Attr_UROM_MainMBCpuVersion = 2080;
		public const int Attr_UROM_MainMBCpuBuildTime = 2081;
		public const int Attr_UROM_XCombWiseType = 2082;
		public const int Attr_UROM_YCombWiseType = 2083;
		public const int Attr_UROM_SubProductVersion = 2084;
		public const int Attr_UROM_WarningTemperature = 2085;
		public const int Attr_UROM_PowerOffTemperature = 2086;
		public const int Attr_UROM_WLAN_DHCPEnable = 2087;
		public const int Attr_UROM_LAN_DHCPEnable = 2088;
		public const int Attr_UROM_DHCP_StaticIP = 2089;
		public const int Attr_UROM_HWPreoffsetBase = 2090;
		public const int Attr_UROM_ShockMcuVersion = 2091;
		public const int Attr_UROM_ShockMcuBuildTime = 2092;
		public const int Attr_UROM_ProductConfigCode = 2093;
		public const int Attr_UROM_NetworkBand = 2094;
		public const int Attr_UROM_CofPGA = 2095;
		public const int Attr_UROM_TriggerThreshold = 2096;
		public const int Attr_UROM_NumberOfFramesDiscardedInSequenceAcquire = 2097;
		public const int Attr_UROM_AutoSleepIdleTime = 2098;
		public const int Attr_UROM_HvgDelayTime = 2099;
		public const int Attr_UROM_AutoPowerOffTime = 2100;
		public const int Attr_UROM_ImageEnergyType = 2101;
		public const int Attr_UROM_SerialClearTimes = 2102;
		public const int Attr_UROM_SyncInClearFlow = 2103;
		public const int Attr_UROM_AedMethod = 2104;
		public const int Attr_UROM_AntiInterferenceLevel = 2105;
		public const int Attr_UROM_PGA2 = 2106;
		public const int Attr_UROM_PGA3 = 2107;
		public const int Attr_UROM_PGA4 = 2108;
		public const int Attr_UROM_AcquireDelayTime_HighPrecision = 2109;
		public const int Attr_UROM_DataPacketSize = 2110;
		public const int Attr_UROM_DigitalBinning = 2111;
		public const int Attr_UROM_IntegrationMethod = 2112;
		public const int Attr_UROM_FrameIntervalOfMFS = 2113;
		public const int Attr_UROM_IDCSupportRegionType = 2114;
		public const int Attr_UROM_BatchID = 2115;
		public const int Attr_UROM_IntegrateTime_W = 2540;
		public const int Attr_UROM_ZoomMode_W = 2501;
		public const int Attr_UROM_ExpEnable_SignalLevel_W = 2502;
		public const int Attr_UROM_SelfClearEnable_W = 2503;
		public const int Attr_UROM_SelfClearSpanTime_W = 2504;
		public const int Attr_UROM_SequenceIntervalTime_W = 2505;
		public const int Attr_UROM_TriggerMode_W = 2506;
		public const int Attr_UROM_DynamicFlag_W = 2507;
		public const int Attr_UROM_TubeReadyTime_W = 2508;
		public const int Attr_UROM_SetDelayTime_W = 2510;
		public const int Attr_UROM_SequenceIntervalTime_HighPrecision_W = 2511;
		public const int Attr_UROM_ExpWindowTime_W = 2512;
		public const int Attr_UROM_PGA_W = 2513;
		public const int Attr_UROM_PrepCapMode_W = 2514;
		public const int Attr_UROM_SelfCapEnable_W = 2515;
		public const int Attr_UROM_FluroSync_W = 2516;
		public const int Attr_UROM_SrcIP_W = 2518;
		public const int Attr_UROM_SrcMAC_W = 2519;
		public const int Attr_UROM_DestPort_W = 2520;
		public const int Attr_UROM_DestIP_W = 2521;
		public const int Attr_UROM_DestMAC_W = 2522;
		public const int Attr_UROM_PreviewImgMode_W = 2523;
		public const int Attr_UROM_HWOffsetType_W = 2544;
		public const int Attr_UROM_SyncboxIP_W = 2543;
		public const int Attr_UROM_AcquireDelayTime_W = 2524;
		public const int Attr_UROM_BinningMode_W = 2525;
		public const int Attr_UROM_ExpMode_W = 2528;
		public const int Attr_UROM_AecMainTime_W = 2529;
		public const int Attr_UROM_DynaOffsetGapTime_W = 2530;
		public const int Attr_UROM_DynaOffsetEnable_W = 2531;
		public const int Attr_UROM_ImagePktGapTime_W = 2542;
		public const int Attr_UROM_OutModeCapTrig_W = 2541;
		public const int Attr_UROM_HvgPrepOn_W = 2532;
		public const int Attr_UROM_HvgXRayEnable_W = 2533;
		public const int Attr_UROM_HvgXRayOn_W = 2534;
		public const int Attr_UROM_HvgXRaySyncOut_W = 2535;
		public const int Attr_UROM_HvgXRaySyncIn_W = 2536;
		public const int Attr_UROM_ExpTimeValidPercent_W = 2537;
		public const int Attr_UROM_FreesyncCenterThreshold_W = 2538;
		public const int Attr_UROM_FreesyncEdgeThreshold_W = 2539;
		public const int Attr_UROM_PowSeriesCorrectEnable_W = 2545;
		public const int Attr_UROM_ROIColStartPos_W = 2546;
		public const int Attr_UROM_ROIColEndPos_W = 2547;
		public const int Attr_UROM_ROIRowStartPos_W = 2548;
		public const int Attr_UROM_ROIRowEndPos_W = 2549;
		public const int Attr_UROM_FullWell_W = 2550;
		public const int Attr_UROM_PulseClearTimes_W = 2551;
		public const int Attr_UROM_InnerSubFlow_W = 2552;
		public const int Attr_UROM_SoftwareSubFlow_W = 2553;
		public const int Attr_UROM_SubProductVersion_W = 2554;
		public const int Attr_UROM_WarningTemperature_W = 2555;
		public const int Attr_UROM_PowerOffTemperature_W = 2556;
		public const int Attr_UROM_WLAN_DHCPEnable_W = 2557;
		public const int Attr_UROM_LAN_DHCPEnable_W = 2558;
		public const int Attr_UROM_DHCP_StaticIP_W = 2559;
		public const int Attr_UROM_HWPreoffsetBase_W = 2560;
		public const int Attr_UROM_YCombWiseType_W = 2561;
		public const int Attr_UROM_NetworkBand_W = 2562;
		public const int Attr_UROM_CofPGA_W = 2563;
		public const int Attr_UROM_TriggerThreshold_W = 2564;
		public const int Attr_UROM_NumberOfFramesDiscardedInSequenceAcquire_W = 2565;
		public const int Attr_UROM_AutoSleepIdleTime_W = 2566;
		public const int Attr_UROM_HvgDelayTime_W = 2567;
		public const int Attr_UROM_AutoPowerOffTime_W = 2568;
		public const int Attr_UROM_ImageEnergyType_W = 2569;
		public const int Attr_UROM_SerialClearTimes_W = 2570;
		public const int Attr_UROM_SyncInClearFlow_W = 2571;
		public const int Attr_UROM_AedMethod_W = 2572;
		public const int Attr_UROM_AntiInterferenceLevel_W = 2573;
		public const int Attr_UROM_PGA2_W = 2574;
		public const int Attr_UROM_PGA3_W = 2575;
		public const int Attr_UROM_PGA4_W = 2576;
		public const int Attr_UROM_AcquireDelayTime_HighPrecision_W = 2577;
		public const int Attr_UROM_DataPacketSize_W = 2578;
		public const int Attr_UROM_DigitalBinning_W = 2579;
		public const int Attr_UROM_IntegrationMethod_W = 2580;
		public const int Attr_UROM_FrameIntervalOfMFS_W = 2581;
		public const int Attr_Wifi_Enable = 4000;
		public const int Attr_Wifi_AP_SSID = 4001;
		public const int Attr_Wifi_AP_Key = 4002;
		public const int Attr_Wifi_AP_CountryCode = 4003;
		public const int Attr_Wifi_AP_FrequencySel = 4004;
		public const int Attr_Wifi_AP_BandWidthSel = 4005;
		public const int Attr_Wifi_AP_ChannelSel = 4006;
		public const int Attr_Wifi_AP_SecuritySel = 4007;
		public const int Attr_Wifi_AP_ApModeEn = 4008;
		public const int Attr_Wifi_AP_DhcpServerEn = 4009;
		public const int Attr_Wifi_Client_ListNum = 4010;
		public const int Attr_Wifi_Client_CurrentSel = 4011;
		public const int Attr_Wifi_Client_SSID0 = 4012;
		public const int Attr_Wifi_Client_Key0 = 4013;
		public const int Attr_Wifi_Client_SSID1 = 4014;
		public const int Attr_Wifi_Client_Key1 = 4015;
		public const int Attr_Wifi_Client_SSID2 = 4016;
		public const int Attr_Wifi_Client_Key2 = 4017;
		public const int Attr_Wifi_Client_SSID3 = 4018;
		public const int Attr_Wifi_Client_Key3 = 4019;
		public const int Attr_Wifi_Client_SSID4 = 4020;
		public const int Attr_Wifi_Client_Key4 = 4021;
		public const int Attr_Wifi_Client_SSID5 = 4022;
		public const int Attr_Wifi_Client_Key5 = 4023;
		public const int Attr_Wifi_Client_SSID6 = 4024;
		public const int Attr_Wifi_Client_Key6 = 4025;
		public const int Attr_Wifi_Client_SSID7 = 4026;
		public const int Attr_Wifi_Client_Key7 = 4027;
		public const int Attr_Wifi_Client_SSID8 = 4028;
		public const int Attr_Wifi_Client_Key8 = 4029;
		public const int Attr_Wifi_Client_SSID9 = 4030;
		public const int Attr_Wifi_Client_Key9 = 4031;
		public const int Attr_Wifi_AP_CountryCode_ByLetter = 4032;
		public const int Attr_Wifi_AP_5GTransmitPower = 4033;
		public const int Attr_Wifi_Enable_W = 4500;
		public const int Attr_Wifi_AP_SSID_W = 4501;
		public const int Attr_Wifi_AP_Key_W = 4502;
		public const int Attr_Wifi_AP_CountryCode_W = 4503;
		public const int Attr_Wifi_AP_FrequencySel_W = 4504;
		public const int Attr_Wifi_AP_BandWidthSel_W = 4505;
		public const int Attr_Wifi_AP_ChannelSel_W = 4506;
		public const int Attr_Wifi_AP_SecuritySel_W = 4507;
		public const int Attr_Wifi_AP_ApModeEn_W = 4508;
		public const int Attr_Wifi_AP_DhcpServerEn_W = 4509;
		public const int Attr_Wifi_Client_ListNum_W = 4510;
		public const int Attr_Wifi_Client_CurrentSel_W = 4511;
		public const int Attr_Wifi_Client_SSID0_W = 4512;
		public const int Attr_Wifi_Client_Key0_W = 4513;
		public const int Attr_Wifi_Client_SSID1_W = 4514;
		public const int Attr_Wifi_Client_Key1_W = 4515;
		public const int Attr_Wifi_Client_SSID2_W = 4516;
		public const int Attr_Wifi_Client_Key2_W = 4517;
		public const int Attr_Wifi_Client_SSID3_W = 4518;
		public const int Attr_Wifi_Client_Key3_W = 4519;
		public const int Attr_Wifi_Client_SSID4_W = 4520;
		public const int Attr_Wifi_Client_Key4_W = 4521;
		public const int Attr_Wifi_Client_SSID5_W = 4522;
		public const int Attr_Wifi_Client_Key5_W = 4523;
		public const int Attr_Wifi_Client_SSID6_W = 4524;
		public const int Attr_Wifi_Client_Key6_W = 4525;
		public const int Attr_Wifi_Client_SSID7_W = 4526;
		public const int Attr_Wifi_Client_Key7_W = 4527;
		public const int Attr_Wifi_Client_SSID8_W = 4528;
		public const int Attr_Wifi_Client_Key8_W = 4529;
		public const int Attr_Wifi_Client_SSID9_W = 4530;
		public const int Attr_Wifi_Client_Key9_W = 4531;
		public const int Attr_Wifi_AP_CountryCode_ByLetter_W = 4532;
		public const int Attr_Wifi_AP_5GTransmitPower_W = 4533;
		public const int Attr_WorkDir = 5001;
		public const int Attr_State = 5002;
		public const int Attr_ConnState = 5003;
		public const int Attr_CurrentTask = 5004;
		public const int Attr_CurrentTransaction = 5005;
		public const int Attr_FsmState = 5006;
		public const int Attr_Width = 5007;
		public const int Attr_Height = 5008;
		public const int Attr_PrevImg_Width = 5009;
		public const int Attr_PrevImg_Height = 5010;
		public const int Attr_Authority = 5011;
		public const int Attr_ElapsedExpWindowTime = 5014;
		public const int Attr_FWUpdateProgress = 5015;
		public const int Attr_ImageTransProgress = 5016;
		public const int Attr_RdResult_T1 = 5017;
		public const int Attr_RdResult_T2 = 5018;
		public const int Attr_RdResult_Humidity = 5019;
		public const int Attr_RdResult_Shock_Threshold = 5020;
		public const int Attr_CurrentSubset = 5021;
		public const int Attr_CurrentCorrectOption = 5022;
		public const int Attr_OffsetValidityState = 5023;
		public const int Attr_GainValidityState = 5024;
		public const int Attr_DefectValidityState = 5025;
		public const int Attr_LagValidityState = 5026;
		public const int Attr_GhostValidityState = 5027;
		public const int Attr_OffsetTotalFrames = 5028;
		public const int Attr_OffsetValidFrames = 5029;
		public const int Attr_GainTotalFrames = 5030;
		public const int Attr_GainValidFrames = 5031;
		public const int Attr_DefectTotalFrames = 5032;
		public const int Attr_DefectValidFrames = 5033;
		public const int Attr_LagTotalFrames = 5034;
		public const int Attr_LagValidFrames = 5035;
		public const int Attr_Battery_Exist = 5036;
		public const int Attr_Battery_Remaining = 5037;
		public const int Attr_Battery_ChargingStatus = 5038;
		public const int Attr_Battery_PowerWarnStatus = 5039;
		public const int Attr_NetworkInterface = 5040;
		public const int Attr_WifiStatu_LinkedAP = 5041;
		public const int Attr_WifiStatu_WorkingBand = 5042;
		public const int Attr_WifiStatu_WorkingSignalIntensity = 5043;
		public const int Attr_WifiStatu_WorkingLinkQuality = 5044;
		public const int Attr_WifiStatu_WorkingTxPower = 5045;
		public const int Attr_HwTmpl_Offset_Enable = 5046;
		public const int Attr_HwTmpl_Offset_ValidIndex = 5047;
		public const int Attr_HwTmpl_Offset_FileCount = 5048;
		public const int Attr_HwTmpl_Gain_Enable = 5049;
		public const int Attr_HwTmpl_Gain_ValidIndex = 5050;
		public const int Attr_HwTmpl_Gain_FileCount = 5051;
		public const int Attr_HwTmpl_MostGain_Enable = 5052;
		public const int Attr_HwTmpl_MostGain_ValidIndex = 5053;
		public const int Attr_HwTmpl_MostGain_FileCount = 5054;
		public const int Attr_HwTmpl_Defect_Enable = 5055;
		public const int Attr_HwTmpl_Defect_ValidIndex = 5056;
		public const int Attr_HwTmpl_Defect_FileCount = 5057;
		public const int Attr_HwTmpl_Lag_Enable = 5058;
		public const int Attr_HwTmpl_Lag_ValidIndex = 5059;
		public const int Attr_HwTmpl_Lag_FileCount = 5060;
		public const int Attr_CorrectionPrepared = 5061;
		public const int Attr_RdResult_OutExpState = 5062;
		public const int Attr_RdResult_AutoSleepState = 5063;
		public const int Attr_Battery_ExternalPower = 5064;
		public const int Attr_GCU_OnlineState = 5065;
		public const int Attr_ClippingValue = 5066;
		public const int Attr_Battery_FullChargeCapacity = 5067;
		public const int Attr_Battery_DesignCapacity = 5068;
		public const int Attr_Battery_CycleCount = 5069;
		public const int Attr_Battery_Temperature = 5070;
		public const int Attr_Battery_RelativeStateOfCharge = 5071;
		public const int Attr_Battery_AbsoluteStateOfCharge = 5072;
		public const int Attr_Battery_RemainingCapacity = 5073;
		public const int Attr_Battery_Manufacture = 5074;
		public const int Attr_Battery_SN = 5075;
		public const int Attr_Battery_DeviceName = 5076;
		public const int Attr_HallSensorValue = 5077;
		public const int Attr_FD_LifeTime = 5078;
		public const int Attr_FD_PowerOnCount = 5079;
		public const int Attr_FD_PowerOffCount = 5080;
		public const int Attr_WifiInfo_WirelessAPMAC = 5081;
		public const int Attr_WifiInfo_WirelessClientMAC = 5082;
		public const int Attr_Temperature_State = 5083;
		public const int Attr_RdResult_RiskA_Shock_Threshold = 5084;
		public const int Attr_RdResult_RiskB_Shock_Threshold = 5085;
		public const int Attr_RdResult_RiskC_Shock_Threshold = 5086;
		public const int Attr_RdResult_RiskB2A_Shock_Threshold = 5087;
		public const int Attr_RdResult_RiskC2B_Shock_Threshold = 5088;
		public const int Attr_RdResult_RiskD2C_Shock_Threshold = 5089;
		public const int Attr_MultiGainPointNumber = 5090;
		public const int Attr_FD_FwMainVersion = 5091;
		public const int Attr_MultiEnergy_RegistrationValidityState = 5092;
		public const int Attr_MultiEnergy_RegistrationTotalFrames = 5093;
		public const int Attr_MultiEnergy_RegistrationValidFrames = 5094;
		public const int Attr_Authority_Det = 5097;
		public const int Attr_Authority_Tls = 5098;
		public const int Attr_Authority_Alg = 5099;
		public const int Attr_MFS_ValidFrames = 5101;
		public const int Attr_Battery_2nd_Exist = 5102;
		public const int Attr_Battery_2nd_Remaining = 5103;
		public const int Attr_Battery_2nd_ChargingStatus = 5104;
		public const int Attr_Battery_2nd_FullChargeCapacity = 5105;
		public const int Attr_Battery_2nd_DesignCapacity = 5106;
		public const int Attr_Battery_2nd_CycleCount = 5107;
		public const int Attr_Battery_2nd_Temperature = 5108;
		public const int Attr_Battery_2nd_RelativeStateOfCharge = 5109;
		public const int Attr_Battery_2nd_AbsoluteStateOfCharge = 5110;
		public const int Attr_Battery_2nd_RemainingCapacity = 5111;
		public const int Attr_Battery_2nd_Manufacture = 5112;
		public const int Attr_Battery_2nd_SN = 5113;
		public const int Attr_Battery_2nd_DeviceName = 5114;
		public const int Attr_Network_LAN_IP_Addr = 5300;
		public const int Attr_Network_WLAN_IP_Addr = 5301;
		public const int Attr_Network_WLAN_DHCPEnable = 5498;
		public const int Attr_Network_LAN_DHCPEnable = 5499;
		public const int Attr_Network_LAN_IP_Addr_W = 5500;
		public const int Attr_Network_WLAN_IP_Addr_W = 5501;
		public const int Attr_Network_WLAN_DHCPEnable_W = 5698;
		public const int Attr_Network_LAN_DHCPEnable_W = 5699;
		public const int Attr_TransactionProgress = 5700;
		public const int Attr_IDCPanelSensitivity = 5701;
		public const int Attr_IDCThresholdStartKV = 5702;
		public const int Attr_IDCThresholdEndKV = 5703;
		public const int Attr_IDCRegionSelectionType = 5704;
		public const int Attr_IDCHvgCtrlBoxOnlineState = 5705;
		public const int Attr_IDCRegionSelectionValue = 5706;
		public const int Attr_AcqParam_Mode_W = 5800;
		public const int Attr_AcqParam_Binning_W = 5801;
		public const int Attr_AcqParam_Zoom_W = 5802;
		public const int Attr_AcqParam_ROIColStartPos_W = 5803;
		public const int Attr_AcqParam_ROIColEndPos_W = 5804;
		public const int Attr_AcqParam_ROIRowStartPos_W = 5805;
		public const int Attr_AcqParam_ROIRowEndPos_W = 5806;
		public const int Attr_AcqParam_PGA_W = 5807;
		public const int Attr_AcqParam_VT_W = 5808;
		public const int Attr_AcqParam_SequenceIntervalTime_HighPrecision_W = 5809;
		public const int Attr_AcqParam_SetDelayTime_W = 5810;
		public const int Attr_AcqParam_HWPreoffsetDiscardNumberBeforeAcq_W = 5811;
		public const int Attr_AcqParam_HWPreoffsetAcqNumber_W = 5812;
		public const int Attr_AcqParam_HWCorrectionEnable_W = 5813;
		public const int Attr_AcqParam_FluroSync_W = 5814;
		public const int Attr_AcqParam_NumberOfFramesDiscardedInSequenceAcquire_W = 5815;
		public const int Attr_AcqParam_IntegrationMethod_W = 5816;
		public const int Attr_AcqParam_CofPGA_W = 5817;
		public const int Attr_AcqParam_OffsetType_W = 5818;
	}
}
