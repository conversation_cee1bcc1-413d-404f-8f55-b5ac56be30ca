﻿using LkControls.common;

namespace InduVision.LkControls.LayoutControls
{
    partial class DuplexIQIPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            label1 = new System.Windows.Forms.Label();
            textBox1 = new System.Windows.Forms.TextBox();
            applyButton = new RoundButton();
            label2 = new System.Windows.Forms.Label();
            SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.ForeColor = System.Drawing.Color.White;
            label1.Location = new System.Drawing.Point(21, 23);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(69, 20);
            label1.TabIndex = 0;
            label1.Text = "线条宽度";
            // 
            // textBox1
            // 
            textBox1.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            textBox1.Location = new System.Drawing.Point(108, 17);
            textBox1.Name = "textBox1";
            textBox1.Size = new System.Drawing.Size(174, 33);
            textBox1.TabIndex = 1;
            // 
            // applyButton
            // 
            applyButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            applyButton.BorderRadius = 8;
            applyButton.BorderSize = 1;
            applyButton.ButtonTextColor = System.Drawing.Color.White;
            applyButton.CustomEnabled = true;
            applyButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            applyButton.FlatAppearance.BorderSize = 0;
            applyButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            applyButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            applyButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            applyButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            applyButton.IsPressed = true;
            applyButton.Location = new System.Drawing.Point(23, 75);
            applyButton.Name = "applyButton";
            applyButton.NormalBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.NormalColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.Size = new System.Drawing.Size(307, 46);
            applyButton.TabIndex = 23;
            applyButton.Text = "应用";
            applyButton.UseVisualStyleBackColor = false;
            applyButton.Click += applyButton_Click;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.ForeColor = System.Drawing.Color.White;
            label2.Location = new System.Drawing.Point(297, 24);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(27, 20);
            label2.TabIndex = 24;
            label2.Text = "px";
            // 
            // DuplexIQIPanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            Controls.Add(label2);
            Controls.Add(applyButton);
            Controls.Add(textBox1);
            Controls.Add(label1);
            Name = "DuplexIQIPanel";
            Size = new System.Drawing.Size(356, 145);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox textBox1;
        private RoundButton applyButton;
        private System.Windows.Forms.Label label2;
    }
}
