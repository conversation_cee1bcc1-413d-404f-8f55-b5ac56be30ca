﻿using CommunityToolkit.HighPerformance.Helpers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InduVision.LkControls.FPD.IRay
{
    public partial class ConnectDevice : UserControl
    {
        public event Action<JObject> ConnectedDevice;

        public ConnectDevice()
        {
            InitializeComponent();

            deviceComboBox.SelectedIndex = 0;
            captureComboBox.SelectedIndex = 0;
        }

        private void connectButton_Click(object sender, EventArgs e)
        {
            JObject jObject = new JObject
            {
                { "deviceType", deviceComboBox.SelectedIndex },
                { "captureType", captureComboBox.SelectedIndex }
            };

            ConnectedDevice?.Invoke(jObject);
        }

        private void deviceComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            
        }
    }
}
