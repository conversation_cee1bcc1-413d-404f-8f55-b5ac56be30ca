﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.DirectoryServices.ActiveDirectory;
using System.Drawing.Imaging;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Vintasoft.Imaging;
using Vintasoft.Imaging.UI;
using Vintasoft.Imaging.Codecs.ImageFiles.Dicom;
using Vintasoft.Imaging.Codecs.ImageFiles.Tiff;
using FellowOakDicom;
using FellowOakDicom.IO.Buffer;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using CommunityToolkit.HighPerformance.Helpers;
using Newtonsoft.Json.Linq;
using System.Reflection.Metadata.Ecma335;
using NLog;
using OpenCvSharp;
using System.Timers;
using static OpenCvSharp.Stitcher;
using static System.Runtime.InteropServices.JavaScript.JSType;
using static System.Windows.Forms.AxHost;

namespace InduVision.LkControls.FPD.Nice
{

    public class StaticFPD
    {

        #region Constant
        public enum SystemErrorCode
        {
            SEC_Succeed = 0,
            SEC_FPD_Busy = 1,
            SEC_FPD_FeedBack_Process_Error,
            SEC_FPD_Not_Support_Current_Mode,
            SEC_FPD_FeedBack_Parameter_Error,
            SEC_FPD_Function_Not_Support_In_Current_Mode,
            SEC_SDK_Not_Init = 100,
            SEC_SDK_Repeated_init,
            SEC_SDK_DeviceID_Not_Exist,
            SEC_CMD_Timing_Error = 200,
            SEC_CMD_Send_Error = 201,
            SEC_CMD_Checksum_Error = 202,
            SEC_DEVICE_Network_Error = 300,
            SEC_DEVICE_Sock_Error = 301,
            SEC_Path_Error = 1000,
            SEC_Input_Parameter_Error,
            SEC_Read_Info_Failed,
            SEC_Open_File_Failed,
            SEC_Unknown,
        };

        public enum SystemInfoCode
        {
            SIC_Exposure_Window_Open = 0x01,  //曝光窗口打开
            SIC_Exposure_Winodw_Closed,       //曝光窗口关闭
            SIC_Exposure_Window_Timeout,      //曝光超时
            SIC_Device_Heart_Beat,
            SIC_Physical_Connection_State,    //对应的参数内容是：1表示正常，0表示异常 
            SIC_Upload_Tmp_File_Begin,        //上传校正模板开始
            SIC_Upload_Tmp_File_End,          //上传校正模板结束，结束不一定是上传成功，是否上传成功要查看回调函数中进度信息是否达到百分之百
            SIC_TCP_Connection_State,         //平板探测器的socket程序状态，1表示客户端与服务器链接成功， 0表示失败
            SIC_Power_Low,                    //提示电量低
            SIC_Power_Down_Soon,              //提示即将关闭
            SIC_FPD_Sleep_State,              //无线平板是否休眠，0表示休眠，1表示正常
            SIC_FPD_MultiFrame_Index,            //多帧采集图像的序号
            SIC_FPD_IndustryMode_Prepare_Rready, //工业模式下prepare准备好了
        };

        public enum ModeType
        {
            Idle = 0x00,
            Sync_In_Mode = 0x01,
            Software_Mode = 0x03,
            AED_Offset_Mode = 0x05,
            AED_Mode = 0x06,
            Outer_Mode = 0x07,
            Freerun_Mode = 0x08,
            Industry_Mode = 0x09
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct YIConfigInfo_SyncOut
        {
            public ushort usTubePrepare;
            public ushort usSampleNum;
            public sbyte cPreOffsetNum;
            public ushort usOffsetTimeGap;
            public ushort usExposureWindow;
            public ushort usExposureTimeOut;
            public ushort usStartRow;
            public ushort usEndRow;
            public ushort usStartColumn;
            public ushort usEndColumn;
            public sbyte cBinningMode;
            public sbyte cMaxFrameRate;
            public sbyte cCapacitor;
            public sbyte cRefTFT;
            public sbyte cOffsetOn;
            public sbyte cCorrectOn;
            public sbyte cReserve;
            public ushort usLineTime;
        }
        #endregion

        #region DLL

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        public delegate bool ImageReadyCallbackFuncEx(nint pImage, int iFlag, string sDeviceID);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        public delegate bool SystemInfoCallbackFuncEx(int iCodeIndex, string sContent, string sDeviceID);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        public delegate bool ProgressCallbackFunc(nint pProgress);

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct ProgressInfo
        {
            public int value;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 300)]
            public string sInfo;
        }

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int YI_Initialize_FPD_V4(string ipAddressLocal, int iPortLocal, string ipAddressRemote, int iPortRemote, [Out] byte[] outDeviceID);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_Get_Callback_Image_Size(string sDeviceID, ref int iWidth, ref int iHeight, ref int iBytesOfPerPixel);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern SystemErrorCode YI_GetLastErrorCode();

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public static extern int YI_ImageReady_Callback_Register_Ex(string sDeviceID, ImageReadyCallbackFuncEx callBackFunc);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public static extern int YI_SystemInfo_Callback_Register_Ex(string sDeviceID, SystemInfoCallbackFuncEx callBackFunc);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Get_FPD_TYPE(string sDeviceID, ref int iType);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_Load_Defect_Tmp_File(string sDeviceID, string sPath);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_Load_Gain_Tmp_File(string sDeviceID, string sPath);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_Load_Offset_Tmp_File(string sDeviceID, string sPath);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_Set_Correct_Type(string sDeviceID, int type);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Subtract_Offset(string sDeviceID, bool bValue);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Get_Configure_SyncOut_Mode(string sDeviceID, int iSpeedMode, int iFPDMemoryType, ref YIConfigInfo_SyncOut configInfo);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Set_Configure_SyncOut_Mode(string sDeviceID, int iSpeedMode, int iFPDMemoryType, ref YIConfigInfo_SyncOut configInfo);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public static extern int YI_Upload_Correct_Files(string sDeviceID, int iMemoryType, string pFolderPath, ProgressCallbackFunc callBackFunc);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Set_Work_Mode(string sDeviceID, ModeType eModeType);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Capture_Prepare(string sDeviceID);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Capture_Image(string sDeviceID, int iNumber = 1);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Continual_Capture_Image(string sDeviceID);

        [DllImport("NiceVisionAPI.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int YI_FPD_Stop_Capture_Image(string sDeviceID);

        #endregion

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static event Action<byte[]> ImageRecv;

        static int iImageByteSize = 0;
        public static string deviceID = null;

        public static JArray status = new JArray();

        public static byte[] imageData = null;

        public static System.Timers.Timer timer = null;

        public static bool SystemInfoProcessCallbackFunc(int iCodeIndex, string sContent, string sDeviceID)
        {
            JObject newObject = new JObject
            {
                { "phase", 13 },
                { "code", iCodeIndex }
            };
            status.Add(newObject);
            Debug.WriteLine("{phase:13, code: " + iCodeIndex + "}");
            return true;
        }

        public static unsafe bool ImageDataRecvCallbackFunc(nint pImage, int iFlag, string sDeviceID)
        {
            if (pImage == nint.Zero || iImageByteSize <= 0) return false;

            imageData = new byte[iImageByteSize];

            // 将数据从非托管内存复制到托管内存
            Marshal.Copy(pImage, imageData, 0, iImageByteSize);

            ImageRecv?.Invoke(imageData);

            return true;
        }

        public static bool connect(string pcIp, int pcPort, string fpdIp, int fpdPort)
        {
            status.Clear();

            string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            string sDefectPath = Path.Combine(appDirectory, "staticConfig\\Defect.tf");
            string sGainPath = Path.Combine(appDirectory, "staticConfig\\Gain.tf");
            string sOffsetPath = Path.Combine(appDirectory, "staticConfig\\Offset.tf");


            // 创建一个字节数组来接收 outDeviceID
            byte[] outDeviceID = new byte[100];

            int result = 0;

            try
            {
                result = YI_Initialize_FPD_V4(pcIp, pcPort, fpdIp, fpdPort, outDeviceID);
            } catch {
                return false;
            }
            
            if (checkError(result, "YI_Initialize_FPD_V4"))
            {
                return false;
            }

            // 将字节数组转换为字符串
            deviceID = Encoding.Default.GetString(outDeviceID).TrimEnd('\0');

            result = YI_Get_Callback_Image_Size(deviceID, ref MainForm.DEVICE_WIDTH, ref MainForm.DEVICE_HEIGHT, ref MainForm.IBYTES_OF_PERPIXEL);
            if (checkError(result, "YI_Get_Callback_Image_Size"))
            {
                return false;
            }

            MainForm.IMAGE_WIDTH = MainForm.DEVICE_WIDTH;
            MainForm.IMAGE_HEIGHT = MainForm.DEVICE_HEIGHT;

            iImageByteSize = MainForm.DEVICE_WIDTH * MainForm.DEVICE_HEIGHT * MainForm.IBYTES_OF_PERPIXEL;

            YI_ImageReady_Callback_Register_Ex(deviceID, ImageDataRecvCallbackFunc);
            YI_SystemInfo_Callback_Register_Ex(deviceID, SystemInfoProcessCallbackFunc);

            int iFPDType = 0;
            result = YI_FPD_Get_FPD_TYPE(deviceID, ref iFPDType);

            if (checkError(result, "YI_FPD_Get_FPD_TYPE"))
            {
                return false;
            }

            if (iFPDType == 0)
            {
                result = YI_Load_Defect_Tmp_File(deviceID, sDefectPath);
                if (checkError(result, "YI_Load_Defect_Tmp_File"))
                {
                    return false;
                }

                result = YI_Load_Gain_Tmp_File(deviceID, sGainPath);
                if (checkError(result, "YI_Load_Gain_Tmp_File"))
                {
                    return false;
                }

                result = YI_Load_Offset_Tmp_File(deviceID, sOffsetPath);
                if (checkError(result, "YI_Load_Offset_Tmp_File"))
                {
                    return false;
                }

                int correctType = 0x01 | 0x04;
                result = YI_Set_Correct_Type(deviceID, correctType);
                if (checkError(result, "YI_Set_Correct_Type"))
                {
                    return false;
                }

                result = YI_FPD_Subtract_Offset(deviceID, true);
                if (checkError(result, "YI_FPD_Subtract_Offset"))
                {
                    return false;
                }
            }

            result = YI_FPD_Set_Work_Mode(deviceID, ModeType.Industry_Mode);
            if (checkError(result, "YI_FPD_Set_Work_Mode"))
            {
                return false;
            }

            return true;
        }

        public static bool prepare()
        {
            int result = YI_FPD_Capture_Prepare(deviceID);
            if (checkError(result, "YI_FPD_Capture_Prepare"))
            {
                return false;
            }
            return true;
        }

        public static bool capture()
        {
            int result = YI_FPD_Capture_Image(deviceID, 1);
            if (checkError(result, "YI_FPD_Capture_Image"))
            {
                return false;
            }
            return true;
        }

        public static bool checkError(int result, string phase)
        {
            if (result == 0)
            {
                int lastCode = (int)YI_GetLastErrorCode();

                JObject newObject = new JObject
                    {
                        { "phase", phase },
                        { "code", lastCode }
                    };

                status.Add(newObject);

                logger.Info("{phase:" + phase + ", code:" + lastCode + "}");

                return true;
            }

            return false;
        }

        public static JObject getLastError()
        {
            int lastCode = 0;
            string message = "";

            try
            {
                lastCode = (int)YI_GetLastErrorCode();
            }
            catch (Exception e) {
                lastCode = 1004;
            }

            switch (lastCode)
            {
                case 1:
                    message = "平板探测器正忙";
                    break;
                case 2:
                    message = "平板探测器反馈命令超时";
                    break;
                case 3:
                    message = "平板探测器未正常处理此命令";
                    break;
                case 4:
                    message = "平板探测器不支持此模式";
                    break;
                case 5:
                    message = "平板探测器反馈参数存在非法值";
                    break;
                case 6:
                    message = "当前模式不支持此功能";
                    break;
                case 100:
                    message = "软件未初始化";
                    break;
                case 101:
                    message = "软件重复初始化";
                    break;
                case 102:
                    message = "设备标识ID输入错误";
                    break;
                case 200:
                    message = "时序问题，如果收到此信息请尝试重新发送上一条命令";
                    break;
                case 201:
                    message = "发送命令失败";
                    break;
                case 202:
                    message = "命令校验码错误";
                    break;
                case 300:
                    message = "网线断开或者IP设置错误";
                    break;
                case 301:
                    message = "socket初始化失败";
                    break;
                case 1000:
                    message = "路径不对";
                    break;
                case 1001:
                    message = "函数的输入参数非法";
                    break;
                case 1002:
                    message = "读取信息失败";
                    break;
                case 1003:
                    message = "打开文件失败，文件不存在或无权限";
                    break;
                case 1004:
                    message = "缺少相关的DLL文件";
                    break;
                default:
                    message = "未知错误";
                    break;
            }

            JObject newObject = new JObject();

            if (status.Count > 0)
            {
                JObject state = (JObject)status[status.Count - 1];
                newObject = new JObject
                {
                    { "message", message },
                    { "code", lastCode },
                    { "phase", state.GetValue("phase")}
                };
            }
            else
            {
                newObject = new JObject
                {
                    { "message", message },
                    { "code", lastCode },
                    { "phase", ""}
                };
            }

            return newObject;
        }

    }
}
