﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Vintasoft.Imaging.Codecs.Encoders;
using Vintasoft.Imaging;
using Vintasoft.Imaging.UI;
using static System.Net.Mime.MediaTypeNames;
using FellowOakDicom.Imaging;
using InduVision;
using System.Text.RegularExpressions;
using LkControls.Utils;
using Vintasoft.Imaging.Codecs.ImageFiles.Dicom;

namespace LkControls.SetPanel
{
    public partial class EnhancePanel : UserControl
    {
        public static Mat enhanceMat = null;
        MainForm mainForm;
        SplitContainer splitContainer;
        int kernelSize;
        int sharpness;
        int edgeWeight;
        int trackBarClipLimit;
        int numericTileGridX;
        int numericTileGridY;

        public EnhancePanel(SplitContainer _splitContainer, MainForm _mainForm, int _kernelSize, int _sharpness, int _edgeWeight, int _trackBarClipLimit, int _numericTileGridX, int _numericTileGridY)
        {
            InitializeComponent();

            kernelSize = _kernelSize;
            sharpness = _sharpness;
            edgeWeight = _edgeWeight;
            trackBarClipLimit = _trackBarClipLimit;
            numericTileGridX = _numericTileGridX;
            numericTileGridY = _numericTileGridY;

            mainForm = _mainForm;
            splitContainer = _splitContainer;
        }

        private void closeButton_Click(object sender, EventArgs e)
        {
            splitContainer.Panel2Collapsed = true;

            splitContainer.SplitterDistance = splitContainer.Height;

            splitContainer.Panel2.Controls.Clear();
        }

        private void AutoWindowCheckBox_CheckedChanged(object sender, System.EventArgs e)
        {
            ApplyEnhance(gammaCheckBox.Checked, autoWindowCheckBox.Checked);
        }

        private void GammaCheckBox_CheckedChanged(object sender, System.EventArgs e)
        {
            ApplyEnhance(gammaCheckBox.Checked, autoWindowCheckBox.Checked);
        }

        public void ApplyEnhance(bool isGamma, bool isWindow)
        {
            if (enhanceMat != null)
            {
                enhanceMat.Dispose();
                enhanceMat = null;
            }

            enhanceMat = MainForm.RawMat.Clone();

            // 创建 Mat 容器
            //if (enhanceMat == null)
            //{
            //    VintasoftImage vintasoftImage = mainForm.getDicomViewerTool().DisplayedImage;
            //    ushort[,] pixelData = VintasoftImageToPixelData(vintasoftImage);

            //    int height = pixelData.GetLength(0);
            //    int width = pixelData.GetLength(1);

            //    if (vintasoftImage.BitsPerPixel == 8)
            //    {
            //        enhanceMat = new Mat(height, width, MatType.CV_8UC1);

            //        // 将 ushort[,] 转 byte[]，因为8位图像实际存储的是byte
            //        byte[] flat = new byte[height * width];

            //        for (int y = 0; y < height; y++)
            //        {
            //            for (int x = 0; x < width; x++)
            //            {
            //                flat[y * width + x] = (byte)pixelData[y, x];
            //            }
            //        }

            //        // 将数据复制到 Mat
            //        enhanceMat.SetArray(flat);
            //    }
            //    else if (vintasoftImage.BitsPerPixel == 16)
            //    {
            //        enhanceMat = new Mat(height, width, MatType.CV_16UC1);

            //        // 将 ushort[,] 转换为一维 ushort[]
            //        ushort[] flat = new ushort[height * width];
            //        Buffer.BlockCopy(pixelData, 0, flat, 0, flat.Length * sizeof(ushort));

            //        // 将数据复制到 Mat
            //        enhanceMat.SetArray(flat);
            //    }
            //    else
            //    {
            //        throw new NotSupportedException("Unsupported BitsPerPixel: " + vintasoftImage.BitsPerPixel);
            //    }
            //}

            //Mat mat = enhanceMat.Clone();

            int[,] kernelArray = new int[kernelSize, kernelSize];

            // 遍历矩阵
            for (int i = 0; i < kernelSize; i++)
            {
                for (int j = 0; j < kernelSize; j++)
                {
                    if (i == kernelSize / 2 && j == kernelSize / 2)
                    {
                        // 中心点：设为锐化强度
                        kernelArray[i, j] = sharpness;
                    }
                    else if (i == kernelSize / 2 || j == kernelSize / 2)
                    {
                        // 只修改十字方向上的边缘值
                        kernelArray[i, j] = edgeWeight;
                    }
                    else
                    {
                        // 其他点设为 0
                        kernelArray[i, j] = 0;
                    }
                }
            }

            // 计算卷积核的总和
            int sum = 0;
            foreach (int value in kernelArray)
            {
                sum += value;
            }

            // 进行归一化处理，确保卷积核总和为 1
            if (sum != 0)
            {
                double normalizeFactor = 1.0 / sum;
                for (int i = 0; i < kernelSize; i++)
                {
                    for (int j = 0; j < kernelSize; j++)
                    {
                        kernelArray[i, j] = (int)(kernelArray[i, j] * normalizeFactor);
                    }
                }
            }

            using (InputArray kernel = InputArray.Create<int>(kernelArray))
            {
                Cv2.Filter2D(enhanceMat, enhanceMat, enhanceMat.Type(), kernel);
            }

            // CLAHE 处理
            using (CLAHE clahe = Cv2.CreateCLAHE())
            {
                clahe.ClipLimit = (double)trackBarClipLimit;
                clahe.TilesGridSize = new OpenCvSharp.Size((int)numericTileGridX, (int)numericTileGridY);
                clahe.Apply(enhanceMat, enhanceMat);
            }

            if (isGamma)
            {
                // Gamma 校正
                double gamma = 0.3; // 小于1增强暗部, 大于1增强亮部
                enhanceMat.ConvertTo(enhanceMat, MatType.CV_32F);
                Cv2.Pow(enhanceMat / 65535.0, gamma, enhanceMat);
                enhanceMat = enhanceMat * 65535.0;
                enhanceMat.ConvertTo(enhanceMat, MatType.CV_16U);
            }

            // 第1步：窗宽窗位转换到8位图（线性拉伸）
            var (wc, ww) = Histogram.cal_win_level(enhanceMat);
            mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(wc, ww);
            Mat normalized8 = ConvertTo8BitUsingWindow(enhanceMat, (int)wc, (int)ww);

            // 第2步：拉普拉斯增强边缘
            Mat laplacian = new Mat();
            Cv2.Laplacian(normalized8, laplacian, MatType.CV_16S, ksize: 3);
            Cv2.ConvertScaleAbs(laplacian, laplacian);

            // 第3步：提取高灰度区域（即浅白色裂缝）
            Mat brightMask = new Mat();
            Cv2.InRange(normalized8, new Scalar(200), new Scalar(255), brightMask);

            // 第4步：提取边缘中的高亮结构
            Mat crackMask = new Mat();
            Cv2.BitwiseAnd(laplacian, brightMask, crackMask);

            // 第5步：增强裂缝区域
            Mat enhancedCracks = new Mat();
            Cv2.ConvertScaleAbs(crackMask, enhancedCracks, alpha: 2.0, beta: 0);

            // 第6步：与原图融合
            Mat result = new Mat();
            Cv2.AddWeighted(normalized8, 1.0, enhancedCracks, 1.0, 0, result);

            MainForm.ChangedRawMat = result;

            VintasoftImage image = mainForm.imageViewer1.Image;
            InduVision.LkControls.Utils.Utils.RenderImage(image, result);

            if (isWindow)
            {
                var (windowLevel, windowWidth) = Histogram.cal_win_level(enhanceMat);
                mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(windowLevel, windowWidth);
            }
            else
            {
                var (windowLevel, windowWidth, max, min) = Histogram.CalculateWindowLevelAndWidth(enhanceMat);
                mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(windowLevel, windowWidth);
            }

            //mainForm.getDicomViewerTool().UpdateImage();
        }

        public static Mat ConvertTo8BitUsingWindow(Mat src16, int center, int width)
        {
            int min = center - width / 2;
            int max = center + width / 2;

            Mat dst8 = new Mat();
            Cv2.Normalize(src16, dst8, 0, 255, NormTypes.MinMax, MatType.CV_8U);

            Mat maskMin = new Mat();
            Cv2.Compare(src16, min, maskMin, CmpType.LT);  // src16 < min

            Mat maskMax = new Mat();
            Cv2.Compare(src16, max, maskMax, CmpType.GT);  // src16 > max

            dst8.SetTo(0, maskMin);     // 把小于 min 的区域设为 0
            dst8.SetTo(255, maskMax);   // 把大于 max 的区域设为 255

            return dst8;
        }

        public ushort[,] VintasoftImageToPixelData(VintasoftImage image)
        {
            // get the PixelManipulator object
            Vintasoft.Imaging.PixelManipulator pixelManipulator = image.OpenPixelManipulator();
            // set the lock area to full image size
            System.Drawing.Rectangle lockRectangle =
                new System.Drawing.Rectangle(0, 0, image.Width, image.Height);
            // lock pixels for read and write
            pixelManipulator.LockPixels(lockRectangle, Vintasoft.Imaging.BitmapLockMode.ReadWrite);
            // remebmer the stride for performance purposes
            int width = image.Width;
            int height = image.Height;

            ushort[,] pixelData = new ushort[height, width];

            // Determine pixel format
            var pixelFormat = image.PixelFormat;

            // 计算每像素字节数
            int bytesPerPixel = image.BitsPerPixel / 8;

            for (int y = 0; y < height; y++)
            {
                byte[] row = pixelManipulator.ReadRowData(y);

                for (int x = 0; x < width; x++)
                {
                    int byteIndex = x * bytesPerPixel;

                    if (bytesPerPixel == 1)
                    {
                        // 8位图像
                        byte gray = row[byteIndex];
                        pixelData[y, x] = gray; // 直接转为 ushort
                    }
                    else if (bytesPerPixel == 2)
                    {
                        // 16位图像
                        ushort gray = BitConverter.ToUInt16(row, byteIndex);
                        pixelData[y, x] = gray;
                    }
                    else
                    {
                        throw new NotSupportedException($"Unsupported pixel format with {bytesPerPixel * 8} bits per pixel.");
                    }
                }
            }

            // unlock pixels
            pixelManipulator.UnlockPixels();
            // close PixelManipulator
            image.ClosePixelManipulator(true);

            return pixelData;
        }

    }
}
