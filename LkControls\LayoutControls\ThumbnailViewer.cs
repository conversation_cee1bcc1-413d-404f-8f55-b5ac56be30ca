﻿using DicomViewerDemo;
using InduVision;
using LkControls.CommonControls;
using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Resources;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Vintasoft.Imaging.UI;
using System.Runtime.InteropServices;

namespace LkControls.LayoutControls
{
    public class ThumbnailViewer : FlowLayoutPanel
    {


        private static readonly NLog.Logger Log = NLog.LogManager.GetLogger("ThumbnailViewer");

        public event Action<string> ImageSelected;

        private string currentFolderPath;

        private Panel selectedPanel;

        private int selectedIndex;

        MainForm mainForm;

        public MainForm MainForm
        {
            set
            {
                mainForm = value;
            }
        }

        public void LoadThumbnails(string folderPath)
        {
            currentFolderPath = folderPath;
            RefreshThumbnails();

            // 扫描DICOM文件并生成缩略图
            ScanAndGenerateThumbnails();
        }

        public void SelectThumbnailByIndex(int index)
        {
            if (index < 0 || index >= Controls.Count)
                return; // 防止索引越界

            Panel panel = Controls[index] as Panel;
            if (panel != null)
            {
                string imagePath = panel.Tag as string;
                if (!string.IsNullOrEmpty(imagePath))
                {
                    // 取消之前选中项的高亮边框
                    if (selectedPanel != null)
                    {
                        selectedPanel.Invalidate(); // 重新绘制旧的 Panel
                    }

                    // 设置新选中项
                    selectedPanel = panel;
                    selectedPanel.Invalidate(); // 重新绘制新的 Panel

                    ImageSelected?.Invoke(imagePath);
                }
            }
        }

        /// <summary>
        /// 重新加载缩略图列表（用于刷新）
        /// </summary>
        public void RefreshThumbnails()
        {
            if (string.IsNullOrEmpty(currentFolderPath) || !Directory.Exists(currentFolderPath))
                return;

            Controls.Clear(); // 清空当前列表

            var imageFiles = Directory.GetFiles(currentFolderPath, "*.*", SearchOption.TopDirectoryOnly)
                                      .Where(f => f.EndsWith(".jpg") || f.EndsWith(".png") || f.EndsWith(".bmp"))
                                      .OrderByDescending(f => File.GetCreationTime(f)) // 按创建时间升序排列
                                      .ToArray();

            for (int i = 0; i < imageFiles.Length; i++)
            {
                string file = imageFiles[i];

                string fileName = Path.GetFileNameWithoutExtension(file) + ".dcm";
                string imagePath = Path.Combine(Directory.GetParent(file).Parent.Parent.FullName, fileName);

                if (File.Exists(imagePath))
                {
                    Controls.Add(CreateThumbnailItem(file, i));
                }
                else
                {
                    File.Delete(file);
                }
            }
        }

        private Panel CreateThumbnailItem(string imagePath, int index)
        {
            string name = Path.GetFileNameWithoutExtension(imagePath);

            ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
            // 创建"刷新"菜单项
            ToolStripMenuItem refreshMenuItem = new ToolStripMenuItem("刷新");
            refreshMenuItem.Click += RefreshMenuItem_Click; // 点击刷新时的事件
            refreshMenuItem.Image = null;

            // 创建"删除"菜单项
            ToolStripMenuItem deleteMenuItem = new ToolStripMenuItem("删除");
            deleteMenuItem.Tag = imagePath;
            deleteMenuItem.Click += DeleteMenuItem_Click; // 点击删除时的事件
            deleteMenuItem.Image = null;

            // 创建"重命名"菜单项
            ToolStripMenuItem renameMenuItem = new ToolStripMenuItem("重命名");
            renameMenuItem.Tag = imagePath;
            renameMenuItem.Click += RenameMenuItem_Click; // 点击重命名时的事件
            renameMenuItem.Image = null;

            // 将菜单项添加到右键菜单
            contextMenuStrip.Items.Add(refreshMenuItem);
            contextMenuStrip.Items.Add(new ToolStripSeparator()); // 添加分隔线
            contextMenuStrip.Items.Add(deleteMenuItem);
            contextMenuStrip.Items.Add(renameMenuItem);

            // 创建容器 Panel
            Panel panel = new Panel
            {
                Size = new System.Drawing.Size(200, 200),
                Padding = new Padding(20, 20, 20, 20),
                BorderStyle = BorderStyle.FixedSingle,
                Tag = imagePath
            };
            panel.Paint += Panel_Paint;

            // 创建 PictureBox
            PictureBox pictureBox = new PictureBox
            {
                Size = new System.Drawing.Size(180, 120),
                SizeMode = PictureBoxSizeMode.Zoom,
                Dock = DockStyle.Top,
                Tag = imagePath,
                ContextMenuStrip = contextMenuStrip
            };

            using (FileStream fs = new FileStream(imagePath, FileMode.Open, FileAccess.Read))
            {
                pictureBox.Image = Image.FromStream(fs); // 使用文件流创建图片
            }

            pictureBox.Click += (sender, e) => PictureBox_Click(panel, imagePath, index);

            // 创建 Label
            Label label = new Label
            {
                ForeColor = Color.White,
                Text = Path.GetFileName(name),
                Dock = DockStyle.Bottom,
                TextAlign = ContentAlignment.MiddleCenter,
                AutoSize = false,
                Height = 25
            };

            // 组合控件
            panel.Controls.Add(pictureBox);
            panel.Controls.Add(label);

            return panel;
        }

        private void Panel_Paint(object sender, PaintEventArgs e)
        {
            Panel panel = sender as Panel;
            if (panel == null) return;

            // 判断当前 Panel 是否是选中的
            bool isSelected = panel == selectedPanel;

            // 获取边框颜色（选中时橙色，未选中时灰色）
            Color borderColor = isSelected ? Color.Orange : Color.Transparent;

            // 手动绘制边框
            using (Pen pen = new Pen(borderColor, 1)) // 3px 宽度
            {
                e.Graphics.DrawRectangle(pen, new Rectangle(0, 0, panel.Width - 3, panel.Height - 3));
            }
        }

        // 删除菜单项的点击事件
        private void DeleteMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show(
            "您确定要删除这张图像吗?",   // 提示信息
            "确认删除",                  // 标题
            MessageBoxButtons.YesNo,     // 显示"是"和"否"按钮
            MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                string thumbnailPath = ((ToolStripMenuItem)sender).Tag as string;
                if (!string.IsNullOrEmpty(thumbnailPath))
                {
                    mainForm.imageViewer1.Images.Remove(mainForm.imageViewer1.Image);

                    string fileName = Path.GetFileNameWithoutExtension(thumbnailPath) + ".dcm";
                    string imagePath = Path.Combine(Directory.GetParent(thumbnailPath).Parent.Parent.FullName, fileName);
                    if (File.Exists(thumbnailPath))
                    {
                        // 删除文件
                        File.Delete(thumbnailPath);
                    }

                    if (File.Exists(imagePath))
                    {
                        // 删除文件
                        File.Delete(imagePath);
                    }

                    RefreshThumbnails();
                    SelectThumbnailByIndex(0);
                }
            }

        }

        // 重命名菜单项的点击事件
        private void RenameMenuItem_Click(object sender, EventArgs e)
        {
            string thumbnailPath = ((ToolStripMenuItem)sender).Tag as string;
            string name = Path.GetFileNameWithoutExtension(thumbnailPath);
            string thumbnailDirectory = Path.GetDirectoryName(thumbnailPath);
            string rawDirectory = Directory.GetParent(thumbnailPath).Parent.Parent.FullName;
            string rawPath = Path.Combine(rawDirectory, name + ".dcm");
            using (RenameDialog renameDialog = new RenameDialog(name))
            {
                renameDialog.StartPosition = FormStartPosition.Manual;
                renameDialog.Location = Cursor.Position;
                if (renameDialog.ShowDialog() == DialogResult.OK)
                {
                    string newName = renameDialog.name;
                    string newThumbnailPath = Path.Combine(thumbnailDirectory, newName + ".jpg");
                    if (!File.Exists(newThumbnailPath))
                    {
                        File.Move(thumbnailPath, newThumbnailPath);
                        string newRawPath = Path.Combine(rawDirectory, newName + ".dcm");
                        File.Move(rawPath, newRawPath);

                        RefreshThumbnails();
                        SelectThumbnailByIndex(selectedIndex);
                    }
                    else
                    {
                        MessageBox.Show(
                       "该项目已经存在同名的文件，请检查后再进行该操作",
                       "操作异常",
                       MessageBoxButtons.OK,
                       MessageBoxIcon.Information);
                    }
                }
            }
        }
        /// <summary>
        /// 刷新菜单项的点击事件
        /// </summary>
        private void RefreshMenuItem_Click(object sender, EventArgs e)
        {
            // 强制扫描并生成缩略图
            ScanAndGenerateThumbnails();

        }



        private void PictureBox_Click(Panel panel, string imagePath, int index)
        {
            // 取消之前选中项的高亮边框
            if (selectedPanel != null)
            {
                selectedPanel.Invalidate(); // 重新绘制旧的 Panel
            }

            // 设置新选中项
            selectedPanel = panel;
            selectedPanel.Invalidate(); // 重新绘制新的 Panel
            selectedIndex = index;

            ImageSelected?.Invoke(imagePath);
        }


        /// <summary>
        /// 扫描项目目录中的DICOM文件并生成缩略图
        /// </summary>
        public void ScanAndGenerateThumbnails()
        {
            if (string.IsNullOrEmpty(currentFolderPath) || !Directory.Exists(currentFolderPath))
                return;

            try
            {
                // 获取项目根目录（缩略图文件夹的上两级目录）
                string projectRoot = Directory.GetParent(currentFolderPath).Parent.FullName;

                // 获取所有DICOM文件
                var dicomFiles = Directory.GetFiles(projectRoot, "*.dcm", SearchOption.TopDirectoryOnly);

                // 对于每个DICOM文件，检查是否有对应的缩略图，如果没有则生成
                foreach (var dicomFile in dicomFiles)
                {
                    string fileName = Path.GetFileNameWithoutExtension(dicomFile);
                    string thumbnailPath = Path.Combine(currentFolderPath, fileName + ".jpg");
                    // 如果缩略图不存在，生成它
                    if (!File.Exists(thumbnailPath))
                    {
                        try
                        {
                            GenerateThumbnailForDicomFile(dicomFile, thumbnailPath);
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但继续处理其他文件
                            Log.Error($"生成缩略图失败: {ex.Message}");
                            Log.Error(ex.StackTrace);
                        }
                    }
                }

                // 完成后刷新显示
                RefreshThumbnails();

            }
            catch (Exception ex)
            {
                // 处理整体异常
                Log.Error($"扫描DICOM文件失败: {ex.Message}");
                Log.Error(ex.StackTrace);

            }
        }

        /// <summary>
        /// 为DICOM文件生成缩略图
        /// </summary>
        private void GenerateThumbnailForDicomFile(string dicomFilePath, string thumbnailPath)
        {
            var dicomFile = FellowOakDicom.DicomFile.Open(dicomFilePath);

            if (!dicomFile.Dataset.Contains(FellowOakDicom.DicomTag.Columns) ||
                !dicomFile.Dataset.Contains(FellowOakDicom.DicomTag.Rows) ||
                !dicomFile.Dataset.Contains(FellowOakDicom.DicomTag.PixelData))
            {
                Log.Error($"DICOM文件缺少必要的标签: {dicomFilePath}");
                return;
            }


            // 获取图像尺寸
            int width = dicomFile.Dataset.GetSingleValue<int>(FellowOakDicom.DicomTag.Columns);
            int height = dicomFile.Dataset.GetSingleValue<int>(FellowOakDicom.DicomTag.Rows);

            // 获取像素数据
            byte[] rawData = dicomFile.Dataset.GetValues<byte>(FellowOakDicom.DicomTag.PixelData);

            // 创建Mat对象
            using (Mat rawMat = new Mat(height, width, MainForm.IMAGE_MATTYPE))
            {
                // 复制数据到Mat
                int bytesPerPixel = (MainForm.IMAGE_MATTYPE == MatType.CV_16U) ? 2 : 1;
                int length = height * width * bytesPerPixel;

                Marshal.Copy(rawData, 0, rawMat.Data, Math.Min(rawData.Length, length));

                // 计算新的宽度和高度
                int newWidth = (int)(width * 0.1);
                int newHeight = (int)(height * 0.1);

                using (Mat thumbnailMat = new Mat())
                {
                    // 将16位灰度图转换为8位灰度图
                    rawMat.ConvertTo(thumbnailMat, MatType.CV_8U, 255.0 / 65535.0);

                    // 调整大小
                    Cv2.Resize(thumbnailMat, thumbnailMat, new OpenCvSharp.Size(newWidth, newHeight));

                    // 编码为JPEG
                    byte[] jpegData;
                    Cv2.ImEncode(".jpg", thumbnailMat, out jpegData);

                    // 保存文件
                    File.WriteAllBytes(thumbnailPath, jpegData);
                }
            }
        }
    }

}
