﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LkControls.CommonControls
{
    public partial class RenameDialog : Form
    {
        public string name = String.Empty;
        public RenameDialog(string _name)
        {
            InitializeComponent();
            name = _name;
            textBox1.Text = name;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            name = textBox1.Text;

            // 获取无效的Windows文件名字符
            char[] invalidChars = Path.GetInvalidFileNameChars();

            if (string.IsNullOrEmpty(name))
            {
                MessageBox.Show("档案名称不能为空.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);

            }
            else if (name.Any(ch => invalidChars.Contains(ch)))
            {
                MessageBox.Show("档案名称不能包含特殊的字符.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);

            }
            else if (!IsChineseAlphanumeric(name))
            {
                MessageBox.Show("档案名称仅包含中文字符、字母、数字以及_-#@+符号", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);

            }
            else {
                DialogResult = DialogResult.OK;

                Close();
            }

            
        }

        private static bool IsChineseAlphanumeric(string input)
        {
            // 正则表达式：中文字符、字母（包括大小写）、数字
            string pattern = @"^[\u4e00-\u9fa5a-zA-Z0-9_\-#@+~]+$";
            return Regex.IsMatch(input, pattern);
        }
    }
}
