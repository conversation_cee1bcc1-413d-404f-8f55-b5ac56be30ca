﻿using InduVision;
using LkControls.Utils;
using OpenCvSharp;
using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using Vintasoft.Imaging;
using Vintasoft.Imaging.Annotation.Dicom.UI.VisualTools;
using Vintasoft.Imaging.UI;
using Vintasoft.Imaging.Annotation.Measurements;
using System.Threading;

namespace LkControls.LayoutControls
{
    public partial class LeftPanel : UserControl
    {
        MainForm mainForm;
        public static string selectedFileName;

        public LeftPanel()
        {
            InitializeComponent();
        }

        public MainForm MainForm
        {
            set
            {
                mainForm = value;
                fpdPanel1.setMainForm(value);
                thumbnailViewer1.MainForm = value;
            }
        }

        private void ThumbnailViewer1_ImageSelected(string thumbnailPath)
        {
            mainForm.toolsPanel.reset();
            mainForm.toolsPanel.activedButton = null;

            if (mainForm.imageViewer1.Image != null && mainForm.imageViewer1.Image.Width < 100)
            {
                mainForm.imageViewer1.VisualTool = mainForm.getDicomAnnotatedViewerTool();
                mainForm.imageViewer1.Images.Remove(mainForm.imageViewer1.Image);
            }

            ImageViewer imageViewer1 = mainForm.getImageViewer1();

            string name = Path.GetFileNameWithoutExtension(thumbnailPath);

            MainForm.currentFileName = name;

            string fileName = name + ".dcm";
            string imagePath = Path.Combine(Directory.GetParent(thumbnailPath).Parent.Parent.FullName, fileName);

            selectedFileName = imagePath;

            var dicomFile1 = FellowOakDicom.DicomFile.Open(imagePath);

            MainForm.IMAGE_WIDTH = dicomFile1.Dataset.GetSingleValue<int>(FellowOakDicom.DicomTag.Columns);
            MainForm.IMAGE_HEIGHT = dicomFile1.Dataset.GetSingleValue<int>(FellowOakDicom.DicomTag.Rows);

            // 获取像素数据
            byte[] rawData = dicomFile1.Dataset.GetValues<byte>(FellowOakDicom.DicomTag.PixelData);

            Mat rawMat = new Mat(MainForm.IMAGE_HEIGHT, MainForm.IMAGE_WIDTH, MainForm.IMAGE_MATTYPE);

            int bytesPerPixel = (MainForm.IMAGE_MATTYPE == MatType.CV_16U) ? 2 : 1;
            int length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH * bytesPerPixel;

            IntPtr ptr = rawMat.Data;  // 获取 Mat 的数据指针
            Marshal.Copy(rawData, 0, ptr, rawData.Length);

            Rect roi = new Rect(MainForm.CROPWIDTH, 0, rawMat.Cols - MainForm.CROPWIDTH, rawMat.Rows);

            // 使用矩形区域来裁剪图像
            using (Mat croppedMat = new Mat(rawMat, roi))
            {
                rawMat.Dispose();
                rawMat = croppedMat.Clone();  // Clone() 创建一个新的 Mat，确保 croppedMat 在 using 结束后不会释放 rawMat
            }

            MatProcesser.RotateImage(rawMat);

            if (MainForm.RawMat != null)
            {
                MainForm.RawMat.Dispose();
                MainForm.RawMat = null;
            }

            if (MainForm.ChangedRawMat != null)
            {
                MainForm.ChangedRawMat.Dispose();
                MainForm.ChangedRawMat = null;
            }

            MainForm.RawMat = rawMat;
            MainForm.ChangedRawMat = rawMat;


            if (mainForm.imageViewer1.Image == null || mainForm.imageViewer1.Image.Width < 100 || mainForm.imageViewer1.Image.Width != rawMat.Width)
            {
                byte[] imageData1;
                Cv2.ImEncode(".tiff", rawMat, out imageData1, new int[] { (int)ImwriteFlags.TiffCompression, 1 });
                MemoryStream memoryStream1 = new MemoryStream(imageData1);
                VintasoftImage vintasoftImage = new VintasoftImage(memoryStream1, true);
                vintasoftImage.Resolution = new Vintasoft.Imaging.Resolution(250f, 250f);
                imageViewer1.Image = vintasoftImage;
            }
            else
            {
                Vintasoft.Imaging.VintasoftImage image = mainForm.imageViewer1.Image;
                InduVision.LkControls.Utils.Utils.RenderImage(image, rawMat);
            }

            if (MainForm.lockVoiLut == false)
            {
                var (windowLevel, windowWidth, maxGrayValue, minGrayValue) = Histogram.CalculateWindowLevelAndWidth(rawMat);
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(windowLevel, windowWidth);
                mainForm.rawMatMaxGrayValue = maxGrayValue;
                mainForm.rawMatMinGrayValue = minGrayValue;
            }
            else
            {
                mainForm.getDicomViewerTool().UpdateImage();
            }

            mainForm.nsnrStripStatusLabel1.Text = "";

            
            if (imageViewer1.Zoom > 1000)
            {
                imageViewer1.Zoom = 32;
            }

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = mainForm.getDicomAnnotatedViewerTool();
            DicomAnnotationTool dicomAnnotationTool = dicomAnnotatedViewerTool.DicomAnnotationTool;
            if (dicomAnnotationTool.AnnotationViewCollection.Count > 0)
            {
                dicomAnnotationTool.AnnotationViewCollection.Clear();
                dicomAnnotationTool.AnnotationDataCollection.Clear();
            }
            ImageMeasureTool imageMeasureTool = dicomAnnotatedViewerTool.ImageMeasureTool;
            if (imageMeasureTool.AnnotationViewCollection.Count > 0)
            {
                imageMeasureTool.AnnotationViewCollection.Clear();
            }
        }

        private void CustomTabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (customTabControl1.SelectedIndex == 1) {
                if (mainForm.leftPanel.fpdPanel1.deviceType == 1 && mainForm.leftPanel.fpdPanel1.DynamicIsStart())
                {
                    mainForm.leftPanel.fpdPanel1.DynamicStop();

                    MainForm.canRefreshImage = false;
                    mainForm.toolsPanel.measureTitlePanel.Visible = true;
                    mainForm.toolsPanel.measureButtonPanel.Visible = true;
                    mainForm.toolsPanel.annotationTitlePanel.Visible = true;
                    mainForm.toolsPanel.annotationButtonPanel.Visible = true;
                    mainForm.toolsPanel.WallThicknessBtn.Visible = true;
                    mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = true;
                    mainForm.toolsPanel.enhancementTitlePanel.Visible = true;
                    mainForm.toolsPanel.enhancementButtonPanel.Visible = true;

                    Thread.Sleep(200);
                } else if (mainForm.leftPanel.fpdPanel1.deviceType == 0 && mainForm.leftPanel.fpdPanel1.StaticIsCapturing())
                {
                    mainForm.leftPanel.fpdPanel1.StaticStop();

                    MainForm.canRefreshImage = false;
                    mainForm.toolsPanel.measureTitlePanel.Visible = true;
                    mainForm.toolsPanel.measureButtonPanel.Visible = true;
                    mainForm.toolsPanel.annotationTitlePanel.Visible = true;
                    mainForm.toolsPanel.annotationButtonPanel.Visible = true;
                    mainForm.toolsPanel.WallThicknessBtn.Visible = true;
                    mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = true;
                    mainForm.toolsPanel.enhancementTitlePanel.Visible = true;
                    mainForm.toolsPanel.enhancementButtonPanel.Visible = true;

                    Thread.Sleep(200);
                }

                mainForm.leftPanel.thumbnailViewer1.RefreshThumbnails();
                mainForm.leftPanel.thumbnailViewer1.SelectThumbnailByIndex(0);
            } else if (customTabControl1.SelectedIndex == 0) {
                MainForm.currentFileName = "";
                mainForm.toolsPanel.reset();

                if (mainForm.leftPanel.fpdPanel1.deviceType == 1 && mainForm.leftPanel.fpdPanel1.DynamicIsStart())
                {
                    MainForm.canRefreshImage = true;
                    mainForm.toolsPanel.measureTitlePanel.Visible = false;
                    mainForm.toolsPanel.measureButtonPanel.Visible = false;
                    mainForm.toolsPanel.annotationTitlePanel.Visible = false;
                    mainForm.toolsPanel.annotationButtonPanel.Visible = false;
                    mainForm.toolsPanel.WallThicknessBtn.Visible = false;
                    mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = false;
                    mainForm.toolsPanel.enhancementTitlePanel.Visible = false;
                    mainForm.toolsPanel.enhancementButtonPanel.Visible = false;
                } else if (mainForm.leftPanel.fpdPanel1.deviceType == 0 && mainForm.leftPanel.fpdPanel1.StaticIsCapturing())
                {
                    MainForm.canRefreshImage = true;
                    mainForm.toolsPanel.measureTitlePanel.Visible = false;
                    mainForm.toolsPanel.measureButtonPanel.Visible = false;
                    mainForm.toolsPanel.annotationTitlePanel.Visible = false;
                    mainForm.toolsPanel.annotationButtonPanel.Visible = false;
                    mainForm.toolsPanel.WallThicknessBtn.Visible = false;
                    mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = false;
                    mainForm.toolsPanel.enhancementTitlePanel.Visible = false;
                    mainForm.toolsPanel.enhancementButtonPanel.Visible = false;
                }

            }
        }

    }
}
