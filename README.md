# .NET Reactor 路径配置指南

## 背景

在我们的项目中，我们使用了 .NET Reactor 作为代码保护工具。之前，该工具的路径是硬编码在 `InduVision.csproj` 文件中的，这导致了以下问题：

- 不同开发人员的计算机上，.NET Reactor 可能安装在不同的位置
- 当通过 git 同步代码时，硬编码的路径可能导致构建失败
- 每个开发人员需要手动修改项目文件，增加出错的可能性

## 新的配置方法

我们实现了一个更灵活的配置方法，使每个开发人员可以指定自己机器上的 .NET Reactor 路径，而不影响其他人。

### 如何设置

1. 在项目根目录下创建一个名为 `reactor.path.user` 的文件
2. 在此文件中输入您机器上 .NET Reactor 控制台程序的完整路径，例如：
   ```
   D:\Eziriz\.NET Reactor\dotNET_Reactor.Console.exe
   ```
3. 确保将 `reactor.path.user` 添加到 `.gitignore` 文件中，以防止它被提交到版本控制系统

### 工作原理

1. 项目文件 (`InduVision.csproj`) 已被修改，它会首先检查 `reactor.path.user` 文件是否存在
2. 如果文件存在，将从该文件读取路径
3. 如果文件不存在，将使用默认的硬编码路径（作为后备选项）

### 示例

```xml
<PropertyGroup>
  <ReactorLocation Condition="Exists('$(MSBuildProjectDirectory)\reactor.path.user')">$([System.IO.File]::ReadAllText('$(MSBuildProjectDirectory)\reactor.path.user').Trim())</ReactorLocation>
  <ReactorLocation Condition="!Exists('$(MSBuildProjectDirectory)\reactor.path.user')">D:\Eziriz\.NET Reactor\dotNET_Reactor.Console.exe</ReactorLocation>
</PropertyGroup>
```

## 常见问题

**问：我需要为每个项目创建一个 `reactor.path.user` 文件吗？**

答：是的，每个使用 .NET Reactor 的项目都需要单独的配置文件。

**问：如果我更新了 .NET Reactor 的安装路径，需要做什么？**

答：只需更新 `reactor.path.user` 文件中的路径即可。

**问：为什么我的项目无法找到 .NET Reactor？**

答：请检查：
- `reactor.path.user` 文件是否存在于项目根目录
- 文件中的路径是否正确
- 路径中是否有多余的空格或换行符 