﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADMAAAAwCAYAAAC8NUKEAAAABGdBTUEAALGPC/xhBQAAAZpJREFUaEPt
        meGxgyAQhC0lpaQUO0kpKSWlpBTfLpwO8kQDelzC8M3cjzB4t4snQ3S4gmmabogXogRed5dU9kDMw8kq
        5yWp7KEYr2nhiaDBvXgjFiRVXVCXLXUPIm6xm0w9BHMXQzLEsVVuGb4eJkfEcJVnM2+Z+hGYzzvokN/x
        HdZrPyTnasVQ0FVmVq0HsvJlgeTdTArMNzVz9MyQnA3AzgxBgXmncSFjoZnRTTwA81YLE4yF+fV2sxQo
        Gpoh/M1V34sVksoeaGGrnUG3nXKAGLYHV5s9nxu87nvOZltQ4E7UfxZKgdh/z0TE9xwsj6BYrzlJN2MC
        xXrNSboZEyjWa07SzZhAsV5zkm7GBIr1mpN0MyZQrNecpJsxAWLbOWgSCN46+ruQKdcjBeZXpr8cI800
        A83wr2sTzG02thDy5OQjCbZ692zUfT+AgjSiRd1tGwU1zdR9h4aCTZnhpqFF9TZryszWp42reEqZOqBg
        O2aIFNbgISXqIYU16GZOgaJaB9TyM1YpKNrNfED9r2goyhOuBoWn5mH4A1xRuRzcTl2PAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="pictureBox2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADEAAAAwCAYAAAC4wJK5AAAABGdBTUEAALGPC/xhBQAAAe5JREFUaEPt
        mouNgzAQRFPKlXKlpJOUklJSSkoh83bXxhCTj5RgiPykVdZmMTNgbKS7AwzD8K+4KmqcragBuvbRJdxx
        URyjLBt4xDVKV0fXRuwjTrXCswL3GLOwooaUWiLQmGbNJRUlmk2bd0GrSx6G1JFoftdfBa0uuZtoC1pd
        cjfRFrS65NGEvenW2BGSzDLrS+wSKpi6FUo5MT8x5ewpeSDlJeOOKtSujTfZeZVTkzdXcgVMxnoZndhN
        iG4CyBXQTRjRlQbtJiC66OsmnqITuwnR3gTo5D8imtaONDM/Pj+nZH6sVjc/Xqv5TeSUR8hH4B7Dp5mS
        NO92STLBkzjtNPIi89vgtHSr/G61mB23+ojayvPueByvjvUScbIRXfT1zc67u4n30IndhOgmgFwBHzXB
        BpOXO+XUlCLKjWi+dLJc2rHooo+bUI5nNdGk/XkTa6NLf+VJ2J1X2N3jN9rlFHtaA7Sj/9FYXzHBHAYb
        lF9vLs9jfr05vjug9lLd19+JbZpQAd/liJu8iFtHejHof6JTkpjM2S2DVpccsyRy6CbWBK0uuZtoC1pd
        8mgiLWN7+mM8ny9G6kgmgOUWl2U0XXoretCY8D1FSXa1QN581kbXLgXXyB+LFLNb8kTKp5Jo+a9C3OCa
        JvriHT4cbkPAU2hL5wabAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="pictureBox3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAfNJREFUaEPt
        mYuNwyAQRF1KSkkp6SSlpJSUcCWkFGdmF6INIjYQ2PPpeNLKuWPBM5iPhZd/w7quJ8TVOS6IM+IUZNQT
        GjoCD8QlyNoHyezxO2sejGuQuA0SU/HsgfQRj44bIteJ5yAzDxJY2VL+6AaA+3M0sAMjj1CUJyRFtt06
        AR00YclPbBRw1ke2nToDPXY45TsWBVy6Irfw70MAPXZo5yczC7RcKJvxTlCPyhKmAXeoR2UJ04CAHLtq
        tVK02iFviIFerxu7L2zIGWLALrut3ENzmyCvvwFPqEdlCdOAO9SjsoRpwB3qUVnCNCAgZ25kgbmRCSzQ
        cmHXgCfUo7KEacAd6lFZwjTgDvWoLGEaEJCzt5F1O1tCW0MMlGxkRcfjyGNn8DyUYY8Sc+SPO1FQa2Bv
        IyvdpCi6hj4GeoD71Ir/3CkodDWAe6RziEOST1WGHa65Ofb5wBmF3gbsHHrrWfydiue8qPo24GHA8prs
        +F0vniDJzQDat2f+r+UWv9vEEyT+qgFc28UTJks1pWgJbAXtv90L8Z14ggrpp5xh38fQthX8E66RevER
        VLTDiA0NGUpoN+3xCO9ZtHtnYWVE+orARrnh0FyvyG1gfd6d0BBNULQn3V78BDRIE+wpD8YtGGicRhjc
        4tNh0CPaJuvfZ1meQYaOFriLRAIAAAAASUVORK5CYII=
</value>
  </data>
  <data name="pictureBox4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAZdJREFUaEPt
        mY2NgzAMhRmlo3SUbtJRbpQbpaNw79lO5bsmcCUQEtWfZJXm970QImGmIAjyzPN82xBX634eEHFH1HC3
        odrDyVVDFd82HMe78L+U1rO+MNYw8YXIbZNSPBCJi43H8r14iMgSaMDVSry9DdhHuwrPZwHXLOdi1Mby
        84UGfrVuVvxv0OeqXYW3+1fDSXVuYZMA9Evb6PkcNAOT7mmA5LbB1uA2lOeqCBrsYYCTHUlZFyu1jbDV
        AA8CfxeOIH/AoKLawFFAD7fQyzH9CxR2a4BAkz+mhzSwrI+FWieEgb2hJpUmhIHmUJNKE8JAc6hJpQlh
        oDnUpNKEMNAcalJpQhhoDjWpNGEMA9DB94tchmMYA3wHGNeAh5pUmjCOAWgZegtJUsyuhzQgaRW7DgPN
        gQ6eQpJGoSYKM843gDmYS02Zt1ykNIokdfHbnQHOlxPuw2e5Vw347PJ5X1kKQJP/UPKaFyKo8NkvHmF0
        3UN48eUPHaj02a9eWf3Q8TcP2Qu8C8viPWyMyN3OMyK/54Pg45mmH2mAREp6nsc2AAAAAElFTkSuQmCC
</value>
  </data>
</root>