using System;
using System.Windows.Forms;
using System.Drawing;

namespace InduVision.LicenseRegistration
{
    partial class LicenseRegisterForm
    {
        // 控件定义
        private System.Windows.Forms.Label labelTitle;
        private System.Windows.Forms.Label labelStatus;
        private System.Windows.Forms.Label labelHardwareID;
        private System.Windows.Forms.TextBox txtMachineCode;
        private System.Windows.Forms.Button btnCopyHardwareID;
        private System.Windows.Forms.PictureBox pictureBoxQRCode;
        private System.Windows.Forms.Label labelFilePath;
        private System.Windows.Forms.TextBox textBoxFilePath;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Button btnRegister;
        private System.Windows.Forms.Button btnAbout;
        private System.Windows.Forms.ToolTip toolTip;

        private void LoadMachineInfo()
        {
            try
            {
                txtMachineCode.Text = _licenseCheckService.GetMachineCode();
                _log.Info("获取机器码: {0}", txtMachineCode.Text);
            }
            catch (Exception ex)
            {
                _log.Error(ex, "获取机器码失败");
                MessageBox.Show("获取机器码失败: " + ex.Message, "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.toolTip = new System.Windows.Forms.ToolTip();
            labelTitle = new Label();
            labelStatus = new Label();
            labelHardwareID = new Label();
            txtMachineCode = new TextBox();
            btnCopyHardwareID = new Button();
            pictureBoxQRCode = new PictureBox();
            labelFilePath = new Label();
            textBoxFilePath = new TextBox();
            btnImport = new Button();
            btnRegister = new Button();
            btnAbout = new Button();
            ((System.ComponentModel.ISupportInitialize)(pictureBoxQRCode)).BeginInit();
            SuspendLayout();
            // 
            // labelTitle
            // 
            labelTitle.AutoSize = true;
            labelTitle.Font = new Font("宋体", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            labelTitle.Location = new System.Drawing.Point(14, 13);
            labelTitle.Name = "labelTitle";
            labelTitle.Size = new System.Drawing.Size(109, 16);
            labelTitle.TabIndex = 0;
            labelTitle.Text = "软件注册界面";
            // 
            // labelStatus
            // 
            labelStatus.AutoSize = true;
            labelStatus.Font = new Font("宋体", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            labelStatus.ForeColor = Color.Red;
            labelStatus.Location = new System.Drawing.Point(14, 57);
            labelStatus.Name = "labelStatus";
            labelStatus.Size = new System.Drawing.Size(147, 14);
            labelStatus.TabIndex = 1;
            labelStatus.Text = "请选择授权文件进行注册";
            // 
            // labelHardwareID
            // 
            labelHardwareID.AutoSize = true;
            labelHardwareID.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point, 134);
            labelHardwareID.Location = new System.Drawing.Point(14, 112);
            labelHardwareID.Name = "labelHardwareID";
            labelHardwareID.Size = new System.Drawing.Size(55, 16);
            labelHardwareID.TabIndex = 2;
            labelHardwareID.Text = "硬件码";
            // 
            // txtMachineCode
            // 
            txtMachineCode.Font = new Font("宋体", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtMachineCode.Location = new System.Drawing.Point(14, 148);
            txtMachineCode.Name = "txtMachineCode";
            txtMachineCode.ReadOnly = true;
            txtMachineCode.Size = new System.Drawing.Size(350, 23);
            txtMachineCode.TabIndex = 3;
            // 
            // btnCopyHardwareID
            // 
            btnCopyHardwareID.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnCopyHardwareID.Location = new System.Drawing.Point(370, 148);
            btnCopyHardwareID.Name = "btnCopyHardwareID";
            btnCopyHardwareID.Size = new System.Drawing.Size(52, 23);
            btnCopyHardwareID.TabIndex = 4;
            btnCopyHardwareID.Text = "复制";
            btnCopyHardwareID.UseVisualStyleBackColor = true;
            btnCopyHardwareID.Click += new System.EventHandler(this.btnCopyHardwareID_Click);
            // 
            // pictureBoxQRCode
            // 
            pictureBoxQRCode.Location = new System.Drawing.Point(436, 13);
            pictureBoxQRCode.Name = "pictureBoxQRCode";
            pictureBoxQRCode.Size = new System.Drawing.Size(110, 110);
            pictureBoxQRCode.TabIndex = 5;
            pictureBoxQRCode.TabStop = false;
            pictureBoxQRCode.SizeMode = PictureBoxSizeMode.StretchImage;
            // 
            // labelFilePath
            // 
            labelFilePath.AutoSize = true;
            labelFilePath.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point, 134);
            labelFilePath.Location = new System.Drawing.Point(14, 184);
            labelFilePath.Name = "labelFilePath";
            labelFilePath.Size = new System.Drawing.Size(103, 16);
            labelFilePath.TabIndex = 6;
            labelFilePath.Text = "选择注册文件";
            // 
            // textBoxFilePath
            // 
            textBoxFilePath.Font = new Font("宋体", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            textBoxFilePath.Location = new System.Drawing.Point(14, 212);
            textBoxFilePath.Name = "textBoxFilePath";
            textBoxFilePath.Size = new System.Drawing.Size(408, 23);
            textBoxFilePath.TabIndex = 7;
            // 
            // btnImport
            // 
            btnImport.Location = new System.Drawing.Point(436, 212);
            btnImport.Name = "btnImport";
            btnImport.Size = new System.Drawing.Size(88, 33);
            btnImport.TabIndex = 8;
            btnImport.Text = "浏览...";
            btnImport.UseVisualStyleBackColor = true;
            // 
            // btnRegister
            // 
            btnRegister.Font = new Font("宋体", 10F, FontStyle.Bold, GraphicsUnit.Point, 134);
            btnRegister.Location = new System.Drawing.Point(436, 260);
            btnRegister.Name = "btnRegister";
            btnRegister.Size = new System.Drawing.Size(107, 35);
            btnRegister.TabIndex = 9;
            btnRegister.Text = "注册";
            btnRegister.UseVisualStyleBackColor = true;
            // 
            // btnAbout
            // 
            btnAbout.Font = new Font("宋体", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnAbout.Location = new System.Drawing.Point(14, 260);
            btnAbout.Name = "btnAbout";
            btnAbout.Size = new System.Drawing.Size(107, 35);
            btnAbout.TabIndex = 10;
            btnAbout.Text = "关于授权";
            btnAbout.UseVisualStyleBackColor = true;
            // 
            // LicenseRegisterForm
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new System.Drawing.Size(565, 315);
            Controls.Add(btnAbout);
            Controls.Add(btnRegister);
            Controls.Add(btnImport);
            Controls.Add(textBoxFilePath);
            Controls.Add(labelFilePath);
            Controls.Add(pictureBoxQRCode);
            Controls.Add(btnCopyHardwareID);
            Controls.Add(txtMachineCode);
            Controls.Add(labelHardwareID);
            Controls.Add(labelStatus);
            Controls.Add(labelTitle);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            Name = "LicenseRegisterForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "InduVision | 软件注册";
            ((System.ComponentModel.ISupportInitialize)(pictureBoxQRCode)).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }
    }
}
