﻿using LkControls.common;

namespace InduVision.LkControls.SetPanel
{
    partial class ExportImagePanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            label1 = new System.Windows.Forms.Label();
            formatCombox = new System.Windows.Forms.ComboBox();
            label2 = new System.Windows.Forms.Label();
            numberCombox = new System.Windows.Forms.ComboBox();
            applyButton = new RoundButton();
            groupBox1 = new System.Windows.Forms.GroupBox();
            closeButton = new RoundButton();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.ForeColor = System.Drawing.Color.White;
            label1.Location = new System.Drawing.Point(14, 29);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(69, 20);
            label1.TabIndex = 0;
            label1.Text = "图片格式";
            // 
            // formatCombox
            // 
            formatCombox.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
            formatCombox.FormattingEnabled = true;
            formatCombox.Items.AddRange(new object[] { "TIFF", "JPEG", "PNG", "RAW" });
            formatCombox.Location = new System.Drawing.Point(89, 26);
            formatCombox.Name = "formatCombox";
            formatCombox.Size = new System.Drawing.Size(263, 31);
            formatCombox.TabIndex = 1;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.ForeColor = System.Drawing.Color.White;
            label2.Location = new System.Drawing.Point(15, 76);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(69, 20);
            label2.TabIndex = 2;
            label2.Text = "导出文件";
            // 
            // numberCombox
            // 
            numberCombox.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
            numberCombox.FormattingEnabled = true;
            numberCombox.Items.AddRange(new object[] { "当前文件", "全部文件" });
            numberCombox.Location = new System.Drawing.Point(90, 71);
            numberCombox.Name = "numberCombox";
            numberCombox.Size = new System.Drawing.Size(262, 31);
            numberCombox.TabIndex = 3;
            // 
            // applyButton
            // 
            applyButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            applyButton.BorderRadius = 8;
            applyButton.BorderSize = 1;
            applyButton.ButtonTextColor = System.Drawing.Color.White;
            applyButton.CustomEnabled = true;
            applyButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            applyButton.FlatAppearance.BorderSize = 0;
            applyButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            applyButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            applyButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            applyButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            applyButton.IconImage = null;
            applyButton.IconSize = 60;
            applyButton.IconVisible = true;
            applyButton.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            applyButton.IsPressed = true;
            applyButton.Location = new System.Drawing.Point(16, 124);
            applyButton.Name = "applyButton";
            applyButton.NormalBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.NormalColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.Size = new System.Drawing.Size(162, 46);
            applyButton.TabIndex = 23;
            applyButton.Text = "导出";
            applyButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            applyButton.UseVisualStyleBackColor = false;
            applyButton.Click += applyButton_Click;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(closeButton);
            groupBox1.Controls.Add(formatCombox);
            groupBox1.Controls.Add(applyButton);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(numberCombox);
            groupBox1.Controls.Add(label2);
            groupBox1.ForeColor = System.Drawing.Color.White;
            groupBox1.Location = new System.Drawing.Point(9, 3);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new System.Drawing.Size(367, 190);
            groupBox1.TabIndex = 24;
            groupBox1.TabStop = false;
            groupBox1.Text = "图像导出";
            // 
            // closeButton
            // 
            closeButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            closeButton.BorderRadius = 8;
            closeButton.BorderSize = 1;
            closeButton.ButtonTextColor = System.Drawing.Color.White;
            closeButton.CustomEnabled = true;
            closeButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            closeButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            closeButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            closeButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            closeButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            closeButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            closeButton.IconImage = null;
            closeButton.IconSize = 60;
            closeButton.IconVisible = true;
            closeButton.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            closeButton.IsPressed = false;
            closeButton.Location = new System.Drawing.Point(194, 124);
            closeButton.Name = "closeButton";
            closeButton.NormalBorderColor = System.Drawing.Color.FromArgb(138, 138, 138);
            closeButton.NormalColor = System.Drawing.Color.FromArgb(138, 138, 138);
            closeButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            closeButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            closeButton.Size = new System.Drawing.Size(158, 46);
            closeButton.TabIndex = 24;
            closeButton.Text = "关闭";
            closeButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            closeButton.UseVisualStyleBackColor = false;
            closeButton.Click += closeButton_Click;
            // 
            // ExportImagePanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            Controls.Add(groupBox1);
            Name = "ExportImagePanel";
            Size = new System.Drawing.Size(390, 212);
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox formatCombox;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox numberCombox;
        private RoundButton applyButton;
        private System.Windows.Forms.GroupBox groupBox1;
        private RoundButton closeButton;
    }
}
