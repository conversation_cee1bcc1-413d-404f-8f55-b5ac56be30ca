<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="splitContainer1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="splitContainer1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 120</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="splitContainer1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="leftPanel.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="leftPanel.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="leftPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="leftPanel.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="leftPanel.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>20, 0, 20, 20</value>
  </data>
  <data name="leftPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 488</value>
  </data>
  <data name="leftPanel.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;leftPanel.Name" xml:space="preserve">
    <value>leftPanel</value>
  </data>
  <data name="&gt;&gt;leftPanel.Type" xml:space="preserve">
    <value>LkControls.LayoutControls.LeftPanel, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;leftPanel.Parent" xml:space="preserve">
    <value>splitContainer1.Panel1</value>
  </data>
  <data name="&gt;&gt;leftPanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel1.Name" xml:space="preserve">
    <value>splitContainer1.Panel1</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel1.Parent" xml:space="preserve">
    <value>splitContainer1</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="splitContainer2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="splitContainer2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="splitContainer2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="imageViewer1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="imageViewer1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="imageViewer1.Size" type="System.Drawing.Size, System.Drawing">
    <value>247, 488</value>
  </data>
  <data name="imageViewer1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="imageViewer1.Text" xml:space="preserve">
    <value>imageViewer1</value>
  </data>
  <data name="&gt;&gt;imageViewer1.Name" xml:space="preserve">
    <value>imageViewer1</value>
  </data>
  <data name="&gt;&gt;imageViewer1.Type" xml:space="preserve">
    <value>Vintasoft.Imaging.UI.ImageViewer, Vintasoft.Imaging.UI, Culture=neutral, PublicKeyToken=153caf29a10d2e31</value>
  </data>
  <data name="&gt;&gt;imageViewer1.Parent" xml:space="preserve">
    <value>splitContainer2.Panel1</value>
  </data>
  <data name="&gt;&gt;imageViewer1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel1.Name" xml:space="preserve">
    <value>splitContainer2.Panel1</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel1.Parent" xml:space="preserve">
    <value>splitContainer2</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="toolsPanel.AutoScroll" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="toolsPanel.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="toolsPanel.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="toolsPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="toolsPanel.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="toolsPanel.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>20, 0, 20, 0</value>
  </data>
  <data name="toolsPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>495, 488</value>
  </data>
  <data name="toolsPanel.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;toolsPanel.Name" xml:space="preserve">
    <value>toolsPanel</value>
  </data>
  <data name="&gt;&gt;toolsPanel.Type" xml:space="preserve">
    <value>LkControls.LayoutControls.ToolsPanel, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;toolsPanel.Parent" xml:space="preserve">
    <value>splitContainer2.Panel2</value>
  </data>
  <data name="&gt;&gt;toolsPanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel2.Name" xml:space="preserve">
    <value>splitContainer2.Panel2</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel2.Parent" xml:space="preserve">
    <value>splitContainer2</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="splitContainer2.Size" type="System.Drawing.Size, System.Drawing">
    <value>746, 488</value>
  </data>
  <data name="splitContainer2.SplitterDistance" type="System.Int32, mscorlib">
    <value>247</value>
  </data>
  <data name="splitContainer2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Name" xml:space="preserve">
    <value>splitContainer2</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitContainer, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;splitContainer2.Parent" xml:space="preserve">
    <value>splitContainer1.Panel2</value>
  </data>
  <data name="&gt;&gt;splitContainer2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel2.Name" xml:space="preserve">
    <value>splitContainer1.Panel2</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel2.Parent" xml:space="preserve">
    <value>splitContainer1</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="splitContainer1.Size" type="System.Drawing.Size, System.Drawing">
    <value>830, 488</value>
  </data>
  <data name="splitContainer1.SplitterDistance" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="splitContainer1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Name" xml:space="preserve">
    <value>splitContainer1</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitContainer, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;splitContainer1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;splitContainer1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="basicOperationLabel.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="basicOperationLabel.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="basicOperationLabel.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;basicOperationLabel.Name" xml:space="preserve">
    <value>basicOperationLabel</value>
  </data>
  <data name="&gt;&gt;basicOperationLabel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="dicomSeriesManagerControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="dicomSeriesManagerControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="dicomSeriesManagerControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>300, 535</value>
  </data>
  <data name="dicomSeriesManagerControl1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="dicomSeriesManagerControl1.Text" xml:space="preserve">
    <value>dicomSeriesManagerControl1</value>
  </data>
  <data name="&gt;&gt;dicomSeriesManagerControl1.Name" xml:space="preserve">
    <value>dicomSeriesManagerControl1</value>
  </data>
  <data name="&gt;&gt;dicomSeriesManagerControl1.Type" xml:space="preserve">
    <value>Vintasoft.Imaging.Dicom.UI.DicomSeriesManagerControl, Vintasoft.Imaging.Dicom.UI, Culture=neutral, PublicKeyToken=153caf29a10d2e31</value>
  </data>
  <metadata name="menuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>18, 16</value>
  </metadata>
  <data name="addFilesToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Ctrl+O</value>
  </data>
  <data name="addFilesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>314, 26</value>
  </data>
  <data name="addFilesToolStripMenuItem.Text" xml:space="preserve">
    <value>Add Files...</value>
  </data>
  <data name="openDirectoryToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>314, 26</value>
  </data>
  <data name="openDirectoryToolStripMenuItem.Text" xml:space="preserve">
    <value>Open Directory...</value>
  </data>
  <data name="toolStripSeparator5.Size" type="System.Drawing.Size, System.Drawing">
    <value>311, 6</value>
  </data>
  <data name="saveImagesAsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>314, 26</value>
  </data>
  <data name="saveImagesAsToolStripMenuItem.Text" xml:space="preserve">
    <value>Save Images As...</value>
  </data>
  <data name="burnAndSaveToDICOMFileToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>314, 26</value>
  </data>
  <data name="burnAndSaveToDICOMFileToolStripMenuItem.Text" xml:space="preserve">
    <value>Burn And Save To DICOM file...</value>
  </data>
  <data name="saveViewerScreenshotToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>314, 26</value>
  </data>
  <data name="saveViewerScreenshotToolStripMenuItem.Text" xml:space="preserve">
    <value>Save Viewer Screenshot...</value>
  </data>
  <data name="toolStripSeparator12.Size" type="System.Drawing.Size, System.Drawing">
    <value>311, 6</value>
  </data>
  <data name="closeFilesToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Ctrl+W</value>
  </data>
  <data name="closeFilesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>314, 26</value>
  </data>
  <data name="closeFilesToolStripMenuItem.Text" xml:space="preserve">
    <value>Close Files</value>
  </data>
  <data name="toolStripSeparator2.Size" type="System.Drawing.Size, System.Drawing">
    <value>311, 6</value>
  </data>
  <data name="exitToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Alt+X</value>
  </data>
  <data name="exitToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>314, 26</value>
  </data>
  <data name="exitToolStripMenuItem.Text" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="fileToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 24</value>
  </data>
  <data name="fileToolStripMenuItem.Text" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="cutToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Ctrl+X</value>
  </data>
  <data name="cutToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>252, 26</value>
  </data>
  <data name="cutToolStripMenuItem.Text" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="copyToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Ctrl+C</value>
  </data>
  <data name="copyToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>252, 26</value>
  </data>
  <data name="copyToolStripMenuItem.Text" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="pasteToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Ctrl+V</value>
  </data>
  <data name="pasteToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>252, 26</value>
  </data>
  <data name="pasteToolStripMenuItem.Text" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="deleteToolStripMenuItem.ShortcutKeyDisplayString" xml:space="preserve">
    <value>Del</value>
  </data>
  <data name="deleteToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>252, 26</value>
  </data>
  <data name="deleteToolStripMenuItem.Text" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="deleteAllToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Alt+Del</value>
  </data>
  <data name="deleteAllToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>252, 26</value>
  </data>
  <data name="deleteAllToolStripMenuItem.Text" xml:space="preserve">
    <value>Delete All</value>
  </data>
  <data name="editToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 24</value>
  </data>
  <data name="editToolStripMenuItem.Text" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="imageViewerSettingsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="imageViewerSettingsToolStripMenuItem.Text" xml:space="preserve">
    <value>Image Viewer Settings...</value>
  </data>
  <data name="clockwiseToolStripMenuItem.ShortcutKeyDisplayString" xml:space="preserve">
    <value>Shift+Ctrl+Plus</value>
  </data>
  <data name="clockwiseToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Ctrl+Shift+Oemplus</value>
  </data>
  <data name="clockwiseToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>355, 26</value>
  </data>
  <data name="clockwiseToolStripMenuItem.Text" xml:space="preserve">
    <value>Clockwise</value>
  </data>
  <data name="counterclockwiseToolStripMenuItem.ShortcutKeyDisplayString" xml:space="preserve">
    <value>Shift+Ctrl+Minus</value>
  </data>
  <data name="counterclockwiseToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>Ctrl+Shift+OemMinus</value>
  </data>
  <data name="counterclockwiseToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>355, 26</value>
  </data>
  <data name="counterclockwiseToolStripMenuItem.Text" xml:space="preserve">
    <value>Counterclockwise</value>
  </data>
  <data name="rotateViewToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="rotateViewToolStripMenuItem.Text" xml:space="preserve">
    <value>Rotate View</value>
  </data>
  <data name="toolStripSeparator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 6</value>
  </data>
  <data name="fullScreenToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F5</value>
  </data>
  <data name="fullScreenToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="fullScreenToolStripMenuItem.Text" xml:space="preserve">
    <value>Full Screen</value>
  </data>
  <data name="showViewerScrollbarsToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F6</value>
  </data>
  <data name="showViewerScrollbarsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="showViewerScrollbarsToolStripMenuItem.Text" xml:space="preserve">
    <value>Show Viewer Scrollbars</value>
  </data>
  <data name="showBrowseScrollbarToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="showBrowseScrollbarToolStripMenuItem.Text" xml:space="preserve">
    <value>Show Browse Scrollbar</value>
  </data>
  <data name="toolStripSeparator4.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 6</value>
  </data>
  <data name="showOverlayImagesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="showOverlayImagesToolStripMenuItem.Text" xml:space="preserve">
    <value>Show Overlay Images</value>
  </data>
  <data name="overlayColorToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="overlayColorToolStripMenuItem.Text" xml:space="preserve">
    <value>Overlay Color...</value>
  </data>
  <data name="toolStripSeparator6.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 6</value>
  </data>
  <data name="showMetadataInViewerToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="showMetadataInViewerToolStripMenuItem.Text" xml:space="preserve">
    <value>Show Metadata In Viewer</value>
  </data>
  <data name="textOverlaySettingsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="textOverlaySettingsToolStripMenuItem.Text" xml:space="preserve">
    <value>Text Overlay Settings...</value>
  </data>
  <data name="toolStripSeparator8.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 6</value>
  </data>
  <data name="showRulersInViewerToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="showRulersInViewerToolStripMenuItem.Text" xml:space="preserve">
    <value>Show Rulers In Viewer</value>
  </data>
  <data name="rulersColorToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="rulersColorToolStripMenuItem.Text" xml:space="preserve">
    <value>Rulers Color...</value>
  </data>
  <data name="rulersUnitOfMeasureToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="rulersUnitOfMeasureToolStripMenuItem.Text" xml:space="preserve">
    <value>Rulers Unit Of Measure</value>
  </data>
  <data name="toolStripSeparator7.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 6</value>
  </data>
  <data name="voiLutToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="voiLutToolStripMenuItem.Text" xml:space="preserve">
    <value>VOI LUT...</value>
  </data>
  <data name="negativeImageToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F11</value>
  </data>
  <data name="negativeImageToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="negativeImageToolStripMenuItem.Text" xml:space="preserve">
    <value>Is Negative</value>
  </data>
  <data name="widthHorizontalInvertedCenterVerticalToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>393, 26</value>
  </data>
  <data name="widthHorizontalInvertedCenterVerticalToolStripMenuItem.Text" xml:space="preserve">
    <value>Width Horizontal Inverted,Center Vertical</value>
  </data>
  <data name="widthHorizontalCenterVerticalToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>393, 26</value>
  </data>
  <data name="widthHorizontalCenterVerticalToolStripMenuItem.Text" xml:space="preserve">
    <value>Width Horizontal, Center Vertical</value>
  </data>
  <data name="widthVerticalCenterHorizontalToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>393, 26</value>
  </data>
  <data name="widthVerticalCenterHorizontalToolStripMenuItem.Text" xml:space="preserve">
    <value>Width Vertical, Center Horizontal</value>
  </data>
  <data name="voiLutMouseMoveDirectionToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="voiLutMouseMoveDirectionToolStripMenuItem.Text" xml:space="preserve">
    <value>VOI LUT Mouse Move Direction</value>
  </data>
  <data name="toolStripSeparator23.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 6</value>
  </data>
  <data name="magnifierSettingsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>321, 26</value>
  </data>
  <data name="magnifierSettingsToolStripMenuItem.Text" xml:space="preserve">
    <value>Magnifier Settings...</value>
  </data>
  <data name="viewToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 24</value>
  </data>
  <data name="viewToolStripMenuItem.Text" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="fileMetadataToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>203, 26</value>
  </data>
  <data name="fileMetadataToolStripMenuItem.Text" xml:space="preserve">
    <value>File Metadata...</value>
  </data>
  <data name="metadataToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 24</value>
  </data>
  <data name="metadataToolStripMenuItem.Text" xml:space="preserve">
    <value>Metadata</value>
  </data>
  <data name="overlayImagesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>215, 26</value>
  </data>
  <data name="overlayImagesToolStripMenuItem.Text" xml:space="preserve">
    <value>Overlay Images...</value>
  </data>
  <data name="pageToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 24</value>
  </data>
  <data name="pageToolStripMenuItem.Text" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="showAnimationToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 26</value>
  </data>
  <data name="showAnimationToolStripMenuItem.Text" xml:space="preserve">
    <value>Show Animation</value>
  </data>
  <data name="animationDelay_valueToolStripComboBox.Items" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="animationDelay_valueToolStripComboBox.Items1" xml:space="preserve">
    <value>100</value>
  </data>
  <data name="animationDelay_valueToolStripComboBox.Items2" xml:space="preserve">
    <value>1000</value>
  </data>
  <data name="animationDelay_valueToolStripComboBox.Items3" xml:space="preserve">
    <value>2000</value>
  </data>
  <data name="animationDelay_valueToolStripComboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 28</value>
  </data>
  <data name="animationDelay_valueToolStripComboBox.Text" xml:space="preserve">
    <value>100</value>
  </data>
  <data name="animationDelayToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 26</value>
  </data>
  <data name="animationDelayToolStripMenuItem.Text" xml:space="preserve">
    <value>Animation Delay</value>
  </data>
  <data name="animationRepeatToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 26</value>
  </data>
  <data name="animationRepeatToolStripMenuItem.Text" xml:space="preserve">
    <value>Animation Repeat</value>
  </data>
  <data name="saveAsGifFileToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 26</value>
  </data>
  <data name="saveAsGifFileToolStripMenuItem.Text" xml:space="preserve">
    <value>Save as GIF file...</value>
  </data>
  <data name="toolsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 24</value>
  </data>
  <data name="toolsToolStripMenuItem.Text" xml:space="preserve">
    <value>Animation</value>
  </data>
  <data name="infoToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 26</value>
  </data>
  <data name="infoToolStripMenuItem.Text" xml:space="preserve">
    <value>Info...</value>
  </data>
  <data name="toolStripSeparator9.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 6</value>
  </data>
  <data name="interactionMode_noneToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 26</value>
  </data>
  <data name="interactionMode_noneToolStripMenuItem.Text" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="interactionMode_viewToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 26</value>
  </data>
  <data name="interactionMode_viewToolStripMenuItem.Text" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="interactionMode_authorToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 26</value>
  </data>
  <data name="interactionMode_authorToolStripMenuItem.Text" xml:space="preserve">
    <value>Author</value>
  </data>
  <data name="interactionModeToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 26</value>
  </data>
  <data name="interactionModeToolStripMenuItem.Text" xml:space="preserve">
    <value>Interaction Mode</value>
  </data>
  <data name="toolStripSeparator10.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 6</value>
  </data>
  <data name="presentationStateLoadToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="presentationStateLoadToolStripMenuItem.Text" xml:space="preserve">
    <value>Load...</value>
  </data>
  <data name="presentationStateInfoToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="presentationStateInfoToolStripMenuItem.Text" xml:space="preserve">
    <value>Info...</value>
  </data>
  <data name="presentationStateSaveToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="presentationStateSaveToolStripMenuItem.Text" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="presentationStateSaveToToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="presentationStateSaveToToolStripMenuItem.Text" xml:space="preserve">
    <value>Save To...</value>
  </data>
  <data name="presentationStateToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 26</value>
  </data>
  <data name="presentationStateToolStripMenuItem.Text" xml:space="preserve">
    <value>Presentation State</value>
  </data>
  <data name="binaryFormatLoadToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="binaryFormatLoadToolStripMenuItem.Text" xml:space="preserve">
    <value>Load...</value>
  </data>
  <data name="binaryFormatSaveToToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="binaryFormatSaveToToolStripMenuItem.Text" xml:space="preserve">
    <value>Save To...</value>
  </data>
  <data name="binaryFormatToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 26</value>
  </data>
  <data name="binaryFormatToolStripMenuItem.Text" xml:space="preserve">
    <value>Binary Format</value>
  </data>
  <data name="xmpFormatLoadToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="xmpFormatLoadToolStripMenuItem.Text" xml:space="preserve">
    <value>Load...</value>
  </data>
  <data name="xmpFormatSaveToToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 26</value>
  </data>
  <data name="xmpFormatSaveToToolStripMenuItem.Text" xml:space="preserve">
    <value>Save To...</value>
  </data>
  <data name="xmpFormatToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 26</value>
  </data>
  <data name="xmpFormatToolStripMenuItem.Text" xml:space="preserve">
    <value>XMP Format</value>
  </data>
  <data name="toolStripSeparator11.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 6</value>
  </data>
  <data name="pointToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="pointToolStripMenuItem.Text" xml:space="preserve">
    <value>Point</value>
  </data>
  <data name="circleToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="circleToolStripMenuItem.Text" xml:space="preserve">
    <value>Circle</value>
  </data>
  <data name="polylineToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="polylineToolStripMenuItem.Text" xml:space="preserve">
    <value>Polyline</value>
  </data>
  <data name="interpolatedToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="interpolatedToolStripMenuItem.Text" xml:space="preserve">
    <value>Interpolated</value>
  </data>
  <data name="toolStripSeparator15.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 6</value>
  </data>
  <data name="rectangleToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="rectangleToolStripMenuItem.Text" xml:space="preserve">
    <value>Rectangle</value>
  </data>
  <data name="ellipseToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="ellipseToolStripMenuItem.Text" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="multilineToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="multilineToolStripMenuItem.Text" xml:space="preserve">
    <value>Multiline</value>
  </data>
  <data name="rangelineToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="rangelineToolStripMenuItem.Text" xml:space="preserve">
    <value>Rangeline</value>
  </data>
  <data name="infinitelineToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="infinitelineToolStripMenuItem.Text" xml:space="preserve">
    <value>Infiniteline</value>
  </data>
  <data name="cutlineToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="cutlineToolStripMenuItem.Text" xml:space="preserve">
    <value>Cutline</value>
  </data>
  <data name="arrowToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="arrowToolStripMenuItem.Text" xml:space="preserve">
    <value>Arrow</value>
  </data>
  <data name="axisToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="axisToolStripMenuItem.Text" xml:space="preserve">
    <value>Axis</value>
  </data>
  <data name="rulerToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="rulerToolStripMenuItem.Text" xml:space="preserve">
    <value>Ruler</value>
  </data>
  <data name="crosshairToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="crosshairToolStripMenuItem.Text" xml:space="preserve">
    <value>Crosshair</value>
  </data>
  <data name="toolStripSeparator16.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 6</value>
  </data>
  <data name="textToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 26</value>
  </data>
  <data name="textToolStripMenuItem.Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="addToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 26</value>
  </data>
  <data name="addToolStripMenuItem.Text" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="toolStripSeparator3.Size" type="System.Drawing.Size, System.Drawing">
    <value>223, 6</value>
  </data>
  <data name="propertiesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 26</value>
  </data>
  <data name="propertiesToolStripMenuItem.Text" xml:space="preserve">
    <value>Properties...</value>
  </data>
  <data name="annotationsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="annotationsToolStripMenuItem.Text" xml:space="preserve">
    <value>Annotations</value>
  </data>
  <data name="aboutToolStripMenuItem.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F1</value>
  </data>
  <data name="aboutToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 26</value>
  </data>
  <data name="aboutToolStripMenuItem.Text" xml:space="preserve">
    <value>About...</value>
  </data>
  <data name="helpToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 24</value>
  </data>
  <data name="helpToolStripMenuItem.Text" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="menuStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="menuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>830, 28</value>
  </data>
  <data name="menuStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="menuStrip1.Text" xml:space="preserve">
    <value>menuStrip1</value>
  </data>
  <data name="&gt;&gt;menuStrip1.Name" xml:space="preserve">
    <value>menuStrip1</value>
  </data>
  <data name="&gt;&gt;menuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuStrip, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="statusStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>486, 20</value>
  </metadata>
  <data name="progressBar1.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 17</value>
  </data>
  <data name="progressBar1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="springStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>803, 17</value>
  </data>
  <data name="nsnrStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 17</value>
  </data>
  <data name="separator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 23</value>
  </data>
  <data name="toolStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 17</value>
  </data>
  <data name="separator2.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 23</value>
  </data>
  <data name="windowLevelStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 17</value>
  </data>
  <data name="zoomStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 17</value>
  </data>
  <data name="imageInfoToolStripStatusLabel.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 17</value>
  </data>
  <data name="statusStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 608</value>
  </data>
  <data name="statusStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>830, 23</value>
  </data>
  <data name="statusStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="statusStrip1.Text" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Name" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.StatusStrip, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;statusStrip1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="openDicomFileDialog.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>253, 56</value>
  </metadata>
  <data name="openDicomFileDialog.Filter" xml:space="preserve">
    <value>Dicom files|*.dcm</value>
  </data>
  <metadata name="imageViewerToolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>474, 94</value>
  </metadata>
  <data name="imageViewerToolStrip1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="imageViewerToolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 0</value>
  </data>
  <data name="imageViewerToolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 27</value>
  </data>
  <data name="imageViewerToolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;imageViewerToolStrip1.Name" xml:space="preserve">
    <value>imageViewerToolStrip1</value>
  </data>
  <data name="&gt;&gt;imageViewerToolStrip1.Type" xml:space="preserve">
    <value>DemosCommonCode.Imaging.ImageViewerToolStrip, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;imageViewerToolStrip1.Parent" xml:space="preserve">
    <value>toolStripPanel1</value>
  </data>
  <data name="&gt;&gt;imageViewerToolStrip1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="dicomAnnotatedViewerToolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 56</value>
  </metadata>
  <data name="dicomAnnotatedViewerToolStrip1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="dicomAnnotatedViewerToolStrip1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="voiLutsToolStripSplitButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAHGSURBVDhPhZI/SBxBFMYnEW53FNKm19iIhSBBJXdoEWKZ
        NConagzaaWOjooSQLqUgpE0hiBFzO4coHogKFhJSqLFSi7PxD3Kex+n55+72PWfG5+3o3uEHHztveN9v
        ZmeG1b5tx1LWEhyZIUT2gob3MgMdPaPPAmCRvYKo9YZKD7C5vYuXmSvs7v9aEgBR+50r+Mz1nJWkKQ+w
        9X9X97uuq+tw7xg2NH9GiFa8RsdqUb15wdcVMC+svzjLyh4BlGPLGzj+/SeGPvRj8jwtYYCrgrs3IpAA
        x+oDEajJCn4AEbsZv7GXPoBy55dxzGSu9W605IoFO/wTOHbo0UE+BZwlU5QkGYBLwS8o5onatBQAAKgi
        GQBtubpZ+wA+Gc0pwa/gN+PmnO8Xjk8SlCQZzbk/fJ827skMt3UN6+/tbZbSUhSWN3GKc1YlOuX1FL3X
        Q7iuKYwLS+vabd0jeJ5K6/OI/+I5dXUYsYOuY09mpnkaoryR4h4gfnCoFzw8OtV18H2f/sJ8oBoiVpXq
        lYAJtZvEFL8pXOUDYGXtH27v7GHrx0FdK2vJgG4kybcwK59zrOhLHBj6URgrFwMogeANvh0UcymAJ8bu
        AA70BUHr3Yk0AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="voiLutsToolStripSplitButton.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="voiLutsToolStripSplitButton.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 24</value>
  </data>
  <data name="voiLutsToolStripSplitButton.Text" xml:space="preserve">
    <value>Value of interest lookup tables</value>
  </data>
  <data name="dicomAnnotatedViewerToolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 0</value>
  </data>
  <data name="dicomAnnotatedViewerToolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 27</value>
  </data>
  <data name="dicomAnnotatedViewerToolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;dicomAnnotatedViewerToolStrip1.Name" xml:space="preserve">
    <value>dicomAnnotatedViewerToolStrip1</value>
  </data>
  <data name="&gt;&gt;dicomAnnotatedViewerToolStrip1.Type" xml:space="preserve">
    <value>DemosCommonCode.Imaging.DicomAnnotatedViewerToolStrip, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dicomAnnotatedViewerToolStrip1.Parent" xml:space="preserve">
    <value>toolStripPanel1</value>
  </data>
  <data name="&gt;&gt;dicomAnnotatedViewerToolStrip1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="dicomViewerToolInteractionButtonToolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>184, 94</value>
  </metadata>
  <data name="dicomViewerToolInteractionButtonToolStrip1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="dicomViewerToolInteractionButtonToolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 0</value>
  </data>
  <data name="dicomViewerToolInteractionButtonToolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 25</value>
  </data>
  <data name="dicomViewerToolInteractionButtonToolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;dicomViewerToolInteractionButtonToolStrip1.Name" xml:space="preserve">
    <value>dicomViewerToolInteractionButtonToolStrip1</value>
  </data>
  <data name="&gt;&gt;dicomViewerToolInteractionButtonToolStrip1.Type" xml:space="preserve">
    <value>DicomViewerDemo.DicomViewerToolInteractionButtonToolStrip, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dicomViewerToolInteractionButtonToolStrip1.Parent" xml:space="preserve">
    <value>toolStripPanel1</value>
  </data>
  <data name="&gt;&gt;dicomViewerToolInteractionButtonToolStrip1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="annotationInteractionModeToolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>599, 23</value>
  </metadata>
  <data name="annotationInteractionModeToolStrip.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="toolStripLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>222, 25</value>
  </data>
  <data name="toolStripLabel1.Text" xml:space="preserve">
    <value>Annotation Interaction Mode</value>
  </data>
  <data name="annotationInteractionModeToolStripComboBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 28</value>
  </data>
  <data name="annotationInteractionModeToolStrip.Location" type="System.Drawing.Point, System.Drawing">
    <value>381, 0</value>
  </data>
  <data name="annotationInteractionModeToolStrip.Size" type="System.Drawing.Size, System.Drawing">
    <value>358, 28</value>
  </data>
  <data name="annotationInteractionModeToolStrip.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="annotationInteractionModeToolStrip.Text" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="&gt;&gt;annotationInteractionModeToolStrip.Name" xml:space="preserve">
    <value>annotationInteractionModeToolStrip</value>
  </data>
  <data name="&gt;&gt;annotationInteractionModeToolStrip.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;annotationInteractionModeToolStrip.Parent" xml:space="preserve">
    <value>toolStripPanel1</value>
  </data>
  <data name="&gt;&gt;annotationInteractionModeToolStrip.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="annotationsToolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>310, 19</value>
  </metadata>
  <data name="annotationsToolStrip1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="annotationsToolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>739, 0</value>
  </data>
  <data name="annotationsToolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 25</value>
  </data>
  <data name="annotationsToolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="annotationsToolStrip1.Text" xml:space="preserve">
    <value>annotationsToolStrip1</value>
  </data>
  <data name="&gt;&gt;annotationsToolStrip1.Name" xml:space="preserve">
    <value>annotationsToolStrip1</value>
  </data>
  <data name="&gt;&gt;annotationsToolStrip1.Type" xml:space="preserve">
    <value>DicomViewerDemo.AnnotationsToolStrip, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;annotationsToolStrip1.Parent" xml:space="preserve">
    <value>toolStripPanel1</value>
  </data>
  <data name="&gt;&gt;annotationsToolStrip1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="toolStripPanel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="toolStripPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 24</value>
  </data>
  <data name="toolStripPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>830, 50</value>
  </data>
  <data name="&gt;&gt;toolStripPanel1.Name" xml:space="preserve">
    <value>toolStripPanel1</value>
  </data>
  <data name="&gt;&gt;toolStripPanel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="topPanel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="topPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="topPanel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 3, 2, 3</value>
  </data>
  <data name="topPanel1.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>20, 0, 20, 0</value>
  </data>
  <data name="topPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>830, 120</value>
  </data>
  <data name="topPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;topPanel1.Name" xml:space="preserve">
    <value>topPanel1</value>
  </data>
  <data name="&gt;&gt;topPanel1.Type" xml:space="preserve">
    <value>LkControls.LayoutControls.TopPanel, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;topPanel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;topPanel1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="colorDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>836, 24</value>
  </metadata>
  <metadata name="openDicomAnnotationsFileDialog.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>689, 56</value>
  </metadata>
  <data name="openDicomAnnotationsFileDialog.Filter" xml:space="preserve">
    <value>Presentation State File(*.pre)|*.pre|Binary Annotations(*.vsab)|*.vsab|XMP Annotations(*.xmp)|*.xmp|All Formats(*.pre;*.vsab;*.xmp)|*.pre;*.vsab;*.xmp</value>
  </data>
  <metadata name="saveDicomAnnotationsFileDialog.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>919, 57</value>
  </metadata>
  <metadata name="saveFileDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>422, 56</value>
  </metadata>
  <data name="saveFileDialog1.Filter" xml:space="preserve">
    <value>Mpeg files|*.mpg</value>
  </data>
  <metadata name="saveFileDialog2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>548, 56</value>
  </metadata>
  <data name="saveFileDialog2.Filter" xml:space="preserve">
    <value>GIF files|*.gif</value>
  </data>
  <metadata name="folderBrowserDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 94</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>153</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>830, 631</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAZDAAAHQ4AABYKADAUCgCeFAkA5RMJAP0TCQD/EwkA/xMJAP8TCQD/EwkA/xMJAP8TCQD/EwkA/xMJ
        AP8TCQD/EwkA/xMJAP8TCQD/EwkA/xMJAP8TCQD/EwkA/xMJAP8TCQD/EwkA/xMJAP0UCQDlFAoAnxYK
        ADEdDgAAGQwAABwPAAAaDABJGQwA1hgMAP8YDAD/GAwA/xgMAP8YDAD/GAwA/xgMAP8YDAD/GAwA/xgM
        AP8YDAD/GAwA/xgMAP8YDAD/GAwA/xgMAP8YDAD/GAwA/xgMAP8YDAD/GAwA/xgMAP8YDAD/GAwA/xgM
        AP8YDAD/GQwA1xoMAEkcDwAAHw8AMR4OANcdDgD/HQ4A/x0OAP8dDgD/HQ4A/x0OAP8dDgD/HQ4A/x0O
        AP8dDgD/HQ4A/x0OAP8dDgD/HQ4A/x0OAP8dDgD/HQ4A/x0OAP8dDgD/HQ4A/x0OAP8dDgD/HQ4A/x0O
        AP8dDgD/HQ4A/x0OAP8dDgD/Hg4A1h8PADAjEQCfIxEA/yMRAP8jEQD/IxEA/yMRAP8jEQD/IhEA/yIQ
        AP8gEAD/IA8A/yAPAP8gDwD/IA8A/yAPAP8gDwD/IA8A/yAPAP8gDwD/IA8A/yAPAP8gDwD/IBAA/yIQ
        AP8jEQD/IxEA/yMRAP8jEQD/IxEA/yMRAP8jEQD/IxEAnigTAOUoEwD/KBMA/ygTAP8oEwD/KBMA/ycT
        AP8kEQD/KBMA/zkbAP8/HwD/PR4A/zwdAP88HQD/PB0A/zwdAP88HQD/PB0A/zwdAP88HQD/PB0A/z0d
        AP88HQD/LRUA/yYSAP8nEwD/KBMA/ygTAP8oEwD/KBMA/ygTAP8oEwDlLRYA/S0WAP8tFgD/LRYA/y0W
        AP8sFQD/KxQA/1InAP+lTwD/2mgA/+JsAP/gawD/32sA/99rAP/fawD/32sA/99rAP/fawD/32sA/99r
        AP/fawD/32sA/+BrAP+MQwD/LBUA/ywVAP8tFgD/LRYA/y0WAP8tFgD/LRYA/y0WAP0yGAD/MhgA/zIY
        AP8yGAD/MhgA/y8XAP9fLgD/32sA//97AP//ewD//3sA//97AP//ewD//3sA//97AP//ewD//3sA//97
        AP//ewD//3sA//97AP//ewD//3wA/+pwAP9dLQD/LhYA/zEYAP8yGAD/MhgA/zIYAP8yGAD/MhgA/zcb
        AP83GwD/NxsA/zcbAP82GgD/PB0A/75bAP//fAD//3oA//96AP//egD//3oA//96AP//egD//3oA//96
        AP//egD//3oA//96AP//egD//3oA//96AP//egD//3sA/79bAP8/HgD/NhoA/zcbAP83GwD/NxsA/zcb
        AP83GwD/PB0A/zwdAP88HQD/PB0A/zocAP9TKAD/5m4A//97AP//egD//3oA//96AP//egD//3oA//96
        AP//egD//3oA//96AP//egD//3oA//96AP//egD//3oA//96AP//egD/+3gA/4lBAP86HAD/PB0A/zwd
        AP88HQD/PB0A/zwdAP9CIAD/QiAA/0IgAP9CIAD/Px4A/2MvAP/wcwD//3oA//96AP//egD//3oA//15
        AP/9eQD//XkA//15AP/9eQD//XkA//15AP/9eQD//XkA//55AP/+egD//noA//56AP//egD/32sA/14t
        AP9AHwD/QiAA/0IgAP9CIAD/QiAA/0ciAP9HIgD/RyIA/0YiAP9DIAD/ajMA//J0AP//egD//3oA//96
        AP/5dwD/oU0A/4Q/AP+GQAD/iUIA/4pCAP+KQgD/ikIA/4pCAP+KQgD/jEMA/45EAP+NQwD/jEMA/49F
        AP+sUgD/cDYA/0UhAP9HIgD/RyIA/0ciAP9HIgD/TCQA/0wkAP9MJAD/TCQA/0gjAP9vNQD/83QA//96
        AP//egD//3oA//V1AP9xNgD/RCEA/0giAP9IIwD/SCMA/0gjAP9IIwD/SCMA/0gjAP9HIgD/QyAA/0Qg
        AP9eLQD/oU0A/9hoAP9vNQD/SiMA/0wkAP9MJAD/TCQA/0wkAP9RJwD/UScA/1EnAP9RJwD/TSUA/3M3
        AP/zdAD//3oA//96AP//egD/9nYA/3c5AP9NJQD/UCcA/1EnAP9RJwD/UScA/1EnAP9RJwD/TyYA/00l
        AP9cNgD/WC8A/5lIAP/ibAD/528A/3I3AP9PJgD/UScA/1EnAP9RJwD/UScA/1cqAP9XKgD/VyoA/1Yq
        AP9TKAD/dzkA//R0AP//egD//3oA//96AP/1dQD/ejsA/1IoAP9WKQD/VioA/1YqAP9XKgD/VikA/1Up
        AP9kOAD/nHIB/+C2Av/JoQL/glcB/4BAAP+2VgD/cDYA/1IoAP9WKgD/VyoA/1cqAP9XKgD/XCwA/1ws
        AP9cLAD/WywA/1gqAP97OwD/9HUA//96AP//egD//3oA//V1AP97OwD/VyoA/1orAP9aKwD/WSsA/1kr
        AP9oMgD/bTUA/5xvAf/zyQP//9kD///YA//zygP/uJEC/35NAP9eLQD/VyoA/1ssAP9cLAD/XCwA/1ws
        AP9hLwD/YS8A/2EvAP9gLwD/XS0A/389AP/0dQD//3oA//96AP//egD/9XUA/389AP9bLAD/XCwA/1kq
        AP9nMQD/m0sA/9ppAP/ibAD/qVUA/5tkAf/RpQL/+9ED///YA///2AP/27EC/2k8AP9bKwD/YC4A/2Ev
        AP9hLwD/YS8A/2YxAP9mMQD/ZjEA/2UxAP9iLwD/gz8A//R1AP//egD//3oA//96AP/1dQD/gj4A/10s
        AP9oOAD/ckQA/4RBAP/gagD//3sA//97AP/9eQD/12YA/59RAP+kcAH/3rIC///VA//1ywP/f08B/2Eu
        AP9lMQD/ZjEA/2YxAP9mMQD/azQA/2s0AP9rNAD/azMA/2cyAP+HQQD/9XUA//96AP//egD//3oA//V1
        AP+MRwD/l2gB/9aqAv/swgP/tooC/4pQAP+zVQD/7nIA//97AP//ewD/+HYA/8pgAP+bUwD/sn8B/+Cz
        Av+GUgH/aDEA/2szAP9rNAD/azQA/2s0AP9wNgD/cDYA/3A2AP9wNgD/bDQA/4tDAP/1dQD//3oA//96
        AP//egD/9HQA/51YAP/UpgL//9UD///YA///1gP/5bsD/6Z5Af+PTQD/w1wA//Z1AP//ewD//3sA//Jz
        AP++WgD/n10B/39HAP9vNQD/cDYA/3A2AP9wNgD/cDYA/3U5AP91OQD/dTkA/3U4AP9yNwD/j0UA//V1
        AP//egD//3oA//96AP/1dQD/jEMA/3xDAP+wewH/6bsC///WA///2AP//tQD/9qvAv+dawH/mE0A/9Nk
        AP/8eAD//3sA//97AP/baAD/gT4A/3U4AP91OQD/dTkA/3U5AP91OQD/ezsA/3s7AP97OwD/ejsA/3c5
        AP+TRwD/9nYA//96AP//egD//3oA//V1AP+PRQD/dTgA/3g5AP+MUAD/wYwC//LGA///1wP//9gD//rQ
        A//NoQL/l2AB/6VQAP/gagD//3oA//Z2AP+RRgD/eToA/3s7AP97OwD/ezsA/3s7AP+APgD/gD4A/4A+
        AP9/PgD/fDwA/5lKAP/3dgD//3oA//96AP//egD/9XUA/5JGAP97OwD/fj0A/348AP+APgD/ml0B/9Ge
        Av/4zQP//9cD///XA//0yQP/wJIC/5VYAP+0VgD/5GwA/5NHAP97PAD/fz0A/4A+AP+APgD/gD4A/4VA
        AP+FQAD/hUAA/4RAAP+CPwD/nkwA//l3AP//egD//3oA//96AP/1dQD/lkgA/4A+AP+EQAD/hEAA/4VA
        AP+DPgD/iEQA/6lsAf/erQL//dID///XA///1wP/7MED/7SCAf+dVAD/iEEA/389AP+EQAD/hUAA/4VA
        AP+FQAD/ikMA/4pDAP+KQwD/ikMA/4hCAP+SRgD/z2MA//h3AP//ewD//3oA//Z2AP+bSwD/hkEA/4lC
        AP+KQwD/ikMA/4pDAP+KQgD/iEEA/5FMAP+4fAH/6roC///VA///1wP//9UD/9KiAv+FRQD/gz8A/4lC
        AP+KQwD/ikMA/4pDAP+PRQD/j0UA/49FAP+PRQD/j0UA/45EAP+QRQD/rFMA/95qAP/9eQD/9nYA/59N
        AP+MQwD/jkUA/49FAP+PRQD/j0UA/49FAP+PRQD/j0QA/45DAP+cVgD/x40B//LEA///1wP/9ckD/5pZ
        AP+KQQD/jkQA/49FAP+PRQD/j0UA/5RHAP+URwD/lEcA/5RHAP+URwD/lEcA/5NHAP+RRgD/mEkA/7xa
        AP/gawD/o04A/5JGAP+URwD/lEcA/5RHAP+URwD/lEcA/5RHAP+URwD/lEcA/5NGAP+URwD/qGIA/9ae
        Av/vwAP/o10A/5FFAP+URwD/lEcA/5RHAP+URwD/mkoA/ZpKAP+aSgD/mkoA/5pKAP+aSgD/mUoA/5lK
        AP+YSQD/l0kA/6FNAP+cSwD/mUoA/5lKAP+aSgD/mkoA/5pKAP+aSgD/mkoA/5pKAP+aSgD/mkoA/5pK
        AP+YSAD/nE0A/7hzAf+kWQD/mEkA/5pKAP+aSgD/mkoA/5pKAP2fTADln0wA/59MAP+fTAD/n0wA/59M
        AP+fTAD/n0wA/55MAP+eTAD/nkwA/55MAP+fTAD/n0wA/59MAP+fTAD/n0wA/59MAP+fTAD/n0wA/59M
        AP+fTAD/n0wA/59MAP+eTAD/nkwA/59NAP+fTAD/n0wA/59MAP+fTAD/n0wA5aRPAJ6kTwD/pE8A/6RP
        AP+kTwD/pE8A/6RPAP+kTwD/pE8A/6RPAP+kTwD/pE8A/6RPAP+kTwD/pE8A/6RPAP+kTwD/pE8A/6RP
        AP+kTwD/pE8A/6RPAP+kTwD/pE8A/6RPAP+kTwD/pE8A/6RPAP+kTwD/pE8A/6RPAP+kTwCfqFEAMKlR
        ANapUgD/qVIA/6lSAP+pUgD/qVIA/6lSAP+pUgD/qVIA/6lSAP+pUgD/qVIA/6lSAP+pUgD/qVIA/6lS
        AP+pUgD/qVIA/6lSAP+pUgD/qVIA/6lSAP+pUgD/qVIA/6lSAP+pUgD/qVIA/6lSAP+pUgD/qVEA16dR
        ADGrUwAArVMASa5UANeuVAD/rlQA/65UAP+uVAD/rlQA/65UAP+uVAD/rlQA/65UAP+uVAD/rlQA/65U
        AP+uVAD/rlQA/65UAP+uVAD/rlQA/65UAP+uVAD/rlQA/65UAP+uVAD/rlQA/65UAP+uVAD/rlQA/65U
        ANatUwBJq1MAAK5TAACqUwAAsVUAMbJWAJ+zVgDls1YA/bNWAP+zVgD/s1YA/7NWAP+zVgD/s1YA/7NW
        AP+zVgD/s1YA/7NWAP+zVgD/s1YA/7NWAP+zVgD/s1YA/7NWAP+zVgD/s1YA/7NWAP+zVgD/s1YA/bNW
        AOWyVgCesVUAMKpTAACuUwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=
</value>
  </data>
  <data name="$this.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>375, 330</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>乐凯工业数字影像</value>
  </data>
  <data name="&gt;&gt;fileToolStripMenuItem.Name" xml:space="preserve">
    <value>fileToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;fileToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;addFilesToolStripMenuItem.Name" xml:space="preserve">
    <value>addFilesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;addFilesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;openDirectoryToolStripMenuItem.Name" xml:space="preserve">
    <value>openDirectoryToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;openDirectoryToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator5.Name" xml:space="preserve">
    <value>toolStripSeparator5</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator5.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;saveImagesAsToolStripMenuItem.Name" xml:space="preserve">
    <value>saveImagesAsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;saveImagesAsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;burnAndSaveToDICOMFileToolStripMenuItem.Name" xml:space="preserve">
    <value>burnAndSaveToDICOMFileToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;burnAndSaveToDICOMFileToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;saveViewerScreenshotToolStripMenuItem.Name" xml:space="preserve">
    <value>saveViewerScreenshotToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;saveViewerScreenshotToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator12.Name" xml:space="preserve">
    <value>toolStripSeparator12</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator12.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;closeFilesToolStripMenuItem.Name" xml:space="preserve">
    <value>closeFilesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;closeFilesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Name" xml:space="preserve">
    <value>toolStripSeparator2</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;exitToolStripMenuItem.Name" xml:space="preserve">
    <value>exitToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;exitToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;editToolStripMenuItem.Name" xml:space="preserve">
    <value>editToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;editToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cutToolStripMenuItem.Name" xml:space="preserve">
    <value>cutToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;cutToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;copyToolStripMenuItem.Name" xml:space="preserve">
    <value>copyToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;copyToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pasteToolStripMenuItem.Name" xml:space="preserve">
    <value>pasteToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;pasteToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;deleteToolStripMenuItem.Name" xml:space="preserve">
    <value>deleteToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;deleteToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;deleteAllToolStripMenuItem.Name" xml:space="preserve">
    <value>deleteAllToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;deleteAllToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;viewToolStripMenuItem.Name" xml:space="preserve">
    <value>viewToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;viewToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;imageViewerSettingsToolStripMenuItem.Name" xml:space="preserve">
    <value>imageViewerSettingsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;imageViewerSettingsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rotateViewToolStripMenuItem.Name" xml:space="preserve">
    <value>rotateViewToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;rotateViewToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;clockwiseToolStripMenuItem.Name" xml:space="preserve">
    <value>clockwiseToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;clockwiseToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;counterclockwiseToolStripMenuItem.Name" xml:space="preserve">
    <value>counterclockwiseToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;counterclockwiseToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Name" xml:space="preserve">
    <value>toolStripSeparator1</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;fullScreenToolStripMenuItem.Name" xml:space="preserve">
    <value>fullScreenToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;fullScreenToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;showViewerScrollbarsToolStripMenuItem.Name" xml:space="preserve">
    <value>showViewerScrollbarsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;showViewerScrollbarsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;showBrowseScrollbarToolStripMenuItem.Name" xml:space="preserve">
    <value>showBrowseScrollbarToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;showBrowseScrollbarToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator4.Name" xml:space="preserve">
    <value>toolStripSeparator4</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;showOverlayImagesToolStripMenuItem.Name" xml:space="preserve">
    <value>showOverlayImagesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;showOverlayImagesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;overlayColorToolStripMenuItem.Name" xml:space="preserve">
    <value>overlayColorToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;overlayColorToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator6.Name" xml:space="preserve">
    <value>toolStripSeparator6</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator6.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;showMetadataInViewerToolStripMenuItem.Name" xml:space="preserve">
    <value>showMetadataInViewerToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;showMetadataInViewerToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textOverlaySettingsToolStripMenuItem.Name" xml:space="preserve">
    <value>textOverlaySettingsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;textOverlaySettingsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator8.Name" xml:space="preserve">
    <value>toolStripSeparator8</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator8.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;showRulersInViewerToolStripMenuItem.Name" xml:space="preserve">
    <value>showRulersInViewerToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;showRulersInViewerToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rulersColorToolStripMenuItem.Name" xml:space="preserve">
    <value>rulersColorToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;rulersColorToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rulersUnitOfMeasureToolStripMenuItem.Name" xml:space="preserve">
    <value>rulersUnitOfMeasureToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;rulersUnitOfMeasureToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator7.Name" xml:space="preserve">
    <value>toolStripSeparator7</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator7.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;voiLutToolStripMenuItem.Name" xml:space="preserve">
    <value>voiLutToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;voiLutToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;negativeImageToolStripMenuItem.Name" xml:space="preserve">
    <value>negativeImageToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;negativeImageToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;voiLutMouseMoveDirectionToolStripMenuItem.Name" xml:space="preserve">
    <value>voiLutMouseMoveDirectionToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;voiLutMouseMoveDirectionToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;widthHorizontalInvertedCenterVerticalToolStripMenuItem.Name" xml:space="preserve">
    <value>widthHorizontalInvertedCenterVerticalToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;widthHorizontalInvertedCenterVerticalToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;widthHorizontalCenterVerticalToolStripMenuItem.Name" xml:space="preserve">
    <value>widthHorizontalCenterVerticalToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;widthHorizontalCenterVerticalToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;widthVerticalCenterHorizontalToolStripMenuItem.Name" xml:space="preserve">
    <value>widthVerticalCenterHorizontalToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;widthVerticalCenterHorizontalToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator23.Name" xml:space="preserve">
    <value>toolStripSeparator23</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator23.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;magnifierSettingsToolStripMenuItem.Name" xml:space="preserve">
    <value>magnifierSettingsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;magnifierSettingsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;metadataToolStripMenuItem.Name" xml:space="preserve">
    <value>metadataToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;metadataToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;fileMetadataToolStripMenuItem.Name" xml:space="preserve">
    <value>fileMetadataToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;fileMetadataToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageToolStripMenuItem.Name" xml:space="preserve">
    <value>pageToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;pageToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;overlayImagesToolStripMenuItem.Name" xml:space="preserve">
    <value>overlayImagesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;overlayImagesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolsToolStripMenuItem.Name" xml:space="preserve">
    <value>toolsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;toolsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;showAnimationToolStripMenuItem.Name" xml:space="preserve">
    <value>showAnimationToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;showAnimationToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;animationDelayToolStripMenuItem.Name" xml:space="preserve">
    <value>animationDelayToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;animationDelayToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;animationDelay_valueToolStripComboBox.Name" xml:space="preserve">
    <value>animationDelay_valueToolStripComboBox</value>
  </data>
  <data name="&gt;&gt;animationDelay_valueToolStripComboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripComboBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;animationRepeatToolStripMenuItem.Name" xml:space="preserve">
    <value>animationRepeatToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;animationRepeatToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;saveAsGifFileToolStripMenuItem.Name" xml:space="preserve">
    <value>saveAsGifFileToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;saveAsGifFileToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;annotationsToolStripMenuItem.Name" xml:space="preserve">
    <value>annotationsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;annotationsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;infoToolStripMenuItem.Name" xml:space="preserve">
    <value>infoToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;infoToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator9.Name" xml:space="preserve">
    <value>toolStripSeparator9</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator9.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;interactionModeToolStripMenuItem.Name" xml:space="preserve">
    <value>interactionModeToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;interactionModeToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;interactionMode_noneToolStripMenuItem.Name" xml:space="preserve">
    <value>interactionMode_noneToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;interactionMode_noneToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;interactionMode_viewToolStripMenuItem.Name" xml:space="preserve">
    <value>interactionMode_viewToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;interactionMode_viewToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;interactionMode_authorToolStripMenuItem.Name" xml:space="preserve">
    <value>interactionMode_authorToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;interactionMode_authorToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator10.Name" xml:space="preserve">
    <value>toolStripSeparator10</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator10.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;presentationStateToolStripMenuItem.Name" xml:space="preserve">
    <value>presentationStateToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;presentationStateToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;presentationStateLoadToolStripMenuItem.Name" xml:space="preserve">
    <value>presentationStateLoadToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;presentationStateLoadToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;presentationStateInfoToolStripMenuItem.Name" xml:space="preserve">
    <value>presentationStateInfoToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;presentationStateInfoToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;presentationStateSaveToolStripMenuItem.Name" xml:space="preserve">
    <value>presentationStateSaveToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;presentationStateSaveToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;presentationStateSaveToToolStripMenuItem.Name" xml:space="preserve">
    <value>presentationStateSaveToToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;presentationStateSaveToToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;binaryFormatToolStripMenuItem.Name" xml:space="preserve">
    <value>binaryFormatToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;binaryFormatToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;binaryFormatLoadToolStripMenuItem.Name" xml:space="preserve">
    <value>binaryFormatLoadToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;binaryFormatLoadToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;binaryFormatSaveToToolStripMenuItem.Name" xml:space="preserve">
    <value>binaryFormatSaveToToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;binaryFormatSaveToToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;xmpFormatToolStripMenuItem.Name" xml:space="preserve">
    <value>xmpFormatToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;xmpFormatToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;xmpFormatLoadToolStripMenuItem.Name" xml:space="preserve">
    <value>xmpFormatLoadToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;xmpFormatLoadToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;xmpFormatSaveToToolStripMenuItem.Name" xml:space="preserve">
    <value>xmpFormatSaveToToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;xmpFormatSaveToToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator11.Name" xml:space="preserve">
    <value>toolStripSeparator11</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator11.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;addToolStripMenuItem.Name" xml:space="preserve">
    <value>addToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;addToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pointToolStripMenuItem.Name" xml:space="preserve">
    <value>pointToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;pointToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;circleToolStripMenuItem.Name" xml:space="preserve">
    <value>circleToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;circleToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;polylineToolStripMenuItem.Name" xml:space="preserve">
    <value>polylineToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;polylineToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;interpolatedToolStripMenuItem.Name" xml:space="preserve">
    <value>interpolatedToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;interpolatedToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator15.Name" xml:space="preserve">
    <value>toolStripSeparator15</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator15.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rectangleToolStripMenuItem.Name" xml:space="preserve">
    <value>rectangleToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;rectangleToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ellipseToolStripMenuItem.Name" xml:space="preserve">
    <value>ellipseToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;ellipseToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;multilineToolStripMenuItem.Name" xml:space="preserve">
    <value>multilineToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;multilineToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rangelineToolStripMenuItem.Name" xml:space="preserve">
    <value>rangelineToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;rangelineToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;infinitelineToolStripMenuItem.Name" xml:space="preserve">
    <value>infinitelineToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;infinitelineToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cutlineToolStripMenuItem.Name" xml:space="preserve">
    <value>cutlineToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;cutlineToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;arrowToolStripMenuItem.Name" xml:space="preserve">
    <value>arrowToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;arrowToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;axisToolStripMenuItem.Name" xml:space="preserve">
    <value>axisToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;axisToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rulerToolStripMenuItem.Name" xml:space="preserve">
    <value>rulerToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;rulerToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;crosshairToolStripMenuItem.Name" xml:space="preserve">
    <value>crosshairToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;crosshairToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator16.Name" xml:space="preserve">
    <value>toolStripSeparator16</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator16.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textToolStripMenuItem.Name" xml:space="preserve">
    <value>textToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;textToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Name" xml:space="preserve">
    <value>toolStripSeparator3</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;propertiesToolStripMenuItem.Name" xml:space="preserve">
    <value>propertiesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;propertiesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;helpToolStripMenuItem.Name" xml:space="preserve">
    <value>helpToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;helpToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;aboutToolStripMenuItem.Name" xml:space="preserve">
    <value>aboutToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;aboutToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;progressBar1.Name" xml:space="preserve">
    <value>progressBar1</value>
  </data>
  <data name="&gt;&gt;progressBar1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripProgressBar, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;springStripStatusLabel1.Name" xml:space="preserve">
    <value>springStripStatusLabel1</value>
  </data>
  <data name="&gt;&gt;springStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nsnrStripStatusLabel1.Name" xml:space="preserve">
    <value>nsnrStripStatusLabel1</value>
  </data>
  <data name="&gt;&gt;nsnrStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;separator1.Name" xml:space="preserve">
    <value>separator1</value>
  </data>
  <data name="&gt;&gt;separator1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Name" xml:space="preserve">
    <value>toolStripStatusLabel1</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;separator2.Name" xml:space="preserve">
    <value>separator2</value>
  </data>
  <data name="&gt;&gt;separator2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;windowLevelStripStatusLabel1.Name" xml:space="preserve">
    <value>windowLevelStripStatusLabel1</value>
  </data>
  <data name="&gt;&gt;windowLevelStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;zoomStripStatusLabel1.Name" xml:space="preserve">
    <value>zoomStripStatusLabel1</value>
  </data>
  <data name="&gt;&gt;zoomStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;imageInfoToolStripStatusLabel.Name" xml:space="preserve">
    <value>imageInfoToolStripStatusLabel</value>
  </data>
  <data name="&gt;&gt;imageInfoToolStripStatusLabel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;openDicomFileDialog.Name" xml:space="preserve">
    <value>openDicomFileDialog</value>
  </data>
  <data name="&gt;&gt;openDicomFileDialog.Type" xml:space="preserve">
    <value>System.Windows.Forms.OpenFileDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;voiLutsToolStripSplitButton.Name" xml:space="preserve">
    <value>voiLutsToolStripSplitButton</value>
  </data>
  <data name="&gt;&gt;voiLutsToolStripSplitButton.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSplitButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripLabel1.Name" xml:space="preserve">
    <value>toolStripLabel1</value>
  </data>
  <data name="&gt;&gt;toolStripLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;annotationInteractionModeToolStripComboBox.Name" xml:space="preserve">
    <value>annotationInteractionModeToolStripComboBox</value>
  </data>
  <data name="&gt;&gt;annotationInteractionModeToolStripComboBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripComboBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;colorDialog1.Name" xml:space="preserve">
    <value>colorDialog1</value>
  </data>
  <data name="&gt;&gt;colorDialog1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColorDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;openDicomAnnotationsFileDialog.Name" xml:space="preserve">
    <value>openDicomAnnotationsFileDialog</value>
  </data>
  <data name="&gt;&gt;openDicomAnnotationsFileDialog.Type" xml:space="preserve">
    <value>System.Windows.Forms.OpenFileDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;saveDicomAnnotationsFileDialog.Name" xml:space="preserve">
    <value>saveDicomAnnotationsFileDialog</value>
  </data>
  <data name="&gt;&gt;saveDicomAnnotationsFileDialog.Type" xml:space="preserve">
    <value>System.Windows.Forms.SaveFileDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;saveFileDialog1.Name" xml:space="preserve">
    <value>saveFileDialog1</value>
  </data>
  <data name="&gt;&gt;saveFileDialog1.Type" xml:space="preserve">
    <value>System.Windows.Forms.SaveFileDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;saveFileDialog2.Name" xml:space="preserve">
    <value>saveFileDialog2</value>
  </data>
  <data name="&gt;&gt;saveFileDialog2.Type" xml:space="preserve">
    <value>System.Windows.Forms.SaveFileDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog1.Name" xml:space="preserve">
    <value>folderBrowserDialog1</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog1.Type" xml:space="preserve">
    <value>System.Windows.Forms.FolderBrowserDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>MainForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>