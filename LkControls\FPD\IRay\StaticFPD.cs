﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.DirectoryServices.ActiveDirectory;
using System.Drawing.Imaging;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Vintasoft.Imaging;
using Vintasoft.Imaging.UI;
using Vintasoft.Imaging.Codecs.ImageFiles.Dicom;
using Vintasoft.Imaging.Codecs.ImageFiles.Tiff;
using FellowOakDicom;
using FellowOakDicom.IO.Buffer;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using CommunityToolkit.HighPerformance.Helpers;
using Newtonsoft.Json.Linq;
using System.Reflection.Metadata.Ecma335;
using NLog;
using OpenCvSharp;
using System.Timers;
using static OpenCvSharp.Stitcher;
using static System.Runtime.InteropServices.JavaScript.JSType;
using static System.Windows.Forms.AxHost;
using iDetector;

namespace InduVision.LkControls.FPD.IRay
{

    public class StaticFPD
    {

        #region Constant

        #endregion

        #region DLL
        [DllImport("kernel32")]
        private static extern long WritePrivateProfileString(string section, string key, string val, string filePath);

        [DllImport("kernel32")]
        private static extern int GetPrivateProfileString(string section, string key, string def, StringBuilder retVal, int size, string filePath);
        #endregion

        static Detector DetInstance;

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static event Action<short[]> ImageRecv;

        static int iImageByteSize = 0;
        public static string deviceID = null;

        public static JArray status = new JArray();

        public static short[] imageData = null;

        public static System.Timers.Timer timer = null;

        public static bool SystemInfoProcessCallbackFunc(int iCodeIndex, string sContent, string sDeviceID)
        {
            JObject newObject = new JObject
            {
                { "phase", 13 },
                { "code", iCodeIndex }
            };
            status.Add(newObject);
            Debug.WriteLine("{phase:13, code: " + iCodeIndex + "}");
            return true;
        }

        public static unsafe bool ImageDataRecvCallbackFunc(IntPtr pImage, int iFlag, string sDeviceID)
        {
            if (pImage == IntPtr.Zero || iImageByteSize <= 0) return false;

            imageData = new short[iImageByteSize];

            // 将数据从非托管内存复制到托管内存
            Marshal.Copy(pImage, imageData, 0, iImageByteSize);

            ImageRecv?.Invoke(imageData);

            return true;
        }

        public static (int, string, int, int) connect(string pcIp, int pcPort, string fpdIp, int fpdPort) {
            status.Clear();

            string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            string workDir = Path.Combine(appDirectory, "work_dir");
            string workDirPath = Path.Combine(appDirectory, "workdir_path.txt");
            string NDT1013LA = Path.Combine(workDir, "NDT1013LA");
            string configFile = Path.Combine(NDT1013LA, "config.ini");

            File.WriteAllText(workDirPath, "\\work_dir\\NDT1013LA");

            WritePrivateProfileString("Connection", "Cfg_HostIP", pcIp, configFile);
            WritePrivateProfileString("Connection", "Cfg_HostPort", pcPort.ToString(), configFile);
            WritePrivateProfileString("Connection", "Cfg_RemoteIP", fpdIp, configFile);
            WritePrivateProfileString("Connection", "Cfg_RemotePort", fpdPort.ToString(), configFile);

            try
            {
                DetInstance = new Detector();
                int ret = DetInstance.Create(Detector.GetWorkDirPath(), OnSdkCallback);
                if (SdkInterface.Err_OK != ret)
                {
                    return (ret, DetInstance.GetErrorInfo(ret), 0, 0);
                }

                ret = DetInstance.SyncInvoke(SdkInterface.Cmd_Connect, 20000);
                if (SdkInterface.Err_OK != ret)
                {
                    string test = DetInstance.GetErrorInfo(ret);
                    return (ret, DetInstance.GetErrorInfo(ret), 0, 0);
                }

                ret = DetInstance.SyncInvoke(SdkInterface.Cmd_SetCaliSubset, "Mode3", 5000);
                if (SdkInterface.Err_OK != ret)
                {
                    string test = DetInstance.GetErrorInfo(ret);
                    return (ret, DetInstance.GetErrorInfo(ret), 0, 0);
                }

                int correction = (int)(Enm_CorrectOption.Enm_CorrectOp_HW_PostOffset | Enm_CorrectOption.Enm_CorrectOp_HW_Gain | Enm_CorrectOption.Enm_CorrectOp_HW_Defect);
                ret = DetInstance.SyncInvoke(SdkInterface.Cmd_SetCorrectOption, correction, 5000);
                if (SdkInterface.Err_OK != ret)
                {
                    string test = DetInstance.GetErrorInfo(ret);
                    return (ret, DetInstance.GetErrorInfo(ret), 0, 0);
                }

                int width = DetInstance.GetAttrInt(SdkInterface.Attr_Width);
                int height = DetInstance.GetAttrInt(SdkInterface.Attr_Height);

                return (ret, "ok", width, height);
            }
            catch (Exception e)
            {
                return (SdkInterface.Err_Unknown, e.Message, 0, 0);
            }
        }

        public static void disconnect()
        {
            DetInstance.Dispose();
            DetInstance = null;
        }

        public static void OnSdkCallback(int nEventID, int nEventLevel,
          string strMsg, int nParam1, int nParam2, int nPtrParamLen, IntPtr pParam)
        {
            switch (nEventID)
            {
                case SdkInterface.Evt_Image:
                    {
                        //must make deep copies of pParam
                        IRayImage image = (IRayImage)Marshal.PtrToStructure(pParam, typeof(IRayImage));
                        int img_width = image.nWidth;
                        int img_height = image.nHeight;

                        imageData = new short[img_width * img_height];
                        Marshal.Copy(image.pData, imageData, 0, imageData.Length);

                        ImageRecv?.Invoke(imageData);
                    }
                    break;
                default:
                    break;
            }
        }

        public static bool prepare() {
            DetInstance.Invoke(SdkInterface.Cmd_Clear);
            return true;
        }

        public static bool capture() {
            DetInstance.Invoke(SdkInterface.Cmd_StartAcq);
            return true;
        }

     }
}
