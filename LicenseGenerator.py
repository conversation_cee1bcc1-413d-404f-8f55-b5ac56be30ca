import hashlib
import base64
import json
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import time

# 密钥（32 字节）
SECRET_KEY = b"MySecretKey12345"

def encrypt(data, key):
    """AES 加密"""
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
    encryptor = cipher.encryptor()
    padded_data = data + chr(16 - len(data) % 16) * (16 - len(data) % 16)  # PKCS7 填充
    encrypted = encryptor.update(padded_data.encode()) + encryptor.finalize()
    return base64.b64encode(encrypted).decode()

def generate_license(device_id, expiry_days):
    """生成包含时间戳的授权文件"""
    # 获取当前时间戳（以秒为单位）
    timestamp = int(time.time())
    
    # 授权数据，包括设备ID、有效期和时间戳
    license_data = {
        "device_id": device_id,
        "expiry_timestamp": timestamp + expiry_days * 24 * 3600  # 到期时间戳
    }

    json_data = json.dumps(license_data)
    encrypted_data = encrypt(json_data, SECRET_KEY)

    # 将加密数据写入文件
    with open("D:/license.dat", "w") as f:
        f.write(encrypted_data)

    print(f"✅ 授权文件已生成。有效期为 {expiry_days} 天。")

generate_license("0C481DD5BCFAA2C0BB78EB8D22706F219A518C7ED75F62134EC143327F11C82F", 60)  # 30 天有效期
