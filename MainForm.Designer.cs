using DicomViewerDemo;
using LkControls.LayoutControls;
using System.Drawing;
using System.Windows.Forms;
using Vintasoft.Imaging;

namespace InduVision
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            splitContainer1 = new SplitContainer();
            leftPanel = new LeftPanel();
            splitContainer2 = new SplitContainer();
            imageViewer1 = new Vintasoft.Imaging.UI.ImageViewer();
            toolsPanel = new ToolsPanel();
            basicOperationLabel = new Label();
            dicomSeriesManagerControl1 = new Vintasoft.Imaging.Dicom.UI.DicomSeriesManagerControl();
            menuStrip1 = new MenuStrip();
            fileToolStripMenuItem = new ToolStripMenuItem();
            addFilesToolStripMenuItem = new ToolStripMenuItem();
            openDirectoryToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator5 = new ToolStripSeparator();
            saveImagesAsToolStripMenuItem = new ToolStripMenuItem();
            burnAndSaveToDICOMFileToolStripMenuItem = new ToolStripMenuItem();
            saveViewerScreenshotToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator12 = new ToolStripSeparator();
            closeFilesToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator2 = new ToolStripSeparator();
            exitToolStripMenuItem = new ToolStripMenuItem();
            editToolStripMenuItem = new ToolStripMenuItem();
            cutToolStripMenuItem = new ToolStripMenuItem();
            copyToolStripMenuItem = new ToolStripMenuItem();
            pasteToolStripMenuItem = new ToolStripMenuItem();
            deleteToolStripMenuItem = new ToolStripMenuItem();
            deleteAllToolStripMenuItem = new ToolStripMenuItem();
            viewToolStripMenuItem = new ToolStripMenuItem();
            imageViewerSettingsToolStripMenuItem = new ToolStripMenuItem();
            rotateViewToolStripMenuItem = new ToolStripMenuItem();
            clockwiseToolStripMenuItem = new ToolStripMenuItem();
            counterclockwiseToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator1 = new ToolStripSeparator();
            fullScreenToolStripMenuItem = new ToolStripMenuItem();
            showViewerScrollbarsToolStripMenuItem = new ToolStripMenuItem();
            showBrowseScrollbarToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator4 = new ToolStripSeparator();
            showOverlayImagesToolStripMenuItem = new ToolStripMenuItem();
            overlayColorToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator6 = new ToolStripSeparator();
            showMetadataInViewerToolStripMenuItem = new ToolStripMenuItem();
            textOverlaySettingsToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator8 = new ToolStripSeparator();
            showRulersInViewerToolStripMenuItem = new ToolStripMenuItem();
            rulersColorToolStripMenuItem = new ToolStripMenuItem();
            rulersUnitOfMeasureToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator7 = new ToolStripSeparator();
            voiLutToolStripMenuItem = new ToolStripMenuItem();
            negativeImageToolStripMenuItem = new ToolStripMenuItem();
            voiLutMouseMoveDirectionToolStripMenuItem = new ToolStripMenuItem();
            widthHorizontalInvertedCenterVerticalToolStripMenuItem = new ToolStripMenuItem();
            widthHorizontalCenterVerticalToolStripMenuItem = new ToolStripMenuItem();
            widthVerticalCenterHorizontalToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator23 = new ToolStripSeparator();
            magnifierSettingsToolStripMenuItem = new ToolStripMenuItem();
            metadataToolStripMenuItem = new ToolStripMenuItem();
            fileMetadataToolStripMenuItem = new ToolStripMenuItem();
            pageToolStripMenuItem = new ToolStripMenuItem();
            overlayImagesToolStripMenuItem = new ToolStripMenuItem();
            toolsToolStripMenuItem = new ToolStripMenuItem();
            showAnimationToolStripMenuItem = new ToolStripMenuItem();
            animationDelayToolStripMenuItem = new ToolStripMenuItem();
            animationDelay_valueToolStripComboBox = new ToolStripComboBox();
            animationRepeatToolStripMenuItem = new ToolStripMenuItem();
            saveAsGifFileToolStripMenuItem = new ToolStripMenuItem();
            annotationsToolStripMenuItem = new ToolStripMenuItem();
            infoToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator9 = new ToolStripSeparator();
            interactionModeToolStripMenuItem = new ToolStripMenuItem();
            interactionMode_noneToolStripMenuItem = new ToolStripMenuItem();
            interactionMode_viewToolStripMenuItem = new ToolStripMenuItem();
            interactionMode_authorToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator10 = new ToolStripSeparator();
            presentationStateToolStripMenuItem = new ToolStripMenuItem();
            presentationStateLoadToolStripMenuItem = new ToolStripMenuItem();
            presentationStateInfoToolStripMenuItem = new ToolStripMenuItem();
            presentationStateSaveToolStripMenuItem = new ToolStripMenuItem();
            presentationStateSaveToToolStripMenuItem = new ToolStripMenuItem();
            binaryFormatToolStripMenuItem = new ToolStripMenuItem();
            binaryFormatLoadToolStripMenuItem = new ToolStripMenuItem();
            binaryFormatSaveToToolStripMenuItem = new ToolStripMenuItem();
            xmpFormatToolStripMenuItem = new ToolStripMenuItem();
            xmpFormatLoadToolStripMenuItem = new ToolStripMenuItem();
            xmpFormatSaveToToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator11 = new ToolStripSeparator();
            addToolStripMenuItem = new ToolStripMenuItem();
            pointToolStripMenuItem = new ToolStripMenuItem();
            circleToolStripMenuItem = new ToolStripMenuItem();
            polylineToolStripMenuItem = new ToolStripMenuItem();
            interpolatedToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator15 = new ToolStripSeparator();
            rectangleToolStripMenuItem = new ToolStripMenuItem();
            ellipseToolStripMenuItem = new ToolStripMenuItem();
            multilineToolStripMenuItem = new ToolStripMenuItem();
            rangelineToolStripMenuItem = new ToolStripMenuItem();
            infinitelineToolStripMenuItem = new ToolStripMenuItem();
            cutlineToolStripMenuItem = new ToolStripMenuItem();
            arrowToolStripMenuItem = new ToolStripMenuItem();
            axisToolStripMenuItem = new ToolStripMenuItem();
            rulerToolStripMenuItem = new ToolStripMenuItem();
            crosshairToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator16 = new ToolStripSeparator();
            textToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator3 = new ToolStripSeparator();
            propertiesToolStripMenuItem = new ToolStripMenuItem();
            helpToolStripMenuItem = new ToolStripMenuItem();
            aboutToolStripMenuItem = new ToolStripMenuItem();
            statusStrip1 = new StatusStrip();
            progressBar1 = new ToolStripProgressBar();
            springStripStatusLabel1 = new ToolStripStatusLabel();
            nsnrStripStatusLabel1 = new ToolStripStatusLabel();
            separator1 = new ToolStripSeparator();
            toolStripStatusLabel1 = new ToolStripStatusLabel();
            separator2 = new ToolStripSeparator();
            windowLevelStripStatusLabel1 = new ToolStripStatusLabel();
            zoomStripStatusLabel1 = new ToolStripStatusLabel();
            imageInfoToolStripStatusLabel = new ToolStripStatusLabel();
            openDicomFileDialog = new OpenFileDialog();
            toolStripPanel1 = new ToolStripPanel();
            imageViewerToolStrip1 = new DemosCommonCode.Imaging.ImageViewerToolStrip();
            dicomAnnotatedViewerToolStrip1 = new DemosCommonCode.Imaging.DicomAnnotatedViewerToolStrip();
            voiLutsToolStripSplitButton = new ToolStripSplitButton();
            dicomViewerToolInteractionButtonToolStrip1 = new DicomViewerToolInteractionButtonToolStrip();
            annotationInteractionModeToolStrip = new ToolStrip();
            toolStripLabel1 = new ToolStripLabel();
            annotationInteractionModeToolStripComboBox = new ToolStripComboBox();
            annotationsToolStrip1 = new AnnotationsToolStrip();
            topPanel1 = new TopPanel();
            colorDialog1 = new ColorDialog();
            openDicomAnnotationsFileDialog = new OpenFileDialog();
            saveDicomAnnotationsFileDialog = new SaveFileDialog();
            saveFileDialog1 = new SaveFileDialog();
            saveFileDialog2 = new SaveFileDialog();
            folderBrowserDialog1 = new FolderBrowserDialog();
            ((System.ComponentModel.ISupportInitialize)splitContainer1).BeginInit();
            splitContainer1.Panel1.SuspendLayout();
            splitContainer1.Panel2.SuspendLayout();
            splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)splitContainer2).BeginInit();
            splitContainer2.Panel1.SuspendLayout();
            splitContainer2.Panel2.SuspendLayout();
            splitContainer2.SuspendLayout();
            menuStrip1.SuspendLayout();
            statusStrip1.SuspendLayout();
            toolStripPanel1.SuspendLayout();
            dicomAnnotatedViewerToolStrip1.SuspendLayout();
            annotationInteractionModeToolStrip.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer1
            // 
            resources.ApplyResources(splitContainer1, "splitContainer1");
            splitContainer1.FixedPanel = FixedPanel.Panel1;
            splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            splitContainer1.Panel1.Controls.Add(leftPanel);
            // 
            // splitContainer1.Panel2
            // 
            splitContainer1.Panel2.Controls.Add(splitContainer2);
            // 
            // leftPanel
            // 
            resources.ApplyResources(leftPanel, "leftPanel");
            leftPanel.BackColor = Color.FromArgb(20, 20, 20);
            leftPanel.Name = "leftPanel";
            // 
            // splitContainer2
            // 
            resources.ApplyResources(splitContainer2, "splitContainer2");
            splitContainer2.Name = "splitContainer2";
            // 
            // splitContainer2.Panel1
            // 
            splitContainer2.Panel1.Controls.Add(imageViewer1);
            // 
            // splitContainer2.Panel2
            // 
            splitContainer2.Panel2.Controls.Add(toolsPanel);
            // 
            // imageViewer1
            // 
            imageViewer1.AllowDrop = true;
            imageViewer1.BackColor = Color.Black;
            resources.ApplyResources(imageViewer1, "imageViewer1");
            imageViewer1.FocusPointAnchor = AnchorType.None;
            imageViewer1.IsFocusPointFixed = false;
            imageViewer1.IsKeyboardNavigationEnabled = true;
            imageViewer1.Name = "imageViewer1";
            imageViewer1.RenderingQuality = ImageRenderingQuality.Low;
            imageViewer1.ShortcutCopy = Shortcut.None;
            imageViewer1.ShortcutCut = Shortcut.None;
            imageViewer1.ShortcutDelete = Shortcut.None;
            imageViewer1.ShortcutInsert = Shortcut.None;
            imageViewer1.ShortcutSelectAll = Shortcut.None;
            imageViewer1.SizeMode = Vintasoft.Imaging.UI.ImageSizeMode.BestFit;
            imageViewer1.ImageLoadingProgress += imageViewer1_ImageLoadingProgress;
            imageViewer1.ZoomChanged += ImageViewer1_ZoomChanged;
            imageViewer1.ZoomChanging += ImageViewer1_ZoomChanging;
            imageViewer1.FocusedIndexChanged += imageViewer1_FocusedIndexChanged;
            imageViewer1.DragDrop += imageViewer1_DragDrop;
            imageViewer1.DragEnter += imageViewer1_Dragging;
            imageViewer1.DragOver += imageViewer1_Dragging;
            imageViewer1.KeyDown += imageViewer1_KeyDown;
            imageViewer1.MouseDown += ImageViewer1_MouseDown;
            imageViewer1.MouseMove += ImageViewer1_MouseMove;
            imageViewer1.MouseUp += ImageViewer1_MouseUp;
            // 
            // toolsPanel
            // 
            resources.ApplyResources(toolsPanel, "toolsPanel");
            toolsPanel.BackColor = Color.FromArgb(20, 20, 20);
            toolsPanel.Name = "toolsPanel";
            // 
            // basicOperationLabel
            // 
            resources.ApplyResources(basicOperationLabel, "basicOperationLabel");
            basicOperationLabel.Name = "basicOperationLabel";
            // 
            // dicomSeriesManagerControl1
            // 
            dicomSeriesManagerControl1.AllowDrop = true;
            dicomSeriesManagerControl1.BackColor = Color.Black;
            dicomSeriesManagerControl1.DicomViewer = imageViewer1;
            resources.ApplyResources(dicomSeriesManagerControl1, "dicomSeriesManagerControl1");
            dicomSeriesManagerControl1.IsKeyboardNavigationEnabled = true;
            dicomSeriesManagerControl1.Name = "dicomSeriesManagerControl1";
            dicomSeriesManagerControl1.PanelTextPadding = new Padding(1);
            dicomSeriesManagerControl1.PatientPanelFillColor = Color.FromArgb(80, 0, 0);
            dicomSeriesManagerControl1.PatientPanelPadding = new Padding(0, 4, 0, 0);
            dicomSeriesManagerControl1.SelectedSeriesPanelFillColor = Color.FromArgb(180, 180, 180);
            dicomSeriesManagerControl1.SeriesImageCountFont = new Font("Consolas", 10F, FontStyle.Bold);
            dicomSeriesManagerControl1.SeriesImageCountPadding = new Padding(3, 0, 3, 0);
            dicomSeriesManagerControl1.SeriesImageCountTextColor = Color.White;
            dicomSeriesManagerControl1.SeriesPanelFillColor = Color.FromArgb(120, 120, 120);
            dicomSeriesManagerControl1.SeriesPanelPadding = new Padding(0);
            dicomSeriesManagerControl1.StudyPanelFillColor = Color.FromArgb(0, 80, 0);
            dicomSeriesManagerControl1.StudyPanelPadding = new Padding(0);
            dicomSeriesManagerControl1.TextBlockFontDefaultValue = new Font("Consolas", 10F);
            dicomSeriesManagerControl1.TextBlockTextColorDefaultValue = Color.White;
            dicomSeriesManagerControl1.ThumbnailImageBorderColor = Color.FromArgb(200, 200, 200);
            dicomSeriesManagerControl1.ThumbnailImageBorderWidth = 1;
            dicomSeriesManagerControl1.ThumbnailImagePadding = new Padding(6, 0, 6, 6);
            dicomSeriesManagerControl1.ThumbnailImageSize = new Size(150, 150);
            dicomSeriesManagerControl1.AddedFileCountChanged += dicomSeriesManagerControl1_AddedFileCountChanged;
            dicomSeriesManagerControl1.AddFilesException += dicomSeriesManagerControl1_AddFilesException;
            dicomSeriesManagerControl1.DragDrop += imageViewer1_DragDrop;
            dicomSeriesManagerControl1.DragEnter += imageViewer1_Dragging;
            dicomSeriesManagerControl1.DragOver += imageViewer1_Dragging;
            // 
            // menuStrip1
            // 
            menuStrip1.BackColor = Color.FromArgb(27, 27, 27);
            menuStrip1.ForeColor = Color.White;
            menuStrip1.ImageScalingSize = new Size(20, 20);
            menuStrip1.Items.AddRange(new ToolStripItem[] { fileToolStripMenuItem, editToolStripMenuItem, viewToolStripMenuItem, metadataToolStripMenuItem, pageToolStripMenuItem, toolsToolStripMenuItem, annotationsToolStripMenuItem, helpToolStripMenuItem });
            resources.ApplyResources(menuStrip1, "menuStrip1");
            menuStrip1.Name = "menuStrip1";
            // 
            // fileToolStripMenuItem
            // 
            fileToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { addFilesToolStripMenuItem, openDirectoryToolStripMenuItem, toolStripSeparator5, saveImagesAsToolStripMenuItem, burnAndSaveToDICOMFileToolStripMenuItem, saveViewerScreenshotToolStripMenuItem, toolStripSeparator12, closeFilesToolStripMenuItem, toolStripSeparator2, exitToolStripMenuItem });
            fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            resources.ApplyResources(fileToolStripMenuItem, "fileToolStripMenuItem");
            // 
            // addFilesToolStripMenuItem
            // 
            addFilesToolStripMenuItem.Name = "addFilesToolStripMenuItem";
            resources.ApplyResources(addFilesToolStripMenuItem, "addFilesToolStripMenuItem");
            addFilesToolStripMenuItem.Click += addFilesToolStripMenuItem_Click;
            // 
            // openDirectoryToolStripMenuItem
            // 
            openDirectoryToolStripMenuItem.Name = "openDirectoryToolStripMenuItem";
            resources.ApplyResources(openDirectoryToolStripMenuItem, "openDirectoryToolStripMenuItem");
            openDirectoryToolStripMenuItem.Click += openDirectoryToolStripMenuItem_Click;
            // 
            // toolStripSeparator5
            // 
            toolStripSeparator5.Name = "toolStripSeparator5";
            resources.ApplyResources(toolStripSeparator5, "toolStripSeparator5");
            // 
            // saveImagesAsToolStripMenuItem
            // 
            saveImagesAsToolStripMenuItem.Name = "saveImagesAsToolStripMenuItem";
            resources.ApplyResources(saveImagesAsToolStripMenuItem, "saveImagesAsToolStripMenuItem");
            saveImagesAsToolStripMenuItem.Click += saveImagesAsToolStripMenuItem_Click;
            // 
            // burnAndSaveToDICOMFileToolStripMenuItem
            // 
            burnAndSaveToDICOMFileToolStripMenuItem.Name = "burnAndSaveToDICOMFileToolStripMenuItem";
            resources.ApplyResources(burnAndSaveToDICOMFileToolStripMenuItem, "burnAndSaveToDICOMFileToolStripMenuItem");
            burnAndSaveToDICOMFileToolStripMenuItem.Click += burnAndSaveToDICOMFileToolStripMenuItem_Click;
            // 
            // saveViewerScreenshotToolStripMenuItem
            // 
            saveViewerScreenshotToolStripMenuItem.Name = "saveViewerScreenshotToolStripMenuItem";
            resources.ApplyResources(saveViewerScreenshotToolStripMenuItem, "saveViewerScreenshotToolStripMenuItem");
            saveViewerScreenshotToolStripMenuItem.Click += saveViewerScreenshotToolStripMenuItem_Click;
            // 
            // toolStripSeparator12
            // 
            toolStripSeparator12.Name = "toolStripSeparator12";
            resources.ApplyResources(toolStripSeparator12, "toolStripSeparator12");
            // 
            // closeFilesToolStripMenuItem
            // 
            closeFilesToolStripMenuItem.Name = "closeFilesToolStripMenuItem";
            resources.ApplyResources(closeFilesToolStripMenuItem, "closeFilesToolStripMenuItem");
            closeFilesToolStripMenuItem.Click += closeFilesToolStripMenuItem_Click;
            // 
            // toolStripSeparator2
            // 
            toolStripSeparator2.Name = "toolStripSeparator2";
            resources.ApplyResources(toolStripSeparator2, "toolStripSeparator2");
            // 
            // exitToolStripMenuItem
            // 
            exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            resources.ApplyResources(exitToolStripMenuItem, "exitToolStripMenuItem");
            exitToolStripMenuItem.Click += exitToolStripMenuItem_Click;
            // 
            // editToolStripMenuItem
            // 
            editToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { cutToolStripMenuItem, copyToolStripMenuItem, pasteToolStripMenuItem, deleteToolStripMenuItem, deleteAllToolStripMenuItem });
            editToolStripMenuItem.Name = "editToolStripMenuItem";
            resources.ApplyResources(editToolStripMenuItem, "editToolStripMenuItem");
            editToolStripMenuItem.DropDownClosed += editToolStripMenuItem_DropDownClosed;
            editToolStripMenuItem.DropDownOpened += editToolStripMenuItem_DropDownOpened;
            // 
            // cutToolStripMenuItem
            // 
            cutToolStripMenuItem.Name = "cutToolStripMenuItem";
            resources.ApplyResources(cutToolStripMenuItem, "cutToolStripMenuItem");
            cutToolStripMenuItem.Click += cutToolStripMenuItem_Click;
            // 
            // copyToolStripMenuItem
            // 
            copyToolStripMenuItem.Name = "copyToolStripMenuItem";
            resources.ApplyResources(copyToolStripMenuItem, "copyToolStripMenuItem");
            copyToolStripMenuItem.Click += copyToolStripMenuItem_Click;
            // 
            // pasteToolStripMenuItem
            // 
            pasteToolStripMenuItem.Name = "pasteToolStripMenuItem";
            resources.ApplyResources(pasteToolStripMenuItem, "pasteToolStripMenuItem");
            pasteToolStripMenuItem.Click += pasteToolStripMenuItem_Click;
            // 
            // deleteToolStripMenuItem
            // 
            deleteToolStripMenuItem.Name = "deleteToolStripMenuItem";
            resources.ApplyResources(deleteToolStripMenuItem, "deleteToolStripMenuItem");
            deleteToolStripMenuItem.Click += deleteToolStripMenuItem_Click;
            // 
            // deleteAllToolStripMenuItem
            // 
            deleteAllToolStripMenuItem.Name = "deleteAllToolStripMenuItem";
            resources.ApplyResources(deleteAllToolStripMenuItem, "deleteAllToolStripMenuItem");
            deleteAllToolStripMenuItem.Click += deleteAllToolStripMenuItem_Click;
            // 
            // viewToolStripMenuItem
            // 
            viewToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { imageViewerSettingsToolStripMenuItem, rotateViewToolStripMenuItem, toolStripSeparator1, fullScreenToolStripMenuItem, showViewerScrollbarsToolStripMenuItem, showBrowseScrollbarToolStripMenuItem, toolStripSeparator4, showOverlayImagesToolStripMenuItem, overlayColorToolStripMenuItem, toolStripSeparator6, showMetadataInViewerToolStripMenuItem, textOverlaySettingsToolStripMenuItem, toolStripSeparator8, showRulersInViewerToolStripMenuItem, rulersColorToolStripMenuItem, rulersUnitOfMeasureToolStripMenuItem, toolStripSeparator7, voiLutToolStripMenuItem, negativeImageToolStripMenuItem, voiLutMouseMoveDirectionToolStripMenuItem, toolStripSeparator23, magnifierSettingsToolStripMenuItem });
            viewToolStripMenuItem.Name = "viewToolStripMenuItem";
            resources.ApplyResources(viewToolStripMenuItem, "viewToolStripMenuItem");
            // 
            // imageViewerSettingsToolStripMenuItem
            // 
            imageViewerSettingsToolStripMenuItem.Name = "imageViewerSettingsToolStripMenuItem";
            resources.ApplyResources(imageViewerSettingsToolStripMenuItem, "imageViewerSettingsToolStripMenuItem");
            imageViewerSettingsToolStripMenuItem.Click += imageViewerSettingsToolStripMenuItem_Click;
            // 
            // rotateViewToolStripMenuItem
            // 
            rotateViewToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { clockwiseToolStripMenuItem, counterclockwiseToolStripMenuItem });
            rotateViewToolStripMenuItem.Name = "rotateViewToolStripMenuItem";
            resources.ApplyResources(rotateViewToolStripMenuItem, "rotateViewToolStripMenuItem");
            // 
            // clockwiseToolStripMenuItem
            // 
            clockwiseToolStripMenuItem.Name = "clockwiseToolStripMenuItem";
            resources.ApplyResources(clockwiseToolStripMenuItem, "clockwiseToolStripMenuItem");
            clockwiseToolStripMenuItem.Click += clockwiseToolStripMenuItem_Click;
            // 
            // counterclockwiseToolStripMenuItem
            // 
            counterclockwiseToolStripMenuItem.Name = "counterclockwiseToolStripMenuItem";
            resources.ApplyResources(counterclockwiseToolStripMenuItem, "counterclockwiseToolStripMenuItem");
            counterclockwiseToolStripMenuItem.Click += counterclockwiseToolStripMenuItem_Click;
            // 
            // toolStripSeparator1
            // 
            toolStripSeparator1.Name = "toolStripSeparator1";
            resources.ApplyResources(toolStripSeparator1, "toolStripSeparator1");
            // 
            // fullScreenToolStripMenuItem
            // 
            fullScreenToolStripMenuItem.CheckOnClick = true;
            fullScreenToolStripMenuItem.Name = "fullScreenToolStripMenuItem";
            resources.ApplyResources(fullScreenToolStripMenuItem, "fullScreenToolStripMenuItem");
            fullScreenToolStripMenuItem.CheckedChanged += fullScreenToolStripMenuItem_CheckedChanged;
            // 
            // showViewerScrollbarsToolStripMenuItem
            // 
            showViewerScrollbarsToolStripMenuItem.Checked = true;
            showViewerScrollbarsToolStripMenuItem.CheckOnClick = true;
            showViewerScrollbarsToolStripMenuItem.CheckState = CheckState.Checked;
            showViewerScrollbarsToolStripMenuItem.Name = "showViewerScrollbarsToolStripMenuItem";
            resources.ApplyResources(showViewerScrollbarsToolStripMenuItem, "showViewerScrollbarsToolStripMenuItem");
            showViewerScrollbarsToolStripMenuItem.CheckedChanged += showViewerScrollbarsToolStripMenuItem_CheckedChanged;
            // 
            // showBrowseScrollbarToolStripMenuItem
            // 
            showBrowseScrollbarToolStripMenuItem.Checked = true;
            showBrowseScrollbarToolStripMenuItem.CheckOnClick = true;
            showBrowseScrollbarToolStripMenuItem.CheckState = CheckState.Checked;
            showBrowseScrollbarToolStripMenuItem.Name = "showBrowseScrollbarToolStripMenuItem";
            resources.ApplyResources(showBrowseScrollbarToolStripMenuItem, "showBrowseScrollbarToolStripMenuItem");
            showBrowseScrollbarToolStripMenuItem.CheckedChanged += showBrowseScrollbarToolStripMenuItem_CheckedChanged;
            // 
            // toolStripSeparator4
            // 
            toolStripSeparator4.Name = "toolStripSeparator4";
            resources.ApplyResources(toolStripSeparator4, "toolStripSeparator4");
            // 
            // showOverlayImagesToolStripMenuItem
            // 
            showOverlayImagesToolStripMenuItem.CheckOnClick = true;
            showOverlayImagesToolStripMenuItem.Name = "showOverlayImagesToolStripMenuItem";
            resources.ApplyResources(showOverlayImagesToolStripMenuItem, "showOverlayImagesToolStripMenuItem");
            showOverlayImagesToolStripMenuItem.Click += showOverlayImagesToolStripMenuItem_Click;
            // 
            // overlayColorToolStripMenuItem
            // 
            overlayColorToolStripMenuItem.Name = "overlayColorToolStripMenuItem";
            resources.ApplyResources(overlayColorToolStripMenuItem, "overlayColorToolStripMenuItem");
            overlayColorToolStripMenuItem.Click += overlayColorToolStripMenuItem_Click;
            // 
            // toolStripSeparator6
            // 
            toolStripSeparator6.Name = "toolStripSeparator6";
            resources.ApplyResources(toolStripSeparator6, "toolStripSeparator6");
            // 
            // showMetadataInViewerToolStripMenuItem
            // 
            showMetadataInViewerToolStripMenuItem.Name = "showMetadataInViewerToolStripMenuItem";
            resources.ApplyResources(showMetadataInViewerToolStripMenuItem, "showMetadataInViewerToolStripMenuItem");
            showMetadataInViewerToolStripMenuItem.Click += showMetadataOnViewerToolStripMenuItem_Click;
            // 
            // textOverlaySettingsToolStripMenuItem
            // 
            textOverlaySettingsToolStripMenuItem.Name = "textOverlaySettingsToolStripMenuItem";
            resources.ApplyResources(textOverlaySettingsToolStripMenuItem, "textOverlaySettingsToolStripMenuItem");
            textOverlaySettingsToolStripMenuItem.Click += textOverlaySettingsToolStripMenuItem_Click;
            // 
            // toolStripSeparator8
            // 
            toolStripSeparator8.Name = "toolStripSeparator8";
            resources.ApplyResources(toolStripSeparator8, "toolStripSeparator8");
            // 
            // showRulersInViewerToolStripMenuItem
            // 
            showRulersInViewerToolStripMenuItem.Name = "showRulersInViewerToolStripMenuItem";
            resources.ApplyResources(showRulersInViewerToolStripMenuItem, "showRulersInViewerToolStripMenuItem");
            showRulersInViewerToolStripMenuItem.Click += showRulersOnViewerToolStripMenuItem_Click;
            // 
            // rulersColorToolStripMenuItem
            // 
            rulersColorToolStripMenuItem.Name = "rulersColorToolStripMenuItem";
            resources.ApplyResources(rulersColorToolStripMenuItem, "rulersColorToolStripMenuItem");
            rulersColorToolStripMenuItem.Click += rulersColorToolStripMenuItem_Click;
            // 
            // rulersUnitOfMeasureToolStripMenuItem
            // 
            rulersUnitOfMeasureToolStripMenuItem.Name = "rulersUnitOfMeasureToolStripMenuItem";
            resources.ApplyResources(rulersUnitOfMeasureToolStripMenuItem, "rulersUnitOfMeasureToolStripMenuItem");
            // 
            // toolStripSeparator7
            // 
            toolStripSeparator7.Name = "toolStripSeparator7";
            resources.ApplyResources(toolStripSeparator7, "toolStripSeparator7");
            // 
            // voiLutToolStripMenuItem
            // 
            voiLutToolStripMenuItem.Name = "voiLutToolStripMenuItem";
            resources.ApplyResources(voiLutToolStripMenuItem, "voiLutToolStripMenuItem");
            voiLutToolStripMenuItem.Click += voiLutToolStripMenuItem_Click;
            // 
            // negativeImageToolStripMenuItem
            // 
            negativeImageToolStripMenuItem.Name = "negativeImageToolStripMenuItem";
            resources.ApplyResources(negativeImageToolStripMenuItem, "negativeImageToolStripMenuItem");
            negativeImageToolStripMenuItem.Click += negativeImageToolStripMenuItem_Click;
            // 
            // voiLutMouseMoveDirectionToolStripMenuItem
            // 
            voiLutMouseMoveDirectionToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { widthHorizontalInvertedCenterVerticalToolStripMenuItem, widthHorizontalCenterVerticalToolStripMenuItem, widthVerticalCenterHorizontalToolStripMenuItem });
            voiLutMouseMoveDirectionToolStripMenuItem.Name = "voiLutMouseMoveDirectionToolStripMenuItem";
            resources.ApplyResources(voiLutMouseMoveDirectionToolStripMenuItem, "voiLutMouseMoveDirectionToolStripMenuItem");
            // 
            // widthHorizontalInvertedCenterVerticalToolStripMenuItem
            // 
            widthHorizontalInvertedCenterVerticalToolStripMenuItem.Checked = true;
            widthHorizontalInvertedCenterVerticalToolStripMenuItem.CheckState = CheckState.Checked;
            widthHorizontalInvertedCenterVerticalToolStripMenuItem.Name = "widthHorizontalInvertedCenterVerticalToolStripMenuItem";
            resources.ApplyResources(widthHorizontalInvertedCenterVerticalToolStripMenuItem, "widthHorizontalInvertedCenterVerticalToolStripMenuItem");
            widthHorizontalInvertedCenterVerticalToolStripMenuItem.Click += widthHorizontalInvertedCenterVerticalToolStripMenuItem_Click;
            // 
            // widthHorizontalCenterVerticalToolStripMenuItem
            // 
            widthHorizontalCenterVerticalToolStripMenuItem.Name = "widthHorizontalCenterVerticalToolStripMenuItem";
            resources.ApplyResources(widthHorizontalCenterVerticalToolStripMenuItem, "widthHorizontalCenterVerticalToolStripMenuItem");
            widthHorizontalCenterVerticalToolStripMenuItem.Click += widthHorizontalCenterVerticalToolStripMenuItem_Click;
            // 
            // widthVerticalCenterHorizontalToolStripMenuItem
            // 
            widthVerticalCenterHorizontalToolStripMenuItem.Name = "widthVerticalCenterHorizontalToolStripMenuItem";
            resources.ApplyResources(widthVerticalCenterHorizontalToolStripMenuItem, "widthVerticalCenterHorizontalToolStripMenuItem");
            widthVerticalCenterHorizontalToolStripMenuItem.Click += widthVerticalCenterHorizontalToolStripMenuItem_Click;
            // 
            // toolStripSeparator23
            // 
            toolStripSeparator23.Name = "toolStripSeparator23";
            resources.ApplyResources(toolStripSeparator23, "toolStripSeparator23");
            // 
            // magnifierSettingsToolStripMenuItem
            // 
            magnifierSettingsToolStripMenuItem.Name = "magnifierSettingsToolStripMenuItem";
            resources.ApplyResources(magnifierSettingsToolStripMenuItem, "magnifierSettingsToolStripMenuItem");
            magnifierSettingsToolStripMenuItem.Click += magnifierSettingsToolStripMenuItem_Click;
            // 
            // metadataToolStripMenuItem
            // 
            metadataToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { fileMetadataToolStripMenuItem });
            metadataToolStripMenuItem.Name = "metadataToolStripMenuItem";
            resources.ApplyResources(metadataToolStripMenuItem, "metadataToolStripMenuItem");
            // 
            // fileMetadataToolStripMenuItem
            // 
            fileMetadataToolStripMenuItem.Name = "fileMetadataToolStripMenuItem";
            resources.ApplyResources(fileMetadataToolStripMenuItem, "fileMetadataToolStripMenuItem");
            fileMetadataToolStripMenuItem.Click += metadata_fileMetadataToolStripMenuItem_Click;
            // 
            // pageToolStripMenuItem
            // 
            pageToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { overlayImagesToolStripMenuItem });
            pageToolStripMenuItem.Name = "pageToolStripMenuItem";
            resources.ApplyResources(pageToolStripMenuItem, "pageToolStripMenuItem");
            // 
            // overlayImagesToolStripMenuItem
            // 
            overlayImagesToolStripMenuItem.Name = "overlayImagesToolStripMenuItem";
            resources.ApplyResources(overlayImagesToolStripMenuItem, "overlayImagesToolStripMenuItem");
            overlayImagesToolStripMenuItem.Click += overlayImagesToolStripMenuItem_Click;
            // 
            // toolsToolStripMenuItem
            // 
            toolsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { showAnimationToolStripMenuItem, animationDelayToolStripMenuItem, animationRepeatToolStripMenuItem, saveAsGifFileToolStripMenuItem });
            toolsToolStripMenuItem.Name = "toolsToolStripMenuItem";
            resources.ApplyResources(toolsToolStripMenuItem, "toolsToolStripMenuItem");
            // 
            // showAnimationToolStripMenuItem
            // 
            showAnimationToolStripMenuItem.CheckOnClick = true;
            showAnimationToolStripMenuItem.Name = "showAnimationToolStripMenuItem";
            resources.ApplyResources(showAnimationToolStripMenuItem, "showAnimationToolStripMenuItem");
            showAnimationToolStripMenuItem.Click += showAnimationToolStripMenuItem_Click;
            // 
            // animationDelayToolStripMenuItem
            // 
            animationDelayToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { animationDelay_valueToolStripComboBox });
            animationDelayToolStripMenuItem.Name = "animationDelayToolStripMenuItem";
            resources.ApplyResources(animationDelayToolStripMenuItem, "animationDelayToolStripMenuItem");
            // 
            // animationDelay_valueToolStripComboBox
            // 
            animationDelay_valueToolStripComboBox.Items.AddRange(new object[] { resources.GetString("animationDelay_valueToolStripComboBox.Items"), resources.GetString("animationDelay_valueToolStripComboBox.Items1"), resources.GetString("animationDelay_valueToolStripComboBox.Items2"), resources.GetString("animationDelay_valueToolStripComboBox.Items3") });
            animationDelay_valueToolStripComboBox.Name = "animationDelay_valueToolStripComboBox";
            resources.ApplyResources(animationDelay_valueToolStripComboBox, "animationDelay_valueToolStripComboBox");
            animationDelay_valueToolStripComboBox.TextChanged += animationDelayToolStripComboBox_TextChanged;
            // 
            // animationRepeatToolStripMenuItem
            // 
            animationRepeatToolStripMenuItem.Checked = true;
            animationRepeatToolStripMenuItem.CheckOnClick = true;
            animationRepeatToolStripMenuItem.CheckState = CheckState.Checked;
            animationRepeatToolStripMenuItem.Name = "animationRepeatToolStripMenuItem";
            resources.ApplyResources(animationRepeatToolStripMenuItem, "animationRepeatToolStripMenuItem");
            animationRepeatToolStripMenuItem.Click += animationRepeatToolStripMenuItem_Click;
            // 
            // saveAsGifFileToolStripMenuItem
            // 
            saveAsGifFileToolStripMenuItem.Name = "saveAsGifFileToolStripMenuItem";
            resources.ApplyResources(saveAsGifFileToolStripMenuItem, "saveAsGifFileToolStripMenuItem");
            saveAsGifFileToolStripMenuItem.Click += saveAsGifFileToolStripMenuItem_Click;
            // 
            // annotationsToolStripMenuItem
            // 
            annotationsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { infoToolStripMenuItem, toolStripSeparator9, interactionModeToolStripMenuItem, toolStripSeparator10, presentationStateToolStripMenuItem, binaryFormatToolStripMenuItem, xmpFormatToolStripMenuItem, toolStripSeparator11, addToolStripMenuItem, toolStripSeparator3, propertiesToolStripMenuItem });
            annotationsToolStripMenuItem.Name = "annotationsToolStripMenuItem";
            resources.ApplyResources(annotationsToolStripMenuItem, "annotationsToolStripMenuItem");
            // 
            // infoToolStripMenuItem
            // 
            infoToolStripMenuItem.Name = "infoToolStripMenuItem";
            resources.ApplyResources(infoToolStripMenuItem, "infoToolStripMenuItem");
            infoToolStripMenuItem.Click += infoToolStripMenuItem_Click;
            // 
            // toolStripSeparator9
            // 
            toolStripSeparator9.Name = "toolStripSeparator9";
            resources.ApplyResources(toolStripSeparator9, "toolStripSeparator9");
            // 
            // interactionModeToolStripMenuItem
            // 
            interactionModeToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { interactionMode_noneToolStripMenuItem, interactionMode_viewToolStripMenuItem, interactionMode_authorToolStripMenuItem });
            interactionModeToolStripMenuItem.Name = "interactionModeToolStripMenuItem";
            resources.ApplyResources(interactionModeToolStripMenuItem, "interactionModeToolStripMenuItem");
            // 
            // interactionMode_noneToolStripMenuItem
            // 
            interactionMode_noneToolStripMenuItem.Name = "interactionMode_noneToolStripMenuItem";
            resources.ApplyResources(interactionMode_noneToolStripMenuItem, "interactionMode_noneToolStripMenuItem");
            interactionMode_noneToolStripMenuItem.Click += noneToolStripMenuItem_Click;
            // 
            // interactionMode_viewToolStripMenuItem
            // 
            interactionMode_viewToolStripMenuItem.Name = "interactionMode_viewToolStripMenuItem";
            resources.ApplyResources(interactionMode_viewToolStripMenuItem, "interactionMode_viewToolStripMenuItem");
            interactionMode_viewToolStripMenuItem.Click += viewToolStripMenuItem_Click;
            // 
            // interactionMode_authorToolStripMenuItem
            // 
            interactionMode_authorToolStripMenuItem.Checked = true;
            interactionMode_authorToolStripMenuItem.CheckState = CheckState.Checked;
            interactionMode_authorToolStripMenuItem.Name = "interactionMode_authorToolStripMenuItem";
            resources.ApplyResources(interactionMode_authorToolStripMenuItem, "interactionMode_authorToolStripMenuItem");
            interactionMode_authorToolStripMenuItem.Click += authorToolStripMenuItem_Click;
            // 
            // toolStripSeparator10
            // 
            toolStripSeparator10.Name = "toolStripSeparator10";
            resources.ApplyResources(toolStripSeparator10, "toolStripSeparator10");
            // 
            // presentationStateToolStripMenuItem
            // 
            presentationStateToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { presentationStateLoadToolStripMenuItem, presentationStateInfoToolStripMenuItem, presentationStateSaveToolStripMenuItem, presentationStateSaveToToolStripMenuItem });
            presentationStateToolStripMenuItem.Name = "presentationStateToolStripMenuItem";
            resources.ApplyResources(presentationStateToolStripMenuItem, "presentationStateToolStripMenuItem");
            // 
            // presentationStateLoadToolStripMenuItem
            // 
            presentationStateLoadToolStripMenuItem.Name = "presentationStateLoadToolStripMenuItem";
            resources.ApplyResources(presentationStateLoadToolStripMenuItem, "presentationStateLoadToolStripMenuItem");
            presentationStateLoadToolStripMenuItem.Click += loadToolStripMenuItem_Click;
            // 
            // presentationStateInfoToolStripMenuItem
            // 
            presentationStateInfoToolStripMenuItem.Name = "presentationStateInfoToolStripMenuItem";
            resources.ApplyResources(presentationStateInfoToolStripMenuItem, "presentationStateInfoToolStripMenuItem");
            presentationStateInfoToolStripMenuItem.Click += presentationStateInfoToolStripMenuItem_Click;
            // 
            // presentationStateSaveToolStripMenuItem
            // 
            presentationStateSaveToolStripMenuItem.Name = "presentationStateSaveToolStripMenuItem";
            resources.ApplyResources(presentationStateSaveToolStripMenuItem, "presentationStateSaveToolStripMenuItem");
            presentationStateSaveToolStripMenuItem.Click += presentationStateSaveToolStripMenuItem_Click;
            // 
            // presentationStateSaveToToolStripMenuItem
            // 
            presentationStateSaveToToolStripMenuItem.Name = "presentationStateSaveToToolStripMenuItem";
            resources.ApplyResources(presentationStateSaveToToolStripMenuItem, "presentationStateSaveToToolStripMenuItem");
            presentationStateSaveToToolStripMenuItem.Click += presentationStatesSaveToToolStripMenuItem_Click;
            // 
            // binaryFormatToolStripMenuItem
            // 
            binaryFormatToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { binaryFormatLoadToolStripMenuItem, binaryFormatSaveToToolStripMenuItem });
            binaryFormatToolStripMenuItem.Name = "binaryFormatToolStripMenuItem";
            resources.ApplyResources(binaryFormatToolStripMenuItem, "binaryFormatToolStripMenuItem");
            // 
            // binaryFormatLoadToolStripMenuItem
            // 
            binaryFormatLoadToolStripMenuItem.Name = "binaryFormatLoadToolStripMenuItem";
            resources.ApplyResources(binaryFormatLoadToolStripMenuItem, "binaryFormatLoadToolStripMenuItem");
            binaryFormatLoadToolStripMenuItem.Click += binaryFormatLoadToolStripMenuItem_Click;
            // 
            // binaryFormatSaveToToolStripMenuItem
            // 
            binaryFormatSaveToToolStripMenuItem.Name = "binaryFormatSaveToToolStripMenuItem";
            resources.ApplyResources(binaryFormatSaveToToolStripMenuItem, "binaryFormatSaveToToolStripMenuItem");
            binaryFormatSaveToToolStripMenuItem.Click += binaryFormatSaveToToolStripMenuItem_Click;
            // 
            // xmpFormatToolStripMenuItem
            // 
            xmpFormatToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { xmpFormatLoadToolStripMenuItem, xmpFormatSaveToToolStripMenuItem });
            xmpFormatToolStripMenuItem.Name = "xmpFormatToolStripMenuItem";
            resources.ApplyResources(xmpFormatToolStripMenuItem, "xmpFormatToolStripMenuItem");
            // 
            // xmpFormatLoadToolStripMenuItem
            // 
            xmpFormatLoadToolStripMenuItem.Name = "xmpFormatLoadToolStripMenuItem";
            resources.ApplyResources(xmpFormatLoadToolStripMenuItem, "xmpFormatLoadToolStripMenuItem");
            xmpFormatLoadToolStripMenuItem.Click += xmpFormatLoadToolStripMenuItem_Click;
            // 
            // xmpFormatSaveToToolStripMenuItem
            // 
            xmpFormatSaveToToolStripMenuItem.Name = "xmpFormatSaveToToolStripMenuItem";
            resources.ApplyResources(xmpFormatSaveToToolStripMenuItem, "xmpFormatSaveToToolStripMenuItem");
            xmpFormatSaveToToolStripMenuItem.Click += xmpFormatSaveToToolStripMenuItem_Click;
            // 
            // toolStripSeparator11
            // 
            toolStripSeparator11.Name = "toolStripSeparator11";
            resources.ApplyResources(toolStripSeparator11, "toolStripSeparator11");
            // 
            // addToolStripMenuItem
            // 
            addToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { pointToolStripMenuItem, circleToolStripMenuItem, polylineToolStripMenuItem, interpolatedToolStripMenuItem, toolStripSeparator15, rectangleToolStripMenuItem, ellipseToolStripMenuItem, multilineToolStripMenuItem, rangelineToolStripMenuItem, infinitelineToolStripMenuItem, cutlineToolStripMenuItem, arrowToolStripMenuItem, axisToolStripMenuItem, rulerToolStripMenuItem, crosshairToolStripMenuItem, toolStripSeparator16, textToolStripMenuItem });
            addToolStripMenuItem.Name = "addToolStripMenuItem";
            resources.ApplyResources(addToolStripMenuItem, "addToolStripMenuItem");
            // 
            // pointToolStripMenuItem
            // 
            pointToolStripMenuItem.Name = "pointToolStripMenuItem";
            resources.ApplyResources(pointToolStripMenuItem, "pointToolStripMenuItem");
            pointToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // circleToolStripMenuItem
            // 
            circleToolStripMenuItem.Name = "circleToolStripMenuItem";
            resources.ApplyResources(circleToolStripMenuItem, "circleToolStripMenuItem");
            circleToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // polylineToolStripMenuItem
            // 
            polylineToolStripMenuItem.Name = "polylineToolStripMenuItem";
            resources.ApplyResources(polylineToolStripMenuItem, "polylineToolStripMenuItem");
            polylineToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // interpolatedToolStripMenuItem
            // 
            interpolatedToolStripMenuItem.Name = "interpolatedToolStripMenuItem";
            resources.ApplyResources(interpolatedToolStripMenuItem, "interpolatedToolStripMenuItem");
            interpolatedToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // toolStripSeparator15
            // 
            toolStripSeparator15.Name = "toolStripSeparator15";
            resources.ApplyResources(toolStripSeparator15, "toolStripSeparator15");
            // 
            // rectangleToolStripMenuItem
            // 
            rectangleToolStripMenuItem.Name = "rectangleToolStripMenuItem";
            resources.ApplyResources(rectangleToolStripMenuItem, "rectangleToolStripMenuItem");
            rectangleToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // ellipseToolStripMenuItem
            // 
            ellipseToolStripMenuItem.Name = "ellipseToolStripMenuItem";
            resources.ApplyResources(ellipseToolStripMenuItem, "ellipseToolStripMenuItem");
            ellipseToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // multilineToolStripMenuItem
            // 
            multilineToolStripMenuItem.Name = "multilineToolStripMenuItem";
            resources.ApplyResources(multilineToolStripMenuItem, "multilineToolStripMenuItem");
            multilineToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // rangelineToolStripMenuItem
            // 
            rangelineToolStripMenuItem.Name = "rangelineToolStripMenuItem";
            resources.ApplyResources(rangelineToolStripMenuItem, "rangelineToolStripMenuItem");
            rangelineToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // infinitelineToolStripMenuItem
            // 
            infinitelineToolStripMenuItem.Name = "infinitelineToolStripMenuItem";
            resources.ApplyResources(infinitelineToolStripMenuItem, "infinitelineToolStripMenuItem");
            infinitelineToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // cutlineToolStripMenuItem
            // 
            cutlineToolStripMenuItem.Name = "cutlineToolStripMenuItem";
            resources.ApplyResources(cutlineToolStripMenuItem, "cutlineToolStripMenuItem");
            cutlineToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // arrowToolStripMenuItem
            // 
            arrowToolStripMenuItem.Name = "arrowToolStripMenuItem";
            resources.ApplyResources(arrowToolStripMenuItem, "arrowToolStripMenuItem");
            arrowToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // axisToolStripMenuItem
            // 
            axisToolStripMenuItem.Name = "axisToolStripMenuItem";
            resources.ApplyResources(axisToolStripMenuItem, "axisToolStripMenuItem");
            axisToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // rulerToolStripMenuItem
            // 
            rulerToolStripMenuItem.Name = "rulerToolStripMenuItem";
            resources.ApplyResources(rulerToolStripMenuItem, "rulerToolStripMenuItem");
            rulerToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // crosshairToolStripMenuItem
            // 
            crosshairToolStripMenuItem.Name = "crosshairToolStripMenuItem";
            resources.ApplyResources(crosshairToolStripMenuItem, "crosshairToolStripMenuItem");
            crosshairToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // toolStripSeparator16
            // 
            toolStripSeparator16.Name = "toolStripSeparator16";
            resources.ApplyResources(toolStripSeparator16, "toolStripSeparator16");
            // 
            // textToolStripMenuItem
            // 
            textToolStripMenuItem.Name = "textToolStripMenuItem";
            resources.ApplyResources(textToolStripMenuItem, "textToolStripMenuItem");
            textToolStripMenuItem.Click += addToolStripMenuItem_Click;
            // 
            // toolStripSeparator3
            // 
            toolStripSeparator3.Name = "toolStripSeparator3";
            resources.ApplyResources(toolStripSeparator3, "toolStripSeparator3");
            // 
            // propertiesToolStripMenuItem
            // 
            propertiesToolStripMenuItem.Name = "propertiesToolStripMenuItem";
            resources.ApplyResources(propertiesToolStripMenuItem, "propertiesToolStripMenuItem");
            propertiesToolStripMenuItem.Click += propertiesToolStripMenuItem_Click;
            // 
            // helpToolStripMenuItem
            // 
            helpToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { aboutToolStripMenuItem });
            helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            resources.ApplyResources(helpToolStripMenuItem, "helpToolStripMenuItem");
            // 
            // aboutToolStripMenuItem
            // 
            aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
            resources.ApplyResources(aboutToolStripMenuItem, "aboutToolStripMenuItem");
            aboutToolStripMenuItem.Click += aboutToolStripMenuItem_Click;
            // 
            // statusStrip1
            // 
            statusStrip1.BackColor = Color.FromArgb(37, 37, 37);
            statusStrip1.ForeColor = Color.White;
            statusStrip1.ImageScalingSize = new Size(20, 20);
            statusStrip1.Items.AddRange(new ToolStripItem[] { progressBar1, springStripStatusLabel1, nsnrStripStatusLabel1, separator1, toolStripStatusLabel1, separator2, windowLevelStripStatusLabel1, zoomStripStatusLabel1, imageInfoToolStripStatusLabel });
            resources.ApplyResources(statusStrip1, "statusStrip1");
            statusStrip1.Name = "statusStrip1";
            // 
            // progressBar1
            // 
            progressBar1.Maximum = 0;
            progressBar1.Name = "progressBar1";
            resources.ApplyResources(progressBar1, "progressBar1");
            // 
            // springStripStatusLabel1
            // 
            springStripStatusLabel1.Name = "springStripStatusLabel1";
            resources.ApplyResources(springStripStatusLabel1, "springStripStatusLabel1");
            springStripStatusLabel1.Spring = true;
            // 
            // nsnrStripStatusLabel1
            // 
            nsnrStripStatusLabel1.Name = "nsnrStripStatusLabel1";
            resources.ApplyResources(nsnrStripStatusLabel1, "nsnrStripStatusLabel1");
            // 
            // separator1
            // 
            separator1.Name = "separator1";
            resources.ApplyResources(separator1, "separator1");
            // 
            // toolStripStatusLabel1
            // 
            toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            resources.ApplyResources(toolStripStatusLabel1, "toolStripStatusLabel1");
            // 
            // separator2
            // 
            separator2.Name = "separator2";
            resources.ApplyResources(separator2, "separator2");
            // 
            // windowLevelStripStatusLabel1
            // 
            windowLevelStripStatusLabel1.Name = "windowLevelStripStatusLabel1";
            resources.ApplyResources(windowLevelStripStatusLabel1, "windowLevelStripStatusLabel1");
            // 
            // zoomStripStatusLabel1
            // 
            zoomStripStatusLabel1.Name = "zoomStripStatusLabel1";
            resources.ApplyResources(zoomStripStatusLabel1, "zoomStripStatusLabel1");
            // 
            // imageInfoToolStripStatusLabel
            // 
            imageInfoToolStripStatusLabel.Name = "imageInfoToolStripStatusLabel";
            resources.ApplyResources(imageInfoToolStripStatusLabel, "imageInfoToolStripStatusLabel");
            // 
            // openDicomFileDialog
            // 
            resources.ApplyResources(openDicomFileDialog, "openDicomFileDialog");
            openDicomFileDialog.Multiselect = true;
            // 
            // toolStripPanel1
            // 
            toolStripPanel1.Controls.Add(imageViewerToolStrip1);
            toolStripPanel1.Controls.Add(dicomAnnotatedViewerToolStrip1);
            toolStripPanel1.Controls.Add(dicomViewerToolInteractionButtonToolStrip1);
            toolStripPanel1.Controls.Add(annotationInteractionModeToolStrip);
            toolStripPanel1.Controls.Add(annotationsToolStrip1);
            resources.ApplyResources(toolStripPanel1, "toolStripPanel1");
            toolStripPanel1.Name = "toolStripPanel1";
            toolStripPanel1.Orientation = Orientation.Horizontal;
            toolStripPanel1.RowMargin = new Padding(3, 0, 0, 0);
            // 
            // imageViewerToolStrip1
            // 
            imageViewerToolStrip1.AssociatedZoomTrackBar = null;
            imageViewerToolStrip1.CanNavigate = false;
            imageViewerToolStrip1.CanOpenFile = false;
            imageViewerToolStrip1.CanPrint = false;
            imageViewerToolStrip1.CanSaveFile = false;
            imageViewerToolStrip1.CaptureFromCameraButtonEnabled = true;
            resources.ApplyResources(imageViewerToolStrip1, "imageViewerToolStrip1");
            imageViewerToolStrip1.ImageScalingSize = new Size(20, 20);
            imageViewerToolStrip1.ImageViewer = imageViewer1;
            imageViewerToolStrip1.Name = "imageViewerToolStrip1";
            imageViewerToolStrip1.PrintButtonEnabled = true;
            imageViewerToolStrip1.ScanButtonEnabled = true;
            // 
            // dicomAnnotatedViewerToolStrip1
            // 
            dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool = null;
            resources.ApplyResources(dicomAnnotatedViewerToolStrip1, "dicomAnnotatedViewerToolStrip1");
            dicomAnnotatedViewerToolStrip1.ImageScalingSize = new Size(20, 20);
            dicomAnnotatedViewerToolStrip1.ImageViewer = imageViewer1;
            dicomAnnotatedViewerToolStrip1.Items.AddRange(new ToolStripItem[] { voiLutsToolStripSplitButton });
            dicomAnnotatedViewerToolStrip1.MandatoryVisualTool = null;
            dicomAnnotatedViewerToolStrip1.Name = "dicomAnnotatedViewerToolStrip1";
            dicomAnnotatedViewerToolStrip1.VisualToolsMenuItem = null;
            // 
            // voiLutsToolStripSplitButton
            // 
            voiLutsToolStripSplitButton.DisplayStyle = ToolStripItemDisplayStyle.Image;
            resources.ApplyResources(voiLutsToolStripSplitButton, "voiLutsToolStripSplitButton");
            voiLutsToolStripSplitButton.Name = "voiLutsToolStripSplitButton";
            // 
            // dicomViewerToolInteractionButtonToolStrip1
            // 
            dicomViewerToolInteractionButtonToolStrip1.DisabledInteractionModes = null;
            resources.ApplyResources(dicomViewerToolInteractionButtonToolStrip1, "dicomViewerToolInteractionButtonToolStrip1");
            dicomViewerToolInteractionButtonToolStrip1.ImageScalingSize = new Size(20, 20);
            dicomViewerToolInteractionButtonToolStrip1.Name = "dicomViewerToolInteractionButtonToolStrip1";
            dicomViewerToolInteractionButtonToolStrip1.SupportedInteractionModes = new Vintasoft.Imaging.Dicom.UI.VisualTools.DicomViewerToolInteractionMode[]
    {
    Vintasoft.Imaging.Dicom.UI.VisualTools.DicomViewerToolInteractionMode.Browse,
    Vintasoft.Imaging.Dicom.UI.VisualTools.DicomViewerToolInteractionMode.Pan,
    Vintasoft.Imaging.Dicom.UI.VisualTools.DicomViewerToolInteractionMode.Zoom,
    Vintasoft.Imaging.Dicom.UI.VisualTools.DicomViewerToolInteractionMode.WindowLevel
    };
            dicomViewerToolInteractionButtonToolStrip1.Tool = null;
            // 
            // annotationInteractionModeToolStrip
            // 
            resources.ApplyResources(annotationInteractionModeToolStrip, "annotationInteractionModeToolStrip");
            annotationInteractionModeToolStrip.ImageScalingSize = new Size(20, 20);
            annotationInteractionModeToolStrip.Items.AddRange(new ToolStripItem[] { toolStripLabel1, annotationInteractionModeToolStripComboBox });
            annotationInteractionModeToolStrip.Name = "annotationInteractionModeToolStrip";
            // 
            // toolStripLabel1
            // 
            toolStripLabel1.Dock = DockStyle.Right;
            toolStripLabel1.Name = "toolStripLabel1";
            resources.ApplyResources(toolStripLabel1, "toolStripLabel1");
            // 
            // annotationInteractionModeToolStripComboBox
            // 
            annotationInteractionModeToolStripComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            annotationInteractionModeToolStripComboBox.Name = "annotationInteractionModeToolStripComboBox";
            resources.ApplyResources(annotationInteractionModeToolStripComboBox, "annotationInteractionModeToolStripComboBox");
            annotationInteractionModeToolStripComboBox.SelectedIndexChanged += annotationInteractionModeToolStripComboBox_SelectedIndexChanged;
            // 
            // annotationsToolStrip1
            // 
            resources.ApplyResources(annotationsToolStrip1, "annotationsToolStrip1");
            annotationsToolStrip1.ImageScalingSize = new Size(20, 20);
            annotationsToolStrip1.Name = "annotationsToolStrip1";
            annotationsToolStrip1.Viewer = null;
            // 
            // topPanel1
            // 
            topPanel1.BackColor = Color.FromArgb(20, 20, 20);
            resources.ApplyResources(topPanel1, "topPanel1");
            topPanel1.Name = "topPanel1";
            // 
            // openDicomAnnotationsFileDialog
            // 
            resources.ApplyResources(openDicomAnnotationsFileDialog, "openDicomAnnotationsFileDialog");
            openDicomAnnotationsFileDialog.FilterIndex = 4;
            // 
            // saveFileDialog1
            // 
            saveFileDialog1.DefaultExt = "mpg";
            resources.ApplyResources(saveFileDialog1, "saveFileDialog1");
            // 
            // saveFileDialog2
            // 
            saveFileDialog2.DefaultExt = "gif";
            resources.ApplyResources(saveFileDialog2, "saveFileDialog2");
            // 
            // folderBrowserDialog1
            // 
            folderBrowserDialog1.ShowNewFolderButton = false;
            // 
            // MainForm
            // 
            AutoScaleMode = AutoScaleMode.None;
            resources.ApplyResources(this, "$this");
            Controls.Add(splitContainer1);
            Controls.Add(topPanel1);
            Controls.Add(statusStrip1);
            Name = "MainForm";
            FormClosing += MainForm_FormClosing;
            splitContainer1.Panel1.ResumeLayout(false);
            splitContainer1.Panel1.PerformLayout();
            splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer1).EndInit();
            splitContainer1.ResumeLayout(false);
            splitContainer2.Panel1.ResumeLayout(false);
            splitContainer2.Panel2.ResumeLayout(false);
            splitContainer2.Panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)splitContainer2).EndInit();
            splitContainer2.ResumeLayout(false);
            menuStrip1.ResumeLayout(false);
            menuStrip1.PerformLayout();
            statusStrip1.ResumeLayout(false);
            statusStrip1.PerformLayout();
            toolStripPanel1.ResumeLayout(false);
            toolStripPanel1.PerformLayout();
            dicomAnnotatedViewerToolStrip1.ResumeLayout(false);
            dicomAnnotatedViewerToolStrip1.PerformLayout();
            annotationInteractionModeToolStrip.ResumeLayout(false);
            annotationInteractionModeToolStrip.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.ToolStripMenuItem fileToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem closeFilesToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem exitToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem viewToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem pageToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem showAnimationToolStripMenuItem;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.OpenFileDialog openDicomFileDialog;
        private System.Windows.Forms.ToolStripMenuItem animationRepeatToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem imageViewerSettingsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem helpToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem aboutToolStripMenuItem;
        public System.Windows.Forms.ToolStripSeparator separator1;
        private System.Windows.Forms.ToolStripSeparator separator2;
        private System.Windows.Forms.ToolStripStatusLabel springStripStatusLabel1;
        public System.Windows.Forms.ToolStripStatusLabel nsnrStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel zoomStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel windowLevelStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel imageInfoToolStripStatusLabel;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripMenuItem showOverlayImagesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem overlayImagesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem overlayColorToolStripMenuItem;
        private System.Windows.Forms.ColorDialog colorDialog1;
        private System.Windows.Forms.ToolStripMenuItem voiLutToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator6;
        private System.Windows.Forms.ToolStripMenuItem animationDelayToolStripMenuItem;
        private System.Windows.Forms.ToolStripComboBox animationDelay_valueToolStripComboBox;
        private System.Windows.Forms.ToolStripMenuItem showMetadataInViewerToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator7;
        private System.Windows.Forms.ToolStripMenuItem showRulersInViewerToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rulersUnitOfMeasureToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem metadataToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem fileMetadataToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem textOverlaySettingsToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator8;
        private System.Windows.Forms.ToolStripMenuItem rulersColorToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem annotationsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem interactionModeToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem interactionMode_noneToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem interactionMode_viewToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem interactionMode_authorToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem infoToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator9;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator10;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator11;
        private System.Windows.Forms.ToolStripMenuItem pointToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem polylineToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem interpolatedToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator15;
        private System.Windows.Forms.ToolStripMenuItem ellipseToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem multilineToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rangelineToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem arrowToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rectangleToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator16;
        private System.Windows.Forms.ToolStripMenuItem textToolStripMenuItem;
        private AnnotationsToolStrip annotationsToolStrip1;
        private System.Windows.Forms.OpenFileDialog openDicomAnnotationsFileDialog;
        private System.Windows.Forms.ToolStrip annotationInteractionModeToolStrip;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private System.Windows.Forms.ToolStripComboBox annotationInteractionModeToolStripComboBox;
        private System.Windows.Forms.ToolStripPanel toolStripPanel1;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.SplitContainer splitContainer2;

        private Label basicOperationLabel;

        public Vintasoft.Imaging.UI.ImageViewer imageViewer1;
        private System.Windows.Forms.SaveFileDialog saveDicomAnnotationsFileDialog;
        private System.Windows.Forms.ToolStripProgressBar progressBar1;
        private System.Windows.Forms.ToolStripMenuItem presentationStateToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem presentationStateLoadToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem presentationStateSaveToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem presentationStateSaveToToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem binaryFormatToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem xmpFormatToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem binaryFormatLoadToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem binaryFormatSaveToToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem xmpFormatLoadToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem xmpFormatSaveToToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem presentationStateInfoToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem saveImagesAsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem circleToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem cutlineToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem infinitelineToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem axisToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rulerToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem crosshairToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem voiLutMouseMoveDirectionToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem widthHorizontalCenterVerticalToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem widthVerticalCenterHorizontalToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addFilesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem negativeImageToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem editToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem cutToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem copyToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem pasteToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem deleteToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem deleteAllToolStripMenuItem;
        private DemosCommonCode.Imaging.DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1;
        private System.Windows.Forms.ToolStripSplitButton voiLutsToolStripSplitButton;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator23;
        private System.Windows.Forms.ToolStripMenuItem magnifierSettingsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rotateViewToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem clockwiseToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem counterclockwiseToolStripMenuItem;
        private System.Windows.Forms.SaveFileDialog saveFileDialog1;
        private System.Windows.Forms.ToolStripMenuItem saveAsGifFileToolStripMenuItem;
        private System.Windows.Forms.SaveFileDialog saveFileDialog2;
        private System.Windows.Forms.ToolStripMenuItem fullScreenToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem showViewerScrollbarsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem widthHorizontalInvertedCenterVerticalToolStripMenuItem;
        private Vintasoft.Imaging.Dicom.UI.DicomSeriesManagerControl dicomSeriesManagerControl1;
        public LeftPanel leftPanel;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem propertiesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem openDirectoryToolStripMenuItem;
        private System.Windows.Forms.FolderBrowserDialog folderBrowserDialog1;
        private DicomViewerToolInteractionButtonToolStrip dicomViewerToolInteractionButtonToolStrip1;
        private DemosCommonCode.Imaging.ImageViewerToolStrip imageViewerToolStrip1;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolStripMenuItem burnAndSaveToDICOMFileToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator12;
        private System.Windows.Forms.ToolStripMenuItem saveViewerScreenshotToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem showBrowseScrollbarToolStripMenuItem;
        public TopPanel topPanel1;
        public ToolsPanel toolsPanel;
    }
}