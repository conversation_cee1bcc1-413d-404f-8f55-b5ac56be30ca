using System;
using System.IO;
using License;

namespace InduVision.LicenseRegistration
{
    /// <summary>
    /// 授权检查服务 - 使用License.dll SDK
    /// </summary>
    public class LicenseCheckService
    {
        private readonly NLog.Logger _log = NLog.LogManager.GetLogger("LicenseCheckService");
        private const string LICENSE_FILE_PATH = "Config/license.license";

        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseCheckService()
        {
            // 检查已有的授权文件
            CheckExistingLicense();
        }

        /// <summary>
        /// 检查已有的授权文件
        /// </summary>
        private void CheckExistingLicense()
        {
            try
            {
                if (File.Exists(LICENSE_FILE_PATH))
                {
                    // 加载授权文件
                    Status.LoadLicense(LICENSE_FILE_PATH);
                    _log.Info("找到并加载了授权文件");
                    
                    // 记录授权状态
                    LogLicenseStatus();
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "检查已有授权文件时出错");
            }
        }

        /// <summary>
        /// 记录授权状态信息
        /// </summary>
        private void LogLicenseStatus()
        {
            _log.Info("====授权状态详细信息====");
            _log.Info("授权是否有效: {0}", Status.Licensed);
            _log.Info("本机硬件码: {0}", Status.HardwareID);
            _log.Info("授权文件硬件码: {0}", Status.License_HardwareID);
            _log.Info("硬件锁是否启用: {0}", Status.Hardware_Lock_Enabled);
            _log.Info("过期日期锁是否启用: {0}", Status.Expiration_Date_Lock_Enable);

            if (Status.Expiration_Date_Lock_Enable)
            {
                _log.Info("过期日期: {0}", Status.Expiration_Date);
                _log.Info("当前时间: {0}", DateTime.Now);
                _log.Info("是否已过期: {0}", Status.Expiration_Date < DateTime.Now);
            }

            // 检查评估锁（试用版）
            _log.Info("评估锁是否启用: {0}", Status.Evaluation_Lock_Enabled);
            if (Status.Evaluation_Lock_Enabled)
            {
                _log.Info("评估类型: {0}", Status.Evaluation_Type);
                _log.Info("评估总时间: {0}", Status.Evaluation_Time);
                _log.Info("评估已用时间: {0}", Status.Evaluation_Time_Current);
                _log.Info("评估是否超时: {0}", Status.Evaluation_Time_Current >= Status.Evaluation_Time);
            }

            // 检查使用次数锁
            _log.Info("使用次数锁是否启用: {0}", Status.Number_Of_Uses_Lock_Enable);
            if (Status.Number_Of_Uses_Lock_Enable)
            {
                _log.Info("最大使用次数: {0}", Status.Number_Of_Uses);
                _log.Info("当前使用次数: {0}", Status.Number_Of_Uses_Current);
                _log.Info("使用次数是否超限: {0}", Status.Number_Of_Uses_Current >= Status.Number_Of_Uses);
            }

            // 检查实例数锁
            _log.Info("实例数锁是否启用: {0}", Status.Number_Of_Instances_Lock_Enable);
            if (Status.Number_Of_Instances_Lock_Enable)
            {
                _log.Info("最大实例数: {0}", Status.Number_Of_Instances);
            }

            // 检查授权文件本身
            var licenseData = Status.License;
            _log.Info("授权文件数据长度: {0} bytes", licenseData?.Length ?? 0);

            // 检查附加信息
            var keyValueList = Status.KeyValueList;
            if (keyValueList != null && keyValueList.Count > 0)
            {
                _log.Info("授权文件包含 {0} 个附加信息项", keyValueList.Count);
                foreach (System.Collections.DictionaryEntry item in keyValueList)
                {
                    _log.Info("附加信息: {0} = {1}", item.Key, item.Value);
                }
            }
            else
            {
                _log.Info("授权文件不包含附加信息");
            }

            _log.Info("====授权状态分析完成====");
        }

        /// <summary>
        /// 获取机器码
        /// </summary>
        /// <returns>机器码</returns>
        public string GetMachineCode()
        {
            // 获取硬件ID（使用所有可能的硬件信息）
            return Status.GetHardwareID(true, true, true, false);
        }

        /// <summary>
        /// 使用授权码注册
        /// </summary>
        /// <param name="licenseCode">授权码（Base64编码的授权文件内容）</param>
        /// <returns>是否注册成功</returns>
        public bool RegisterWithLicenseCode(string licenseCode)
        {
            if (string.IsNullOrEmpty(licenseCode))
                return false;

            try
            {
                _log.Info("开始使用授权码注册");

                // 确保Config目录存在
                string configDir = Path.GetDirectoryName(LICENSE_FILE_PATH);
                if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                // 将Base64字符串转换为字节数组
                byte[] licenseData = Convert.FromBase64String(licenseCode);
                
                // 直接使用SDK的API加载授权数据
                Status.LoadLicense(licenseData);

                // 记录授权状态
                LogLicenseStatus();

                // 只有授权验证成功后才保存文件
                bool isLicensed = Status.Licensed;
                if (isLicensed)
                {
                    // 将授权数据保存到文件（备份）
                    File.WriteAllBytes(LICENSE_FILE_PATH, licenseData);
                    _log.Info("授权验证成功，已保存授权文件");
                }
                else
                {
                    _log.Warn("授权验证失败，未保存授权文件");
                }

                // 返回授权结果
                return isLicensed;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "使用授权码注册失败");
                return false;
            }
        }

        /// <summary>
        /// 使用授权文件注册
        /// </summary>
        /// <param name="filePath">授权文件路径</param>
        /// <returns>是否注册成功</returns>
        public bool RegisterWithLicenseFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            try
            {
                _log.Info("开始使用授权文件注册，文件：{0}", filePath);
                
                // 确保Config目录存在
                string configDir = Path.GetDirectoryName(LICENSE_FILE_PATH);
                if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                // 使用SDK的API直接加载授权文件
                Status.LoadLicense(filePath);

                // 记录授权状态
                LogLicenseStatus();

                // 只有授权验证成功后才复制文件
                bool isLicensed = Status.Licensed;
                if (isLicensed)
                {
                    // 复制授权文件到应用目录（备份）
                    File.Copy(filePath, LICENSE_FILE_PATH, true);
                    _log.Info("授权验证成功，已复制授权文件");
                }
                else
                {
                    _log.Warn("授权验证失败，未复制授权文件");
                }

                // 返回授权结果
                return isLicensed;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "使用授权文件注册失败");
                return false;
            }
        }

        /// <summary>
        /// 检查授权状态
        /// </summary>
        /// <returns>授权是否有效</returns>
        public bool IsLicensed()
        {
#if DEBUG
            // DEBUG 模式下的开发验证逻辑
            _log.Info("DEBUG模式：执行开发期间的授权验证");

            // 检查基本条件：硬件ID匹配 + 未过期
            bool hardwareMatch = IsHardwareMatched();
            bool notExpired = !IsExpired();

            _log.Info("开发验证 - 硬件匹配: {0}, 未过期: {1}", hardwareMatch, notExpired);

            return hardwareMatch && notExpired;
#else
            // RELEASE 模式下使用标准的 License.dll 验证
            return Status.Licensed;
#endif
        }

        /// <summary>
        /// 获取授权过期日期
        /// </summary>
        /// <returns>过期日期</returns>
        public DateTime GetExpirationDate()
        {
            return Status.Expiration_Date;
        }
        /// <summary>
        /// 检查是否启用了日期限制
        /// </summary>
        /// <returns>是否启用日期限制</returns>
        public bool IsDateLimitEnabled()
        {
            return Status.Expiration_Date_Lock_Enable;
        }

        /// <summary>
        /// 检查是否已过期（包含时间验证）
        /// </summary>
        /// <returns>是否已过期</returns>
        public bool IsExpired()
        {
            if (!Status.Expiration_Date_Lock_Enable)
                return false;

            // 验证系统时间（需要引用 LicenseManager 中的 TimeValidator）
            // 这里简化处理，直接检查时间合理性
            DateTime currentTime = DateTime.Now;
            if (currentTime.Year < 2020 || currentTime.Year > 2050)
            {
                _log.Warn("系统时间异常，拒绝授权验证");
                return true; // 时间异常时认为已过期
            }

            return Status.Expiration_Date < currentTime;
        }

        /// <summary>
        /// 检查硬件是否匹配（强制验证）
        /// </summary>
        /// <returns>硬件是否匹配</returns>
        public bool IsHardwareMatched()
        {
            try
            {
                // 强制进行硬件验证，不依赖授权文件中的硬件锁配置
                string currentHardwareID = Status.HardwareID;
                string licenseHardwareID = Status.License_HardwareID;

                _log.Info("硬件验证 - 当前硬件ID: {0}", currentHardwareID);
                _log.Info("硬件验证 - 授权硬件ID: {0}", licenseHardwareID);

                // 如果授权文件中没有硬件ID，则拒绝
                if (string.IsNullOrEmpty(licenseHardwareID))
                {
                    _log.Warn("授权文件中缺少硬件ID");
                    return false;
                }

                // 严格比较硬件ID
                bool isMatch = string.Equals(currentHardwareID, licenseHardwareID, StringComparison.OrdinalIgnoreCase);

                if (!isMatch)
                {
                    _log.Warn("硬件ID不匹配");
                }

                return isMatch;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "硬件ID验证失败");
                return false;
            }
        }

        /// <summary>
        /// 读取授权附加信息
        /// </summary>
        /// <returns>附加信息字典</returns>
        public System.Collections.SortedList GetLicenseInfo()
        {
            return Status.KeyValueList;
        }

        /// <summary>
        /// 诊断授权文件问题
        /// </summary>
        /// <param name="filePath">授权文件路径</param>
        /// <returns>诊断报告</returns>
        public string DiagnoseLicenseFile(string filePath)
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("====授权文件诊断报告====");

            try
            {
                if (!File.Exists(filePath))
                {
                    report.AppendLine("❌ 授权文件不存在");
                    return report.ToString();
                }

                // 检查文件基本信息
                var fileInfo = new FileInfo(filePath);
                report.AppendLine($"📁 文件路径: {filePath}");
                report.AppendLine($"📏 文件大小: {fileInfo.Length} bytes");
                report.AppendLine($"📅 创建时间: {fileInfo.CreationTime}");
                report.AppendLine($"📅 修改时间: {fileInfo.LastWriteTime}");

                // 尝试加载授权文件
                Status.LoadLicense(filePath);

                // 详细检查各项条件
                report.AppendLine("\n🔍 授权验证详情:");

                // 1. 基础状态
                report.AppendLine($"Licensed 状态: {Status.Licensed}");

                // 检查授权文件本身是否是评估版
                report.AppendLine($"\n📋 授权文件类型分析:");
                bool isEvalFile = Status.Evaluation_Lock_Enabled;
                report.AppendLine($"此授权文件是否为评估版: {(isEvalFile ? "是" : "否")}");

                // 2. 硬件验证
                string currentHW = Status.HardwareID;
                string licenseHW = Status.License_HardwareID;
                bool hwMatch = string.Equals(currentHW, licenseHW, StringComparison.OrdinalIgnoreCase);

                report.AppendLine($"硬件锁启用: {Status.Hardware_Lock_Enabled}");
                report.AppendLine($"当前硬件ID: {currentHW}");
                report.AppendLine($"授权硬件ID: {licenseHW}");
                report.AppendLine($"硬件匹配: {(hwMatch ? "✅" : "❌")}");

                // 3. 时间验证
                if (Status.Expiration_Date_Lock_Enable)
                {
                    bool expired = Status.Expiration_Date < DateTime.Now;
                    report.AppendLine($"过期时间锁启用: {Status.Expiration_Date_Lock_Enable}");
                    report.AppendLine($"过期时间: {Status.Expiration_Date}");
                    report.AppendLine($"当前时间: {DateTime.Now}");
                    report.AppendLine($"是否过期: {(expired ? "❌" : "✅")}");
                }

                // 4. 评估模式检查
                if (Status.Evaluation_Lock_Enabled)
                {
                    bool evalExpired = Status.Evaluation_Time_Current >= Status.Evaluation_Time;
                    report.AppendLine($"评估锁启用: {Status.Evaluation_Lock_Enabled}");
                    report.AppendLine($"评估类型: {Status.Evaluation_Type}");
                    report.AppendLine($"评估总时间: {Status.Evaluation_Time}");
                    report.AppendLine($"已用时间: {Status.Evaluation_Time_Current}");
                    report.AppendLine($"评估超时: {(evalExpired ? "❌" : "✅")}");
                }

                // 5. 使用次数检查
                if (Status.Number_Of_Uses_Lock_Enable)
                {
                    bool usesExceeded = Status.Number_Of_Uses_Current >= Status.Number_Of_Uses;
                    report.AppendLine($"使用次数锁启用: {Status.Number_Of_Uses_Lock_Enable}");
                    report.AppendLine($"最大使用次数: {Status.Number_Of_Uses}");
                    report.AppendLine($"当前使用次数: {Status.Number_Of_Uses_Current}");
                    report.AppendLine($"次数超限: {(usesExceeded ? "❌" : "✅")}");
                }

                // 6. 实例数检查
                if (Status.Number_Of_Instances_Lock_Enable)
                {
                    report.AppendLine($"实例数锁启用: {Status.Number_Of_Instances_Lock_Enable}");
                    report.AppendLine($"最大实例数: {Status.Number_Of_Instances}");
                }

                // 7. 可能的问题分析
                report.AppendLine("\n🔧 问题分析:");

                if (!Status.Licensed)
                {
                    report.AppendLine("❌ Licensed = False 的可能原因:");

                    // 按优先级分析问题
                    if (Status.Evaluation_Lock_Enabled)
                    {
                        bool evalExpired = Status.Evaluation_Time_Current >= Status.Evaluation_Time;
                        if (evalExpired)
                        {
                            report.AppendLine("  1. ⚠️  此授权文件本身就是评估版且已过期");
                            report.AppendLine("     解决方案：生成正式版授权文件（不启用评估模式）");
                        }
                        else
                        {
                            report.AppendLine("  1. ℹ️  此授权文件是评估版但仍在有效期内");
                        }
                    }
                    else
                    {
                        report.AppendLine("  1. ✅ 此授权文件是正式版（非评估版）");

                        if (Status.Hardware_Lock_Enabled && !hwMatch)
                        {
                            report.AppendLine("  2. ❌ 硬件ID不匹配");
                            report.AppendLine("     当前硬件ID: " + Status.HardwareID);
                            report.AppendLine("     授权硬件ID: " + Status.License_HardwareID);
                        }
                        else
                        {
                            report.AppendLine("  2. ✅ 硬件ID匹配");
                        }

                        if (Status.Expiration_Date_Lock_Enable && Status.Expiration_Date < DateTime.Now)
                        {
                            report.AppendLine("  3. ❌ 授权已过期");
                            report.AppendLine($"     过期时间: {Status.Expiration_Date}");
                            report.AppendLine($"     当前时间: {DateTime.Now}");
                        }
                        else if (Status.Expiration_Date_Lock_Enable)
                        {
                            report.AppendLine("  3. ✅ 授权未过期");
                            report.AppendLine($"     过期时间: {Status.Expiration_Date}");
                        }
                        else
                        {
                            report.AppendLine("  3. ✅ 无过期时间限制");
                        }

                        if (Status.Number_Of_Uses_Lock_Enable && Status.Number_Of_Uses_Current >= Status.Number_Of_Uses)
                        {
                            report.AppendLine("  4. ❌ 使用次数已达上限");
                            report.AppendLine($"     最大次数: {Status.Number_Of_Uses}");
                            report.AppendLine($"     当前次数: {Status.Number_Of_Uses_Current}");
                        }
                        else if (Status.Number_Of_Uses_Lock_Enable)
                        {
                            report.AppendLine("  4. ✅ 使用次数未超限");
                        }
                        else
                        {
                            report.AppendLine("  4. ✅ 无使用次数限制");
                        }

                        // 如果所有条件都满足但仍然 Licensed = False
                        if (hwMatch &&
                            (!Status.Expiration_Date_Lock_Enable || Status.Expiration_Date >= DateTime.Now) &&
                            (!Status.Number_Of_Uses_Lock_Enable || Status.Number_Of_Uses_Current < Status.Number_Of_Uses))
                        {
                            report.AppendLine("\n🚨 关键问题：所有验证条件都满足，但 Licensed 仍为 False");
                            report.AppendLine("   可能原因：");
                            report.AppendLine("   - Master Key 不匹配（最可能）");
                            report.AppendLine("   - 软件保护配置问题");
                            report.AppendLine("   - 授权文件格式损坏");
                            report.AppendLine("   - .NET Reactor 版本不兼容");
                        }
                    }
                }
                else
                {
                    report.AppendLine("✅ 授权文件验证通过");
                }

            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ 诊断过程中发生错误: {ex.Message}");
            }

            report.AppendLine("====诊断完成====");
            return report.ToString();
        }

        /// <summary>
        /// 检查评估模式状态并提供解决方案
        /// </summary>
        /// <returns>评估模式分析报告</returns>
        public string AnalyzeEvaluationMode()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("====评估模式分析====");

            try
            {
                bool evalEnabled = Status.Evaluation_Lock_Enabled;
                report.AppendLine($"评估锁是否启用: {evalEnabled}");

                if (evalEnabled)
                {
                    var evalType = Status.Evaluation_Type;
                    int totalTime = Status.Evaluation_Time;
                    int currentTime = Status.Evaluation_Time_Current;
                    bool isExpired = currentTime >= totalTime;

                    report.AppendLine($"评估类型: {evalType}");
                    report.AppendLine($"评估总时间: {totalTime} {(evalType == EvaluationType.Trial_Days ? "天" : "分钟")}");
                    report.AppendLine($"已使用时间: {currentTime} {(evalType == EvaluationType.Trial_Days ? "天" : "分钟")}");
                    report.AppendLine($"剩余时间: {Math.Max(0, totalTime - currentTime)} {(evalType == EvaluationType.Trial_Days ? "天" : "分钟")}");
                    report.AppendLine($"评估状态: {(isExpired ? "❌ 已过期" : "✅ 有效")}");

                    if (isExpired)
                    {
                        report.AppendLine("\n🚨 评估模式已过期的影响:");
                        report.AppendLine("- 即使提供有效的正式授权文件，Licensed 仍可能返回 False");
                        report.AppendLine("- 这是因为 .NET Reactor 会优先检查评估模式状态");

                        report.AppendLine("\n💡 解决方案:");
                        report.AppendLine("1. 使用 InvalidateLicense() 方法清除当前授权状态");
                        report.AppendLine("2. 重新生成不包含评估模式的正式授权文件");
                        report.AppendLine("3. 或者在 .NET Reactor 中重新保护软件，不启用评估模式");
                        report.AppendLine("4. 检查注册表或应用数据目录中是否有评估模式的持久化数据");
                    }
                }
                else
                {
                    report.AppendLine("✅ 评估锁未启用");
                }

                // 检查其他可能影响 Licensed 状态的因素
                report.AppendLine($"\n其他状态检查:");
                report.AppendLine($"Licensed: {Status.Licensed}");
                report.AppendLine($"硬件锁启用: {Status.Hardware_Lock_Enabled}");
                report.AppendLine($"过期锁启用: {Status.Expiration_Date_Lock_Enable}");
                report.AppendLine($"使用次数锁启用: {Status.Number_Of_Uses_Lock_Enable}");
                report.AppendLine($"实例数锁启用: {Status.Number_Of_Instances_Lock_Enable}");

            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ 分析过程中发生错误: {ex.Message}");
            }

            report.AppendLine("====分析完成====");
            return report.ToString();
        }

        /// <summary>
        /// 尝试清除授权状态（需要确认码）
        /// </summary>
        /// <returns>确认码</returns>
        public string InvalidateCurrentLicense()
        {
            try
            {
                _log.Info("尝试清除当前授权状态");
                string confirmationCode = Status.InvalidateLicense();
                _log.Info("授权状态已清除，确认码: {0}", confirmationCode);
                return confirmationCode;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "清除授权状态失败");
                return null;
            }
        }
    }
} 