using System;
using System.IO;
using License;

namespace InduVision.LicenseRegistration
{
    /// <summary>
    /// 授权检查服务 - 使用License.dll SDK
    /// </summary>
    public class LicenseCheckService
    {
        private readonly NLog.Logger _log = NLog.LogManager.GetLogger("LicenseCheckService");
        private const string LICENSE_FILE_PATH = "Config/license.license";

        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseCheckService()
        {
            // 检查已有的授权文件
            CheckExistingLicense();
        }

        /// <summary>
        /// 检查已有的授权文件
        /// </summary>
        private void CheckExistingLicense()
        {
            try
            {
                if (File.Exists(LICENSE_FILE_PATH))
                {
                    // 加载授权文件
                    Status.LoadLicense(LICENSE_FILE_PATH);
                    _log.Info("找到并加载了授权文件");
                    
                    // 记录授权状态
                    LogLicenseStatus();
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "检查已有授权文件时出错");
            }
        }

        /// <summary>
        /// 记录授权状态信息
        /// </summary>
        private void LogLicenseStatus()
        {
            _log.Info("====授权状态====");
            _log.Info("授权是否有效: {0}", Status.Licensed);
            _log.Info("本机硬件码: {0}", Status.HardwareID);
            _log.Info("授权文件硬件码: {0}", Status.License_HardwareID);
            _log.Info("硬件锁是否启用: {0}", Status.Hardware_Lock_Enabled);
            _log.Info("过期日期锁是否启用: {0}", Status.Expiration_Date_Lock_Enable);
            if (Status.Expiration_Date_Lock_Enable)
            {
                _log.Info("过期日期: {0}", Status.Expiration_Date);
            }
        }

        /// <summary>
        /// 获取机器码
        /// </summary>
        /// <returns>机器码</returns>
        public string GetMachineCode()
        {
            // 获取硬件ID（使用所有可能的硬件信息）
            return Status.GetHardwareID(true, true, true, false);
        }

        /// <summary>
        /// 使用授权码注册
        /// </summary>
        /// <param name="licenseCode">授权码（Base64编码的授权文件内容）</param>
        /// <returns>是否注册成功</returns>
        public bool RegisterWithLicenseCode(string licenseCode)
        {
            if (string.IsNullOrEmpty(licenseCode))
                return false;

            try
            {
                _log.Info("开始使用授权码注册");

                // 确保Config目录存在
                string configDir = Path.GetDirectoryName(LICENSE_FILE_PATH);
                if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                // 将Base64字符串转换为字节数组
                byte[] licenseData = Convert.FromBase64String(licenseCode);
                
                // 直接使用SDK的API加载授权数据
                Status.LoadLicense(licenseData);

                // 记录授权状态
                LogLicenseStatus();

                // 只有授权验证成功后才保存文件
                bool isLicensed = Status.Licensed;
                if (isLicensed)
                {
                    // 将授权数据保存到文件（备份）
                    File.WriteAllBytes(LICENSE_FILE_PATH, licenseData);
                    _log.Info("授权验证成功，已保存授权文件");
                }
                else
                {
                    _log.Warn("授权验证失败，未保存授权文件");
                }

                // 返回授权结果
                return isLicensed;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "使用授权码注册失败");
                return false;
            }
        }

        /// <summary>
        /// 使用授权文件注册
        /// </summary>
        /// <param name="filePath">授权文件路径</param>
        /// <returns>是否注册成功</returns>
        public bool RegisterWithLicenseFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            try
            {
                _log.Info("开始使用授权文件注册，文件：{0}", filePath);
                
                // 确保Config目录存在
                string configDir = Path.GetDirectoryName(LICENSE_FILE_PATH);
                if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                // 使用SDK的API直接加载授权文件
                Status.LoadLicense(filePath);

                // 记录授权状态
                LogLicenseStatus();

                // 只有授权验证成功后才复制文件
                bool isLicensed = Status.Licensed;
                if (isLicensed)
                {
                    // 复制授权文件到应用目录（备份）
                    File.Copy(filePath, LICENSE_FILE_PATH, true);
                    _log.Info("授权验证成功，已复制授权文件");
                }
                else
                {
                    _log.Warn("授权验证失败，未复制授权文件");
                }

                // 返回授权结果
                return isLicensed;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "使用授权文件注册失败");
                return false;
            }
        }

        /// <summary>
        /// 检查授权状态
        /// </summary>
        /// <returns>授权是否有效</returns>
        public bool IsLicensed()
        {
#if DEBUG
            // DEBUG 模式下的开发验证逻辑
            _log.Info("DEBUG模式：执行开发期间的授权验证");

            // 检查基本条件：硬件ID匹配 + 未过期
            bool hardwareMatch = IsHardwareMatched();
            bool notExpired = !IsExpired();

            _log.Info("开发验证 - 硬件匹配: {0}, 未过期: {1}", hardwareMatch, notExpired);

            return hardwareMatch && notExpired;
#else
            // RELEASE 模式下使用标准的 License.dll 验证
            return Status.Licensed;
#endif
        }

        /// <summary>
        /// 获取授权过期日期
        /// </summary>
        /// <returns>过期日期</returns>
        public DateTime GetExpirationDate()
        {
            return Status.Expiration_Date;
        }
        /// <summary>
        /// 检查是否启用了日期限制
        /// </summary>
        /// <returns>是否启用日期限制</returns>
        public bool IsDateLimitEnabled()
        {
            return Status.Expiration_Date_Lock_Enable;
        }

        /// <summary>
        /// 检查是否已过期
        /// </summary>
        /// <returns>是否已过期</returns>
        public bool IsExpired()
        {
            return Status.Expiration_Date_Lock_Enable && Status.Expiration_Date < DateTime.Now;
        }

        /// <summary>
        /// 检查硬件是否匹配
        /// </summary>
        /// <returns>硬件是否匹配</returns>
        public bool IsHardwareMatched()
        {
            if (!Status.Hardware_Lock_Enabled)
                return true;

            return Status.HardwareID == Status.License_HardwareID;
        }

        /// <summary>
        /// 读取授权附加信息
        /// </summary>
        /// <returns>附加信息字典</returns>
        public System.Collections.SortedList GetLicenseInfo()
        {
            return Status.KeyValueList;
        }
    }
} 