﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LkControls.common
{
    public class CustomTabControl : TabControl
    {
        public Color TabBackColor { get; set; } = Color.FromArgb(32, 32, 32); // 默认Tab背景色
        public Color TabForeColor { get; set; } = Color.White;      // 默认Tab字体颜色
        public Color TabBorderColor { get; set; } = Color.Gray;    // Tab边框颜色
        public Color SelectedTabBackColor { get; set; } = Color.White; // 选中Tab背景色
        public Color SelectedTabBorderColor { get; set; } = Color.Blue; // 选中Tab边框颜色
        public Color TabControlBackColor { get; set; } = Color.WhiteSmoke; // 控件背景色

        public CustomTabControl()
        {
            DrawMode = TabDrawMode.OwnerDrawFixed;
            SizeMode = TabSizeMode.Fixed;
            SetStyle(ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            e.Graphics.Clear(Color.FromArgb(32, 32, 32)); // 设置 TabControl 背景色

            int totalWidth = TabPages.Count * ItemSize.Width; // 计算所有选项卡的总宽度
            int startX = (Width - totalWidth) / 2; // 计算起始 X 坐标，让 `Tab` 选项卡居中

            for (int i = 0; i < TabPages.Count; i++)
            {
                Rectangle tabRect = GetTabRect(i);
                bool isSelected = SelectedIndex == i;

                GraphicsPath path = GetRoundedRectPath(tabRect, 9, i == 0 ? true : false, i == TabCount - 1 ? true : false, false, false);

                using (Brush backBrush = new SolidBrush(isSelected ? Color.FromArgb(255, 136, 0) : Color.FromArgb(32, 32, 32)))
                using (Pen borderPen = new Pen(isSelected ? Color.FromArgb(122, 122, 122) : Color.FromArgb(122, 122, 122)))
                using (Brush textBrush = new SolidBrush(Color.White))
                using (StringFormat sf = new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center })
                {
                    e.Graphics.FillPath(backBrush, path);
                    e.Graphics.DrawPath(borderPen, path);

                    //DrawBorderWithoutBottom(e.Graphics, borderPen, path);

                    //e.Graphics.FillRectangle(backBrush, tabRect);

                    //e.Graphics.DrawRectangle(borderPen, tabRect);
                    // 绘制左、上、右边框（跳过底部）
                    //e.Graphics.DrawLine(borderPen, tabRect.Left, tabRect.Top, tabRect.Right, tabRect.Top);  // 顶部
                    //e.Graphics.DrawLine(borderPen, tabRect.Left, tabRect.Top, tabRect.Left, tabRect.Bottom); // 左侧
                    //e.Graphics.DrawLine(borderPen, tabRect.Right, tabRect.Top, tabRect.Right, tabRect.Bottom); // 右侧

                    e.Graphics.DrawString(TabPages[i].Text, Font, textBrush, tabRect, sf);
                }
            }
        }


        private GraphicsPath GetRoundedRectPath(Rectangle rect, int radius, bool topLeft, bool topRight, bool bottomRight, bool bottomLeft)
        {
            int diameter = radius * 2;
            GraphicsPath path = new GraphicsPath();

            // 左上角
            if (topLeft)
                path.AddArc(rect.Left, rect.Top, diameter, diameter, 180, 90);
            else
                path.AddLine(rect.Left, rect.Top, rect.Left + radius, rect.Top);

            // 右上角
            if (topRight)
                path.AddArc(rect.Right - diameter, rect.Top, diameter, diameter, 270, 90);
            else
                path.AddLine(rect.Right - radius, rect.Top, rect.Right, rect.Top);

            // 右下角
            if (bottomRight)
                path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            else
                path.AddLine(rect.Right, rect.Bottom - radius, rect.Right, rect.Bottom);

            // 左下角
            if (bottomLeft)
                path.AddArc(rect.Left, rect.Bottom - diameter, diameter, diameter, 90, 90);
            else
                path.AddLine(rect.Left, rect.Bottom, rect.Left, rect.Bottom - radius);

            path.CloseFigure();
            return path;
        }

    }
}
