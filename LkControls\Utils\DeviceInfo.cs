﻿using System;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using License;
using System.Diagnostics;
using System.IO;

namespace InduVision.LkControls.Utils
{
    internal class DeviceInfo
    {
        // 添加常量定义授权文件名称
        private const string LICENSE_FILE_NAME = "license.license";
        public static string GetHardwareId()
        {
            string hardwareId = License.Status.GetHardwareID(
                    Board: true,   // 包含主板信息
                    CPU: true,     // 包含CPU信息
                    HDD: true,     // 包含硬盘信息
                    MAC: false      // 包含网卡MAC地址
                );
            Debug.WriteLine("hardwareId: " + hardwareId);
            return hardwareId;
        }

        // 检查是否有有效的授权文件
        public static bool IsValidLicenseAvailable()
        {
            // 确保从正确位置加载授权文件
            string licenseFilePath = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, 
                LICENSE_FILE_NAME);
            
            Debug.WriteLine("检查授权文件: " + licenseFilePath);
            
            // 如果文件不存在，则无效
            if (!File.Exists(licenseFilePath))
            {
                Debug.WriteLine("授权文件不存在");
                return false;
            }
            
            try
            {
                // 尝试加载授权文件
                License.Status.LoadLicense(licenseFilePath);
                Debug.WriteLine("IsValidLicenseAvailable: " + License.Status.Licensed);
                return License.Status.Licensed;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("加载授权文件失败: " + ex.Message);
                return false;
            }
        }

        private static string GetCpuId()
        {
            ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
            foreach (ManagementObject obj in searcher.Get())
            {
                return obj["ProcessorId"].ToString();
            }
            return "UNKNOWN";
        }

        public static string GetDiskId()
        {
            ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive");
            foreach (ManagementObject obj in searcher.Get())
            {
                return obj["SerialNumber"].ToString();
            }
            return "UNKNOWN";
        }
    }
}
