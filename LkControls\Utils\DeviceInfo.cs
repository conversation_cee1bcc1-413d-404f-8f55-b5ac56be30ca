﻿using System;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace InduVision.LkControls.Utils
{
    internal class DeviceInfo
    {
        public static string GetHardwareId()
        {
            string cpuId = GetCpuId();
            string diskId = GetDiskId();
            string rawData = cpuId + diskId;

            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(rawData));
                return BitConverter.ToString(hashBytes).Replace("-", "");
            }
        }

        public static string GetCpuId()
        {
            ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
            foreach (ManagementObject obj in searcher.Get())
            {
                return obj["ProcessorId"].ToString();
            }
            return "UNKNOWN";
        }

        public static string GetDiskId()
        {
            ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive");
            foreach (ManagementObject obj in searcher.Get())
            {
                return obj["SerialNumber"].ToString();
            }
            return "UNKNOWN";
        }
    }
}
