﻿using LkControls.common;

namespace LkControls.SetPanel
{
    partial class EnhancePanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            autoWindowCheckBox = new System.Windows.Forms.CheckBox();
            gammaCheckBox = new System.Windows.Forms.CheckBox();
            groupBox1 = new System.Windows.Forms.GroupBox();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // autoWindowCheckBox
            // 
            autoWindowCheckBox.AutoSize = true;
            autoWindowCheckBox.ForeColor = System.Drawing.Color.White;
            autoWindowCheckBox.Location = new System.Drawing.Point(15, 52);
            autoWindowCheckBox.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            autoWindowCheckBox.Name = "autoWindowCheckBox";
            autoWindowCheckBox.Size = new System.Drawing.Size(121, 24);
            autoWindowCheckBox.TabIndex = 1;
            autoWindowCheckBox.Text = "高亮窗宽窗位";
            autoWindowCheckBox.UseVisualStyleBackColor = true;
            autoWindowCheckBox.CheckedChanged += AutoWindowCheckBox_CheckedChanged;
            // 
            // gammaCheckBox
            // 
            gammaCheckBox.AutoSize = true;
            gammaCheckBox.Checked = true;
            gammaCheckBox.CheckState = System.Windows.Forms.CheckState.Checked;
            gammaCheckBox.ForeColor = System.Drawing.Color.White;
            gammaCheckBox.Location = new System.Drawing.Point(15, 102);
            gammaCheckBox.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            gammaCheckBox.Name = "gammaCheckBox";
            gammaCheckBox.Size = new System.Drawing.Size(115, 24);
            gammaCheckBox.TabIndex = 8;
            gammaCheckBox.Text = "gamma校正";
            gammaCheckBox.UseVisualStyleBackColor = true;
            gammaCheckBox.CheckedChanged += GammaCheckBox_CheckedChanged;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(autoWindowCheckBox);
            groupBox1.Controls.Add(gammaCheckBox);
            groupBox1.ForeColor = System.Drawing.Color.White;
            groupBox1.Location = new System.Drawing.Point(10, 11);
            groupBox1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            groupBox1.Name = "groupBox1";
            groupBox1.Padding = new System.Windows.Forms.Padding(3, 4, 3, 4);
            groupBox1.Size = new System.Drawing.Size(343, 172);
            groupBox1.TabIndex = 17;
            groupBox1.TabStop = false;
            groupBox1.Text = "图像增强设置";
            // 
            // EnhancePanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            Controls.Add(groupBox1);
            Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            Name = "EnhancePanel";
            Size = new System.Drawing.Size(364, 202);
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion
        private System.Windows.Forms.GroupBox groupBox1;
        public System.Windows.Forms.CheckBox autoWindowCheckBox;
        public System.Windows.Forms.CheckBox gammaCheckBox;
    }
}
