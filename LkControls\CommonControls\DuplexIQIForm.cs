﻿using Newtonsoft.Json.Linq;
using OxyPlot.Series;
using OxyPlot;
using OxyPlot.WindowsForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.Reflection.Emit;
using Microsoft.VisualBasic.Logging;
using NLog;
using System.IO;
using Vintasoft.Imaging.UI;
using FellowOakDicom.Imaging.LUT;
using OpenCvSharp;
using static System.Net.Mime.MediaTypeNames;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace DicomViewerDemo.RecordDialogs
{
    
    public partial class DuplexIQIForm : Form
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private PlotView plotView;
        private TableLayoutPanel tableLayoutPanel;
        private System.Windows.Forms.Label label;
        private double[] image_data;

        private static NLog.Logger Log = LogManager.GetCurrentClassLogger();

        public DuplexIQIForm(double[] data)
        {
            this.image_data = data;
            InitializeComponent();
            InitializeControls();
        }

        private void InitializeControls()
        {
            this.plotView = new PlotView();
            this.plotView.Dock = DockStyle.Fill;

            this.label = new System.Windows.Forms.Label();
            this.label.Font = new System.Drawing.Font(label.Font.FontFamily, 12);
            this.label.Dock = DockStyle.Fill;
            this.label.AutoSize = false;
            this.label.Margin = new Padding(20);

            this.tableLayoutPanel = new TableLayoutPanel();
            this.tableLayoutPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel.RowCount = 2;
            this.tableLayoutPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 80F));
            this.tableLayoutPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tableLayoutPanel.ColumnCount = 1;
            this.tableLayoutPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel.Name = "tableLayoutPanel";
            this.tableLayoutPanel.Controls.Add(this.plotView);
            this.tableLayoutPanel.Controls.Add(this.label);
            this.Controls.Add(this.tableLayoutPanel);

            DuplexIQI(this.image_data);
            DrawCurve(this.image_data);
        }

        public void reCalculate(double[] data) {
            DuplexIQI(data);
            DrawCurve(data);
        }

        private void DrawCurve(double[] data)
        {
            // 创建 PlotModel 对象
            var plotModel = new PlotModel {};

            // 创建 LineSeries 对象
            var lineSeries = new LineSeries
            {
                Title = "样本数据",
                MarkerType = MarkerType.None,
                MarkerSize = 5,
                MarkerStroke = OxyColors.White,
                MarkerFill = OxyColors.Red
            };

            // 添加数据点

            for (int i = 0; i < data.Length; i++)
            {
                lineSeries.Points.Add(new DataPoint(i, data[i]));
            }
            
            // 将 LineSeries 添加到 PlotModel
            plotModel.Series.Add(lineSeries);

            plotView.Model = plotModel;
        }

        public void DuplexIQI(double[] grayscaleData)
        {
            if (grayscaleData == null || grayscaleData.Length < 10)
            {
                return;
            }

            label.Text = "正在计算中...";

            List<double> results = CalculateDip(grayscaleData);

            results = results.OrderByDescending(x => x).ToList();

            string text = "识别结果：";

            for (int i = 0; i < results.Count; i++)
            {
                double result = Math.Round(results[i]);
                text = text + "线对" + (i + 1) + ": " + Math.Round(results[i]) + "%   ";
            }

            label.Text = text;
        }

        public static Dictionary<string, List<int>> FindPeaksAndValleys(int[] arr)
        {
            var peaksAndValleysIndex = new List<int>();
            var peaksAndValleysValue = new List<int>();

            if (arr.Length > 1 && arr[0] >= arr[1])
            {
                peaksAndValleysIndex.Add(0);
                peaksAndValleysValue.Add(arr[0]);
            }

            for (int i = 1; i < arr.Length - 1; i++)
            {
                if ((arr[i] >= arr[i - 1] && arr[i] >= arr[i + 1]) || (arr[i] <= arr[i - 1] && arr[i] <= arr[i + 1]))
                {
                    peaksAndValleysIndex.Add(i);
                    peaksAndValleysValue.Add(arr[i]);
                }
            }

            if (arr.Length > 1 && arr[arr.Length - 1] >= arr[arr.Length - 2])
            {
                peaksAndValleysIndex.Add(arr.Length - 1);
                peaksAndValleysValue.Add(arr[arr.Length - 1]);
            }

            var map = new Dictionary<string, List<int>>
        {
            { "index", peaksAndValleysIndex },
            { "value", peaksAndValleysValue }
        };

            return map;
        }

        public double DistanceToLine(float x1, float y1, float x2, float y2, float x, float y)
        {
            // Ensure that the line is not vertical to avoid division by zero.
            if (x2 == x1)
            {
                // For vertical line case, distance is simply the horizontal distance.
                return Math.Abs(x - x1);
            }

            // Calculate the slope (m) and intercept (b) of the line.
            float slope = (y2 - y1) / (x2 - x1);
            float intercept = y1 - slope * x1;

            // Distance from point (x, y) to the line y = slope * x + intercept.
            double distance = Math.Abs(slope * x - y + intercept) / Math.Sqrt(slope * slope + 1);

            return distance;
        }

        List<double> DipValueCalculation(double[] grayData, double sigma)
        {
            // 平滑数据 (高斯滤波)
            double[] smoothData = ApplyGaussianFilter(grayData.Select(x => (double)x).ToArray(), sigma);

            // 找到峰值和谷值
            var peaks = FindPeaks(smoothData);
            var valleys = FindValleys(smoothData);

            // 计算峰值显著性
            double[] prominences = CalculateProminences(smoothData, peaks);

            double score = Percentile(prominences.ToArray(), 90);
            double prominenceThreshold = 0.15 * (grayData.Max() - grayData.Min());

            // 判断数据类型
            if (score < prominenceThreshold)
            {
                return new List<double>();
            }

            // 找到显著性小于 1000 的峰值
            var lowProminencePeaks = peaks.Where((_, i) => prominences[i] < 1000).ToArray();
            double[] lowProminenceValues = lowProminencePeaks.Select(p => smoothData[p]).ToArray();
            double threshold = 0.5 * (smoothData.Max() + smoothData.Min());

            // 计算其中小于阈值的数量
            int countBelowThreshold = lowProminenceValues.Count(v => v < threshold);
            double ratio = lowProminenceValues.Length > 0 ? (double)countBelowThreshold / lowProminenceValues.Length : 0;

            if (ratio > 0.8)
            {
                grayData = grayData; // 不改变数据
            }
            else
            {
                grayData = grayData.Select(x => -x).ToArray(); // 反转数据
            }

            // 平滑数据 (高斯滤波)
            smoothData = ApplyGaussianFilter(grayData, sigma);

            // 找到峰值和谷值
            peaks = FindPeaks(smoothData);
            valleys = FindValleys(smoothData);
            var valleyList = valleys.ToList();

            // 计算显著性 (假设 CalculateProminences 已实现)
            prominences = CalculateProminences(smoothData, peaks);

            int ratioHeight = 0;
            // 根据峰值数量设定阈值
            if (peaks.Count <= 15)
            {
                ratioHeight = 0;
                score = GetPercentile(prominences, 0);
            }
            else if (peaks.Count > 15 && peaks.Count < 30)
            {
                ratioHeight = 25;
                score = GetPercentile(prominences, 25);
            }
            else if (peaks.Count >= 30 && peaks.Count < 85)
            {
                ratioHeight = 50;
                score = GetPercentile(prominences, 50);
            }
            else
            {
                ratioHeight = 75;
                score = GetPercentile(prominences, 50);
            }

            double heightThreshold = GetPercentile(peaks.Select(p => smoothData[p]).ToArray(), ratioHeight);
            peaks = FindPeaks(smoothData, prominenceThreshold: score, heightThreshold);

            // 计算最小高度差
            var minHeightDiffs = new List<double>();
            for (int i = 0; i < peaks.Count; i++)
            {
                double leftDiff = double.PositiveInfinity;
                double rightDiff = double.PositiveInfinity;

                if (i > 0)
                {
                    double leftPeakValue = smoothData[peaks[i - 1]];
                    double currentPeakValue = smoothData[peaks[i]];
                    leftDiff = Math.Abs(currentPeakValue - leftPeakValue);
                }

                if (i < peaks.Count - 1)
                {
                    double rightPeakValue = smoothData[peaks[i + 1]];
                    double currentPeakValue = smoothData[peaks[i]];
                    rightDiff = Math.Abs(currentPeakValue - rightPeakValue);
                }

                minHeightDiffs.Add(Math.Min(leftDiff, rightDiff));
            }

            // 找到重复的最小高度差
            var uniqueDiffs = minHeightDiffs.Distinct().ToArray();
            var duplicateDiffs = uniqueDiffs.Where(diff => minHeightDiffs.Count(d => d == diff) > 1);

            // 找到具有重复最小高度差的峰值
            var duplicatePeaks = new List<int>();
            foreach (var diff in duplicateDiffs)
            {
                var indices = minHeightDiffs.Select((v, index) => new { v, index })
                                            .Where(x => x.v == diff)
                                            .Select(x => x.index)
                                            .ToArray();
                duplicatePeaks.AddRange(indices.Select(index => peaks[index]));
            }

            List<int> selectedPeaks = duplicatePeaks.Distinct().OrderBy(x => x).ToList();

            // 计算相邻峰值高度差
            var diffValues = new List<double>();
            for (int i = 0; i < selectedPeaks.Count - 2; i += 2)
            {
                double peakValue1 = smoothData[selectedPeaks[i]];
                double peakValue2 = smoothData[selectedPeaks[i + 2]];
                double diffValue = peakValue2 - peakValue1;
                diffValues.Add(diffValue);
            }

            // 统计正值和负值的数量
            int positiveCount = diffValues.Count(diff => diff > 0);
            int negativeCount = diffValues.Count(diff => diff < 0);

            // 判断主要符号
            int majoritySign = positiveCount > negativeCount ? 1 : -1;

            // 找出符号与主要符号不一致的索引
            var filteredIndices = new List<int>();
            for (int i = 0; i < diffValues.Count; i++)
            {
                if (diffValues[i] * majoritySign < 0) // 符号不一致
                {
                    filteredIndices.Add(i);
                }
            }

            // 删除不一致的峰值点
            for (int idx = filteredIndices.Count - 1; idx >= 0; idx--) // 倒序删除以避免索引偏移
            {
                int startIndex = filteredIndices[idx] * 2; // 每组包含 2 个点
                int endIndex = Math.Min(startIndex + 2, selectedPeaks.Count);

                selectedPeaks.RemoveRange(startIndex, endIndex - startIndex);
            }

            var peakValleyDiffs = new List<(int Peak, int ClosestLeftValley, int ClosestRightValley, double LeftDiff, double RightDiff)>();

            foreach (var peak in selectedPeaks)
            {
                // 找到左侧最近的谷底
                int closestLeftValley = -1;
                double leftDiff = 0;
                var leftValleyCandidates = valleys.Where(v => v < peak).ToArray();

                if (leftValleyCandidates.Length > 0)
                {
                    closestLeftValley = leftValleyCandidates.Last(); // 最接近峰的左侧谷底
                    leftDiff = smoothData[peak] - smoothData[closestLeftValley];
                }
                else
                {
                    // 如果没有找到左侧谷底，则选取峰值左边的最低点
                    closestLeftValley = Array.IndexOf(smoothData, smoothData.Take(peak).Min());
                    leftDiff = smoothData[peak] - smoothData[closestLeftValley];
                    valleyList.Add(closestLeftValley);
                    valleyList = valleyList.Distinct().OrderBy(v => v).ToList();
                }

                // 找到右侧最近的谷底
                int closestRightValley = -1;
                double rightDiff = 0;
                var rightValleyCandidates = valleys.Where(v => v > peak).ToArray();

                if (rightValleyCandidates.Length > 0)
                {
                    closestRightValley = rightValleyCandidates.First(); // 最接近峰的右侧谷底
                    rightDiff = smoothData[peak] - smoothData[closestRightValley];
                }
                else
                {

                    // Slice the array from the 'peak' index to the end
                    double[] slicedData = smoothData.Skip(peak).ToArray();

                    // 如果没有找到右侧谷底，则选取峰值右边的最低点
                    closestRightValley = Array.IndexOf(slicedData, slicedData.Min()) + peak;
                    rightDiff = smoothData[peak] - smoothData[closestRightValley];
                    valleyList.Add(closestRightValley);
                    valleyList = valleyList.Distinct().OrderBy(v => v).ToList();
                }

                peakValleyDiffs.Add((peak, closestLeftValley, closestRightValley, leftDiff, rightDiff));
            }

            // 过滤后的 peakValleyDiffs 列表
            var filteredPeakValleyDiffs = new List<(int Peak, int ClosestLeftValley, int ClosestRightValley, double LeftDiff, double RightDiff)>();

            for (int i = 0; i < peakValleyDiffs.Count - 1; i += 2)
            {
                var (peak1, leftValley1, rightValley1, _, _) = peakValleyDiffs[i];
                var (peak2, leftValley2, rightValley2, _, _) = peakValleyDiffs[i + 1];

                // 检查条件
                if (rightValley1 == leftValley2)
                {
                    if (smoothData[leftValley2] >= smoothData[leftValley1] || smoothData[leftValley2] >= smoothData[rightValley2])
                    {
                        filteredPeakValleyDiffs.Add(peakValleyDiffs[i]);
                        filteredPeakValleyDiffs.Add(peakValleyDiffs[i + 1]);
                    }
                }
            }

            var dips = new List<double>();

            // 遍历相邻的谷点对
            for (int i = 0; i < filteredPeakValleyDiffs.Count - 1; i += 2)
            {
                var (peak1, leftValleyCurrent1, leftValleyCurrent2, _, _) = filteredPeakValleyDiffs[i];
                var (peak2, _, rightValleyNext, _, _) = filteredPeakValleyDiffs[i + 1];

                if (leftValleyCurrent1 >= 0 && rightValleyNext >= 0)
                {
                    int x1 = leftValleyCurrent1, x2 = rightValleyNext;
                    double y1 = smoothData[leftValleyCurrent1], y2 = smoothData[rightValleyNext];

                    // 计算斜率和截距
                    double m = (y2 - y1) / (x2 - x1);
                    double b = y1 - m * x1;

                    // 计算 dip 值
                    double A = Math.Abs(smoothData[peak1] - (m * peak1 + b));
                    double B = Math.Abs(smoothData[peak2] - (m * peak2 + b));
                    double C = Math.Abs(smoothData[leftValleyCurrent2] - (m * leftValleyCurrent2 + b));
                    double dip = 100 * (A + B - 2 * C) / (A + B);

                    if (dip > 0)
                    {
                        dips.Add(dip);
                    }
                }
            }

            return dips;
        }

        public List<double> CalculateDip(double[] data)
        {
            double? bestSigma = null;
            List<double> bestDip = new List<double>();
            int maxDipLength = 0;

            // Sigma 值集合
            double[] sigmaValues = new double[] { 1.5, 1, 0.5, 0 };
            // double[] sigmaValues = new double[] { 0, 0.5, 1, 1.5 };

            foreach (var sigma in sigmaValues)
            {
                // 调用 dip_value_calculation 自定义函数，这里假设返回的结构是元组，包含多个值
                var dips = DipValueCalculation(data, sigma);

                // 检查 dip 值的差值是否超过 30，如果超过，则跳过此 sigma 和 dips
                if (dips.Count > 1 && Math.Abs(dips[0] - dips[1]) > 30)
                {
                    // 跳过 sigma 和 dips
                    continue;
                }

                // 更新最佳 sigma 和 dip
                if (dips.Count > maxDipLength)
                {
                    bestSigma = sigma;
                    bestDip = new List<double>(dips);
                    maxDipLength = dips.Count;
                }
                else if (dips.Count == maxDipLength)
                {
                    if (dips.Count > 0 && bestDip.Count > 0 && dips[dips.Count - 1] > bestDip[bestDip.Count - 1]) {
                        bestSigma = sigma;
                        bestDip = new List<double>(dips);
                        maxDipLength = dips.Count;
                    }
                }
            }

            // 如果没有找到最佳 dip，直接返回空的 bestDip
            if (bestDip.Count == 0)
            {
                return bestDip;
            }

            // 在找到最佳 dip 后，调用 dip_value_calculation
            var finalDips = DipValueCalculation(data, bestSigma.Value);

            // 转换 dip 为浮动类型
            List<double> convertedDip = new List<double>();
            foreach (var val in bestDip)
            {
                convertedDip.Add((double)val);  // 确保转换为浮动类型
            }

            return convertedDip;
        }

        static List<int> ExtractFilteredPeaks(List<(int Peak, int ClosestLeftValley, int ClosestRightValley, double LeftDiff, double RightDiff)> filteredPeakValleyDiffs)
        {
            return filteredPeakValleyDiffs.Select(item => item.Peak).ToList();
        }

        public static double[] ApplyGaussianFilter(double[] data, double sigma)
        {
            if (sigma <= 0)
                return Array.ConvertAll(data, d => (double)d); // 若 sigma 为 0 或负数，不进行滤波。

            // 生成高斯核
            int radius = (int)Math.Ceiling(3 * sigma);
            double[] kernel = GenerateGaussianKernel(sigma, radius);

            // 应用卷积
            return Convolve(data, kernel);
        }

        /// <summary>
        /// 生成高斯核。
        /// </summary>
        /// <param name="sigma">标准差。</param>
        /// <param name="radius">核半径。</param>
        /// <returns>高斯核数组。</returns>
        private static double[] GenerateGaussianKernel(double sigma, int radius)
        {
            int size = 2 * radius + 1;
            double[] kernel = new double[size];
            double sum = 0;
            double sigma2 = 2 * sigma * sigma;

            for (int i = -radius; i <= radius; i++)
            {
                double value = Math.Exp(-i * i / sigma2);
                kernel[i + radius] = value;
                sum += value;
            }

            // 归一化
            for (int i = 0; i < size; i++)
            {
                kernel[i] /= sum;
            }

            return kernel;
        }

        /// <summary>
        /// 对输入数组应用卷积。
        /// </summary>
        /// <param name="data">输入数组。</param>
        /// <param name="kernel">卷积核。</param>
        /// <returns>卷积后的数组。</returns>
        private static double[] Convolve(double[] data, double[] kernel)
        {
            int radius = kernel.Length / 2;
            double[] result = new double[data.Length];

            for (int i = 0; i < data.Length; i++)
            {
                double sum = 0;

                for (int j = -radius; j <= radius; j++)
                {
                    int idx = i + j;

                    if (idx >= 0 && idx < data.Length) // 边界检查
                    {
                        sum += data[idx] * kernel[j + radius];
                    }
                }

                result[i] = sum;
            }

            return result;
        }

        public static List<int> FindPeaks(double[] data)
        {
            var peaks = new List<int>();

            for (int i = 1; i < data.Length - 1; i++)
            {
                if (data[i] > data[i - 1] && data[i] > data[i + 1])
                {
                    peaks.Add(i);
                }
            }

            return peaks;
        }

        /// <summary>
        /// 在数组中查找局部最小值（谷值）。
        /// </summary>
        public static List<int> FindValleys(double[] data)
        {
            var valleys = new List<int>();

            for (int i = 1; i < data.Length - 1; i++)
            {
                if (data[i] < data[i - 1] && data[i] < data[i + 1])
                {
                    valleys.Add(i);
                }
            }

            return valleys;
        }

        public static List<int> FindPeaks(double[] data, double prominenceThreshold, double heightThreshold)
        {
            List<int> peaks = new List<int>();
            List<double> prominences = new List<double>();

            int n = data.Length;
            for (int i = 1; i < n - 1; i++)
            {
                // 查找局部最大值 (峰值)
                if (data[i] > data[i - 1] && data[i] > data[i + 1])
                {
                    // 计算显著性（Prominence） - 峰值与左右谷值的高度差
                    double leftValley = data[i];
                    double rightValley = data[i];

                    // 寻找左边的谷值
                    int left = i - 1;
                    while (left >= 0 && data[left] < data[left + 1])
                    {
                        leftValley = data[left];
                        left--;
                    }

                    // 寻找右边的谷值
                    int right = i + 1;
                    while (right < n && data[right] < data[right - 1])
                    {
                        rightValley = data[right];
                        right++;
                    }

                    // 计算显著性
                    double prominence = data[i] - Math.Min(leftValley, rightValley);

                    // 如果显著性超过阈值且峰值高度大于阈值，则加入结果
                    if (prominence >= prominenceThreshold && data[i] >= heightThreshold)
                    {
                        peaks.Add(i);
                        prominences.Add(prominence);
                    }
                }
            }

            return peaks;
        }

        public double[] CalculateProminences(double[] data, List<int> peaks)
        {
            int n = data.Length;
            double[] prominences = new double[peaks.Count];
            int[] leftBases = new int[peaks.Count];
            int[] rightBases = new int[peaks.Count];

            for (int i = 0; i < peaks.Count; i++)
            {
                int peak = peaks[i];

                // 从峰值向左寻找谷底
                int left = peak;
                while (left > 0 && data[left - 1] <= data[left])
                {
                    left--;
                }
                leftBases[i] = left;

                // 从峰值向右寻找谷底
                int right = peak;
                while (right < n - 1 && data[right + 1] <= data[right])
                {
                    right++;
                }
                rightBases[i] = right;

                // 谷底的高度
                double leftBaseHeight = data[leftBases[i]];
                double rightBaseHeight = data[rightBases[i]];
                double baseHeight = Math.Min(leftBaseHeight, rightBaseHeight);

                // 计算显著性
                prominences[i] = data[peak] - baseHeight;
            }

            return prominences;
        }

        static double GetPercentile(double[] data, double percentile)
        {
            var sorted = data.OrderBy(x => x).ToArray();
            int index = (int)Math.Floor((percentile / 100) * sorted.Length);
            if (index >= sorted.Length) {
                return 0;
            }
            return sorted[index];
        }

        static double Percentile(double[] data, double percentile)
        {
            if (data != null && data.Length > 0 && percentile > 0 && percentile < 100)
            {
                // 排序数组
                var sortedData = data.OrderBy(x => x).ToArray();

                // 计算索引位置
                double rank = (percentile / 100) * (sortedData.Length - 1);
                int lowerIndex = (int)Math.Floor(rank);
                int upperIndex = (int)Math.Ceiling(rank);

                // 如果是整数索引，直接返回
                if (lowerIndex == upperIndex)
                {
                    return sortedData[lowerIndex];
                }

                // 线性插值
                double weight = rank - lowerIndex;
                return sortedData[lowerIndex] * (1 - weight) + sortedData[upperIndex] * weight;
            }
            else {
                return 0;
            }
                
        }

        public int ArgMin(double[] data)
        {
            if (data == null || data.Length == 0)
            {
                throw new ArgumentException("Input array cannot be null or empty.");
            }

            int minIndex = 0;
            double minValue = data[0];

            for (int i = 1; i < data.Length; i++)
            {
                if (data[i] < minValue)
                {
                    minValue = data[i];
                    minIndex = i;
                }
            }

            return minIndex;
        }

    }
}
