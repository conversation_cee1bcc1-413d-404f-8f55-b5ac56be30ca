﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LkControls.Utils
{
    internal class MatProcesser
    {

        public static int rotationState = 0;
        public static bool flipHorizontal = false; // 水平翻转状态
        public static bool flipVertical = false;   // 垂直翻转状态
        static public void Rotate90Clockwise(Mat mat)
        {
            rotationState = (rotationState + 1) % 4;
            Cv2.Rotate(mat, mat, RotateFlags.Rotate90Clockwise);

            Debug.WriteLine("Rotate90Clockwise:" + mat.Width);
        }

        static public void Rotate90Counterclockwise(Mat mat)
        {
            rotationState = (rotationState - 1 + 4) % 4;
            Cv2.Rotate(mat, mat, RotateFlags.Rotate90Counterclockwise);
        }

        static public void FlipVertical(Mat mat)
        {
            flipVertical = !flipVertical;
            Cv2.Flip(mat, mat, FlipMode.X);
        }

        static public void FlipHorizontal(Mat mat)
        {
            flipHorizontal = !flipHorizontal;
            Cv2.Flip(mat, mat, FlipMode.Y);
        }

        static public void Negative(Mat mat)
        {
            Cv2.BitwiseNot(mat, mat);
        }

        static public void RotateImage(Mat mat)
        {
            // 根据 rotationState 选择正确的 RotateFlags
            switch (rotationState)
            {
                case 1:
                    Cv2.Rotate(mat, mat, RotateFlags.Rotate90Clockwise);
                    break;
                case 2:
                    Cv2.Rotate(mat, mat, RotateFlags.Rotate180);
                    break;
                case 3:
                    Cv2.Rotate(mat, mat, RotateFlags.Rotate90Counterclockwise);
                    break;
                    // case 0: 默认不旋转
            }

            // **2. 根据翻转状态进行翻转**
            if (flipHorizontal)
                Cv2.Flip(mat, mat, FlipMode.Y);
            
            if (flipVertical)
                Cv2.Flip(mat, mat, FlipMode.X);
        }

        /// <summary>
        /// 多张16位Mat图像方差加权融合（积分图 + Parallel.For），返回16位Mat
        /// </summary>
        public static Mat VarianceWeightedFusion(List<Mat> images, int windowSize = 5)
        {
            if (images == null || images.Count == 0)
                throw new ArgumentException("输入图像列表不能为空");

            int height = images[0].Rows;
            int width = images[0].Cols;
            int imgCount = images.Count;

            Mat result = new Mat();
            double alpha = 0.6;

            for (int i = 0; i < imgCount - 1; i++) { 
                Mat lowEnergy = images[i];
                Mat highEnergy = images[i + 1];
                Cv2.AddWeighted(lowEnergy, alpha, highEnergy, 1 - alpha, 0, result, -1);
            }

            return result;
        }

    }
}
