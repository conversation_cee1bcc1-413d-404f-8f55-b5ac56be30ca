﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LkControls.Utils
{
    internal class MatProcesser
    {

        public static int rotationState = 0;
        public static bool flipHorizontal = false; // 水平翻转状态
        public static bool flipVertical = false;   // 垂直翻转状态
        static public void Rotate90Clockwise(Mat mat)
        {
            rotationState = (rotationState + 1) % 4;
            Cv2.Rotate(mat, mat, RotateFlags.Rotate90Clockwise);

            Debug.WriteLine("Rotate90Clockwise:" + mat.Width);
        }

        static public void Rotate90Counterclockwise(Mat mat)
        {
            rotationState = (rotationState - 1 + 4) % 4;
            Cv2.Rotate(mat, mat, RotateFlags.Rotate90Counterclockwise);
        }

        static public void FlipVertical(Mat mat)
        {
            flipVertical = !flipVertical;
            Cv2.Flip(mat, mat, FlipMode.X);
        }

        static public void FlipHorizontal(Mat mat)
        {
            flipHorizontal = !flipHorizontal;
            Cv2.Flip(mat, mat, FlipMode.Y);
        }

        static public void Negative(Mat mat)
        {
            Cv2.BitwiseNot(mat, mat);
        }

        static public void RotateImage(Mat mat)
        {
            // 根据 rotationState 选择正确的 RotateFlags
            switch (rotationState)
            {
                case 1:
                    Cv2.Rotate(mat, mat, RotateFlags.Rotate90Clockwise);
                    break;
                case 2:
                    Cv2.Rotate(mat, mat, RotateFlags.Rotate180);
                    break;
                case 3:
                    Cv2.Rotate(mat, mat, RotateFlags.Rotate90Counterclockwise);
                    break;
                    // case 0: 默认不旋转
            }

            // **2. 根据翻转状态进行翻转**
            if (flipHorizontal)
                Cv2.Flip(mat, mat, FlipMode.Y);
            
            if (flipVertical)
                Cv2.Flip(mat, mat, FlipMode.X);
        }

        /// <summary>
        /// 定义积分图结构体
        /// </summary>
        public class IntegralImage
        {
            public Mat Sum;    // CV_64F
            public Mat SumSq;  // CV_64F
        }

        /// <summary>
        /// 计算所有图像的积分图
        /// </summary>
        public static List<IntegralImage> ComputeIntegralImages(List<Mat> images)
        {
            var integralImages = new List<IntegralImage>();
            foreach (var img in images)
            {
                Mat sum = new Mat();
                Mat sumSq = new Mat();
                Cv2.Integral(img, sum, sumSq, MatType.CV_64F);
                integralImages.Add(new IntegralImage { Sum = sum, SumSq = sumSq });
            }
            return integralImages;
        }

        /// <summary>
        /// 使用积分图计算局部方差
        /// </summary>
        public static double LocalVarianceFromIntegral(IntegralImage ii, int cx, int cy, int windowSize, int width, int height)
        {
            int half = windowSize / 2;
            int x1 = Math.Max(cx - half, 0);
            int y1 = Math.Max(cy - half, 0);
            int x2 = Math.Min(cx + half, width - 1);
            int y2 = Math.Min(cy + half, height - 1);

            // 积分图多1行1列
            x1++; y1++;
            x2++; y2++;

            double sum = ii.Sum.At<double>(y2, x2) - ii.Sum.At<double>(y1 - 1, x2) - ii.Sum.At<double>(y2, x1 - 1) + ii.Sum.At<double>(y1 - 1, x1 - 1);
            double sumSq = ii.SumSq.At<double>(y2, x2) - ii.SumSq.At<double>(y1 - 1, x2) - ii.SumSq.At<double>(y2, x1 - 1) + ii.SumSq.At<double>(y1 - 1, x1 - 1);

            int count = (x2 - x1 + 1) * (y2 - y1 + 1);
            double mean = sum / count;
            double variance = (sumSq / count) - (mean * mean);
            return variance;
        }

        /// <summary>
        /// 多张16位Mat图像方差加权融合（积分图 + Parallel.For），返回16位Mat
        /// </summary>
        public static Mat VarianceWeightedFusion(List<Mat> images, int windowSize = 5)
        {
            if (images == null || images.Count == 0)
                throw new ArgumentException("输入图像列表不能为空");

            int height = images[0].Rows;
            int width = images[0].Cols;
            int imgCount = images.Count;

            // 检查尺寸一致
            foreach (var img in images)
            {
                if (img.Rows != height || img.Cols != width)
                    throw new ArgumentException("所有输入图像尺寸必须一致");
            }

            // 计算所有图像积分图
            var integralImages = ComputeIntegralImages(images);

            Mat fusedFloat = new Mat(height, width, MatType.CV_32FC1, Scalar.All(0));
            double epsilon = 1e-6;

            // 并行处理每一行
            Parallel.For(0, height, y =>
            {
                for (int x = 0; x < width; x++)
                {
                    double[] variances = new double[imgCount];
                    double totalVar = 0;

                    // 计算每张图像当前像素的局部方差
                    for (int i = 0; i < imgCount; i++)
                    {
                        variances[i] = LocalVarianceFromIntegral(integralImages[i], x, y, windowSize, width, height) + epsilon;
                        totalVar += variances[i];
                    }

                    // 加权融合
                    double fusedPixel = 0;
                    for (int i = 0; i < imgCount; i++)
                    {
                        double weight = variances[i] / totalVar;
                        fusedPixel += weight * images[i].At<ushort>(y, x);
                    }

                    fusedFloat.Set<float>(y, x, (float)fusedPixel);
                }
            });

            // 归一化并转换为16位
            Mat fused16 = new Mat();
            Cv2.Normalize(fusedFloat, fused16, 0, 65535, NormTypes.MinMax);
            fused16.ConvertTo(fused16, MatType.CV_16UC1);

            return fused16;
        }

    }
}
