<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="vintasoftLogoPictureBox.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAHAAAABiCAYAAACI/lfbAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAPI9JREFUeF7lm3V4
        VNfa9t9/v+t6j9QgxAWCO8Xd3TVukIQQIRAgJLg7tIUChSI1SoW60QLF3UM8ARIgeCkkWGbu737WzJ7s
        meyEtOec9zvn/f74XXtmy9prPfd6bAL/hcPd8I/RtXKO8J4j3Y2pyvOVcaQSjO7XY/SMHTLvCihng/83
        lBwagf8yH+qK/0gogpnGrBAu0PC5/0UUHxyO/8KRHtxV5GjPitHuMcTAs/QcrQw+/49w7AUYPWPDaD4a
        RvdXEUMbaRjYVkOuGT5TMSWHR1LAw/JFG6AC5J6KUIMZPKPoATMNUhGWRfO+o73+rTBzTmbO3xDO2Wgt
        GsZ2qCJG9q2EkkMioMECHDFciA3jZ6rEsd7/GMcrweh+PUbz0TC634bB/f8kjO1rAQZ2LjkyWkIoJ0XM
        f5pKXiwvkkUbGZiYaYz/VxjNR8Po/ipjaKOqYTrcq0I0nfSUHB4jHtiHavYpN5h2vmrI/TqO8ZwVMwUs
        g+eO/+9E1qbWZ2ifP4ejJo4UVySgfpA/zLG+dtgWdpzf/5040a9ijO6vAv9sAQW9Lo7YCVgZmgh6jBZQ
        ZWzGks8c6z8K6/yN1kWMbKVht7kN7PxHMBZQvUSP5WXq5QaTrRQ7kRw4KfSDmUfzSU7mPwht7obrEoxs
        URX04jrqoNdIR8kRP6uAf1QgmzgWzJWhEwmn+KwOcxXAab7jn43Be6qK4xo0ygTuXzG0RzmM7GuEncAW
        LAKqyqsCAY1e6IChaMQyaR4NFmvDyLj/CRitRcNRND0GdtJjs6uRFkoj0aqMkmMSQisSUD/gC7CbiH7C
        p/pz1/KcA4ZGscHnhDNVwPB5K0b369HeY4jBeHZY7jNcm379BtjZyoFytrXTRDSyF9EqoLH3Gb1AMJqU
        HRQNpwZYOC3we0VYjWnWgTN8RjirwXMVYX3eEKP7bVjfUQn6Oemxu89ofdraFfLdipGtrBjZWY9FUOqi
        cqwmJHPgMQmhthvKUA85vED/vRz6SatFWVGGsGBZ9EB7zhpwbpAV+SzwuX86+vdUgNHcjLCuzbZO/fr1
        aIIa2Y8osYzOaXpo6Jys5Ji/g4AOA2jIwGa+3BGbtzlOVhPLaMEaRkazQ+4ROFYFmM//Wary/kowWo+G
        tjkdbWLFyI4KnY3tbE9dDKGAxXoB5aT+QfWw/gXa4AaTslGZcAaGMJ+vGNjgsxVgLE5VkGeN31sV7NZh
        tFaBdtC80uadgoHdjOwsVCogUQLKB0fhFPqB9C/UJqLtND3a5Ok5ZgUXrKE3wPnBFi5YsJ23frcg3wWO
        VwHmP4llXP27yqOfrx51nXO3ndOvkdiJaITNXjpbViDki3KnxQO1E/o85hgW9eLYkMkSmbQjslBtsVXA
        rH2+OESHfBc4TkWk/Vkc32WAbn52vOi6kT2IElfQ7FbOnkRvcz16XXQClhwPoAdqSuseMMvRtlN0LxA0
        z7LuQItYnLjOowQlisNC5Zy9WHIUOIYShNetKCMrQ+s+O2C+9A8gz1eA0btsqPkOsa3Fbk0On23nrLay
        wyqqRKoy+zoIK7bX6aKwiUkPPKEELLtoF6/1A2m7x4o2CceJaqhFaDu1AsxpAu9VhuF45YzM60LaUB4t
        wJF0jvUnKf++MqAh3urw3TJnjmGwJm3tjqLqsRNRs6ODfe1sb3Uimy46MZWAOM0bBM3bBPWwDKQbVPMy
        B08zxGhxgoihQQFsItE4inQNnkvndYL0YWVk6OG1DI75Z3EQ1BG9oOWxrEHWY8NhrYaeaMXxvNhUiakX
        UXDwSLMS0oIIWHIykAKe4U2OqAc56DkLFYlmm4zj5AXlYVaxNJRohGIIUAIJfE5QopBMgdeFjOEWMh2R
        69q9fwLtXRWhn5cOy+ayzN+2HqugZfBeB5sou1htZiiiYHUQze7K9pojldNpIAUMchCQommUiceXG2C+
        wKM2OU5aw14sEcketXB6kGDnUUqQ4TCLOFllQsl3DXUua4QO671/Bm28ilAbx2GORJu7wmFtan1cs6Og
        Fg8VLPYS22lA0NlUndO+2wlpRadXyalgq4A64QRD4S5wAnrUhEiaNmkLSOfCrZjV0bo4ZQB+NzKWYCfM
        C8geacHo2j8Tx3k5zlmLDoImoKxbZw8btJPYymY3PcqmtDE/24mrE9FRI52A5S++UED9y/+IgI4G0FBe
        IUbiMbvMYGZ+FuScWYlm+WxDM+y/DG1eVozmbhPRsk4loBx1NrHZ5l8i4FneeE7gSSUcH1AxWUTT0F5i
        eaElLHAyMilBJ5rdrjRasGAziojCBWdzgSJSDseWI8Uy8WjKGQ5TrpwfidLcUTAL/GzOkXN8vlI4dqUY
        PUO0zaFHL6Iex3VxzWbr2h1DrRJQj11oLUMvoAWLHkpEPRSx+DQFtCls8zSi5TZtYO1lAl9u21Gah8nu
        U+gWIxgtWhCjiBHFq2g0i3gklxPOHYrnecMpGEUUwfJGAaQ0ZxjP+fH6aJ4XeJ7iVgzHVvcYIdeMnrHO
        g3PTU058LRo4rku/dj2affQbXRNSUOHV4hjKOXSCWrDqomlk9cziMyEiIL+c500OXqZQAzvsHv0kMjhp
        I0/TL0ot1oreCDSkWQxGY5pzxihBntEjiy8Mx/1Do3BvTwB+3++PxydH4XnaKJjyhqA0fzQF5f1yJMjn
        OBVCodQ9RvAax7GHc1VwfOumUdgJb8EmrCZkRV7qaBflnTyvtyHRHMLmGFa729D0EG10IioBcZ43XNDd
        pGF9WA3o8EKLcFb0E3RcgBKNi9bQG4LGMdGQpZcJPerJeT8UfRuItKV9cWZCV5wO7YILkZ2Qk9wZhevY
        tJ6hyHzGfJmeeYXPXqEQl1/EmArgNb2Y5dBdr0RMOy/Vb1RHOwhKQB4dcbQtMRRS00VzMgpYcia0AgGt
        wtkGkkGt2F6cqcNxsmpXinjEYdEWY1iMI15Umj8GJeljULRrGI4G98L3TeriW8/q+MmlGva4OmFfTTcc
        6dIEl1cMpjeGoDQvmOL5wUQhzFf+PNDjILzZip2QOow81CakTUwHmwh6m1ntaCn0DLhE7ATkd4V8JtTM
        IiBDljqRJg+UYZYB0vkSQRNNQ4nGSSrksxVO3G5XKsG4aD16Q9BIzxk+7+4bgbNJnfGjlyd+cquOHz1e
        wy8eztjv6YpDXjVwrKYLznZujLtb/fH0YhBMVwMogj/MV5kT/yTQYSSwhqEH69ZgW5e2QXViKuy8UrMZ
        cRDSAs9rNidKA5smPCfYhByO4rNhFFA7od0gXNKQAYn+JXbikWxOulyYlAVZ0QundniZIcyX/fEkzQ+F
        2wbg504N8aPTa9jj5oI9Hk7Y7+WMo97uOE0uebrjfG0v3EjqjZKj9NqCAJiIkTBVBQX+FE+wfpcjPfv5
        ZT+UZPujONMfjzL9LGRY4efibN6Tz3sZOSxrkI3INSuP5DmdkAqxiSaiQr4Tm5AaYl+eF3uL3RUUkXoI
        6rteI2pWci6cAlpPaDfaPZAuAxLZGdqLNNE0ciiKXjAlGheiYd2paqEOu7v0KsPnhVG4vK4XfmhYC79I
        2PRywUEfilezOk7UdMKZms64xM8XazmjcHwnlBxkziwMhKmQHlhAQxIlBr/bIecqwayeD+RnhuMCmY8/
        nuUFoeC0P374cDg+3zwEn5HPNw/F5+9a+HLbcOz+bDSunw3Ecwppsq5BcrJZCqc8jsn1C3p7mCmiIxav
        dEAEJGaKqCE6OGqj9NEENLyoxCsb0CachhJOsJ+owk48bYc6iGfd9cLT9NG4snUAfmrbHHvcXSkgQ6Y3
        Q6anGy56eSDN1x1pdWogs447iqb0Un/EfFbIMEoPxLU/j1kQoa/5ofQa2xKO9zArArt3+mNgn5Zo0bQB
        Xm/WEC2bN0LrFo0VHTnHoBFdcWhXEB5niYAWEdWalFdyPPFMvQ0Eq200cZWAGsoRxK48ZvG+TIGfdUKK
        FmYHIcXpLB4oYjmivI7IQDKgDG7ndcQ6qXKTFbgYMBSZBesCtVClwpXmCVe5k3NCcPuHAJwI74d93hI2
        nXDcxxVnfRg6a3ogw9cNuXVckN2xGe5uDmDBE4TS6xYBcJ0epOeGDsdrdsizFvFwLRClNygEv9/PiMCn
        m0aiSX1XvPrSX/Day39BtVf+Sv6mjs7VX0X7liyy3h+OR7SJmfNXuVit0Z+FFefFNcvaFWIHwdE+mu0E
        cQKbmLyWJfCcEpPnrGJq2uhFLDkfoRNQE00JZx1AkEHVS4j+xfoJya5TWCauFsEFCXpvU6LpwpyZ300M
        Y4/TwlCwIwBnhrbB+SZeOFvXGRd9nZBVuwZyG7ijsEMDXJ/bH0/OhjN0Mv8V0eBFNB6F0sCNIKBIh+5a
        eSggRVNC0ptL+d10Ywzu5wTjs60j0ayRJ6q/9hKcqr0Cp+qvoAaFq8Gjm4sTOrStj+92iID0JAnBFFFy
        qZmYJKda162oTEi9LTXE1npsQhKrJ9r0IsU2AeVCReIJevH0k7BOTHmaTjSFKhBoIM3baHiFzXNoNO56
        E/OZuTAEzy4H4+6ecOTN6MPN2B65fVshb1A7XI3sizsbeW9hhHrOXETDXQsioUoMO9GqDMe5aRWdHmgu
        Cib0wJwgfLp1GJo39qB4L6FGtZcVzkrElylgdXRs0xg/fBTAYkaelTDMuRQQqYyvWdestwOx2UfZS2c/
        Dc22ObwuGImo6aMTseTCWHsBzaTk/BhcP+yH7F9GIPPn4cgSfiF7yN4RZCSy945C3iE/3D3HEJgrIUQj
        gFVcAB6kB6LgBMdgwZF1kPer42hkH7JyeAxyjrD6POOP37MZwmiEkisMpbkUMSMaN36Kx9WPY1H4RTzu
        HEnA/exxuH95HEMn+8DbvP9WCEw3Q+1EMd8MxtPCMN4bghsXA1BwdjSunh6hKOTnoov+eECBnnKzmG5y
        nKIQisZn6YlmEfJ6MH7LjqAHjkKzxvRACuhE0YQaTq/AuQY90M2JAjbCV9vG4NaFQPzO8P8wLxyPL4ez
        MuYcJLJIbhaUkGWYaJ8Sin73QgCunxiJwiPDUHBoKAoOD1Wfb54chYe89jyLG4EFkpniCfYC2gtpEZBF
        hJnh08xK81naaOT+4o+3FwzHWP+OCBnVEmGjWiF0dBuEkdAx7RA2pgMiyKTontj17ig8YBsgu096s9LL
        gbibFoQ9n/hhztTeCA/kM36tEe7XFuFj2vPYHhH+7TA2oB1iwjpj1cLBOPFzII0QjAu/hmPbumBsWeuH
        bWtD+Xkstr4dji3rg7H9nUB8/XEkHuaHU6hQmG7TY24FUziKIMLdCMZ1vvfgN0HYsGoYUiZ1R9y41ogK
        bYzIsEaIj2qHmUk9sH3tMBzbHYjr6SF4ci1MeZ3pJucvG+BGKO7lROJTitOCYVw8z8XpVZX3XNjeuBJ3
        emCLRrUwe+pQfLAhCDu3BWHXh+EcMxL3MsIsqYFh1cxIYy7g/K6y2LoShOKsIFw+Oga/fBaIdcuGIiW+
        E+LDWiImsAViglpg0rg2WDC1F3ZuGIFz34/G3VN+eMY2xpxDKKaqQdIpZgbHz5SjRcDiiyJgBk8wbJqp
        7rNL/vQ6P8yI74H6dbxV/K+Ipo28MCepI4pOSzK3xP9n+UHIo/e+MbcPOretZ/ic4O1RDU0beiExqhsO
        fO2HGxf8sWVlLzSs7Q1f7xqo6+WK2t4e8PUR3FC/nhuGDWyBgnNcEL3PfIsi3gzDY3rN9cwAHPwpCNMm
        dES31jXh6+UE1xqvwaVaNThXe5XvEw96DW41nNDI1xMDujTC7Ek9cfSncNylxz+5Qe9hODXdDMJveVHY
        9b4fq09PCvgSxxHxXlFHBQV1r1Gdc/RCg7qeaFDPFS2b+SJpQldc2CseyKjAMGqiN5oo3pP8AFw9ORpf
        bR2C+OCuaNvcEzW9ORfnV+BSnXOsXo1UJ6/C07kamtR2w6DuDbFmdnec+nYEbrOleZLFubHiBXtQUFQF
        NTPT8YovRloElNBpprKlvOnGsTFYO78bKzHPcobXU6tmNcRHtED+EfE+7jruvKf5gUjbNwqzJ3ZG03qV
        b4BWTb2xbGYfpB8MooCjsX55D3i5ucOlRg24V3eChxOPiurwYmPft2dzXGH/VVrE3FcUgcfMiRePBmLx
        rEEUvg6Ljr8RS75yoWiuAgsRVxpIvMfF6SVe/ytee+W/+flltGleE2sWDsTl82EoLhqDZ7cD8Ft+NL7Y
        7oeWTb04jnigTjzBmcZnr+rCYsbVtQY3hhMF8UZ0aEec3C3tiOR2Rgd6YHGeH87sHolp4zvDy9kdL7/8
        d1STnCrz47gimmwwEVKOztVeQ/VXnTk/Of4dndvUx5uzRyB3H1stVqZmqUOyiXig8kI/lKRZBZQTpXTT
        Usba3077YefaPujato6h4TV8vKojcEQznPiBISxfJs3dTA88/fNQJEa3Qn1fV8PnNHp2aogP3h6M6xeC
        yFCsX9EN3p4uqMHd6cZ84y4hi57kVuNleLhVQ98ejSmghMyxwO0w5J8NVSG4Ua2aLPclX70EZwrjzGMN
        gZ8FEctVDEbvkWrSiVQXaLR6PrWwac1wFF3iJrxFD8wfiy/fG41WzewFdBPhiLvLa/Bw5We36nB3pZA8
        V5PtTlRoO5z6hUaWooqFVgnz3YGvhjNENkcdL4ryKt/HsZR49DrZUNqmkHmJmK70bGd+d+aGrc77qnPD
        NW9cC3MS++LKYX88FSeT1kWipdUbS9KirALKRTaTZvkJ6VwIfv1oBAKGtLIzuBH9ujfGF1v82ABbBCzO
        DcGBb4ZhXFBz1GI/Z/SMUItN+pghLfHDzpG4k0kBmXvXr+yNmp6u3N0vqRDjwQVJuHLjorw9nClgPVw9
        FwzTnRA8vx3CnDgY3Ts2hNPLf4XLazTIqzSsHGkAZ+fqqEFjOxMJoS5iLCUGxSSS15xeq652e+8uLXHs
        xxF4zsJGQugX7wXSA32UgJp47vQ6wYM50JPe5+HiDC9Xzs/lFdSu6YbxFPD0boZ3FlkmVshn9oxGdFAb
        1OW16q+yAOJmcWdIdnuNR6dqXJPAsRWySSWcyrWXeR9F5HknZ4uorZr4YPPy4Sxy2O4wnIpGZoZUiZgW
        DxQ1pbqhe5qzA/AsIwiZe8YgNb4z6tSq3Is6tq6PDcsG457sYLYDdy+F4MttIzFiYHPD+zUaMaclTuiG
        07/SaPkhuM6qcf3KPqjt5kZDcyHMB+7MWW4MUSKIF8NV/x71VQh9fi8Edy9PwIr5AxgeOR6N4lLdhWFX
        Qq8Ixe983lUMIDtdwh2N7ywC8pqbnONzIqR4pIuTO7auHY072VF4cCUGX34UhFbNfZT3lXmeVUB6nSeF
        q8WNVtvHhZHhVdSt44HYcR1wZi+j2PUg/J43FmsWjESL5gzrfJ8zw6bbq4Th3U3ey43hxrHdKaLaoJyP
        Bdm0rzJ1iKB/5/s5v1edVArp3K4R9n88Ag/P01FYyZolpLLBLxNQKWopX0vZhxSdYB5c0JPVWE1DATRa
        NPHG4hndWFLzOSbugtOsAlcMQZcOjQ3vF3zcndGhtS/eWDIABRcj8KiAZX+6Pzau7I46bs4WAzOE2qA3
        1vSojv7dG+DqefaL96Jw8IcYhI7qjtf+/hflXTXoCU7Of1F9mhuNIkVMLYbjlo180byRD3OoC8OeCz3T
        Se1uF2cJt3+HMz32pb/9N6Ii+uLE/nDcK4jG5x/RAymgeKCjgJ5sI3wZMtu3bozunZpznU3Qu3srzEnu
        i0uH2KLQ+zKODkf/rk3g6s4IJEKJMBLG+bwL1+LuQi9jZKjl5YL6tTwUvgyzrhS0htzDGsCDG0tw5QaW
        jfcac+Ibc3qjkK2biW2XajGy2EZcGkcBs1jVsMyFuCebSVPuGNw/6888OAg9JEQZiKDRuL4bUhM6oJDC
        PWMfl3PUD0tm9lCVmdH9EgrrccKD+rTAx+8Ox/08FhAMvdeUgD3gI4amwWTibspb6AUMOd40Rt+e9XDl
        AguYe+PwzaehGNS/nfp5SzOyPlfV9vXC6NHdsHNHHL4mIwZ1Qs2a7hSQoYsiSghTnspnqr38F/iN6oiD
        P7KvK4jh/UForRNQE0/w4abo0qEFtq+PxekDKUg7Og0ZJ6bi+qUElFwdx36QKeTbALRozHeJaAyHrqyG
        a3AjOlM8CYmyrk7t6iMpphs2LB2qkLaiaaP69HDOzboWtR6ZIz3Y6ZW/oVvHzjj2lfyTE4rHSGlmv1ic
        Fg2rgEy+PGli32FiY16c5o9fPxmJ4BGtaLzqhmIIdWu5YHxIG5zfF86qKwyXDozCzMnt0KSee7l7vdxd
        4FvTgxP1RZh/V+zeFUjv43PXQpSA71BAyYFqAUpAGo+LkJzhzSq0Xy964EX2bvfG4utPQjBoQDuGz78r
        w2rCCRL6pNl+Y4Ufbl2bgAdF8Vi5QFqDWhRMwqLcLzmI9/PZV1/6b4wa3h77f6CAhROUgBJCHT1Qjt7c
        YD27vY7dXwXjwTXa6y7bhlvs/9jSmG9G4H5WJDasiWDuc6Z3i4AUTTYin5Wj86tsQWr5sB8djNsXGQrl
        1xtW7/fTA/DO0pFoxtZK3mUTUN7PeUrI9/Wqhd0fDlW/HVvCaABDqBJQxLMIaM6hF+YF8CZ/ZOwLQHJc
        J4rkVk4MDR+vGhg2oDmLkSDcywxn8h7DndUa9WqWL2B8vN1Rr25NtGnVCNMnDcK5A+Pw5LoIyF5OFlCR
        gPQWEbB/7wYoSIuggJFKwIH92ioP1LxDQ57v1a0Ztm8MxcMbE/H0djy2rA1l2K7LaxxP7mORowno9Opf
        MZoC/vq9Pz1wAr75OBhtXq9pM6Dc78G8J5+93GqwcGrMzReAh4wcz++wqJK+lD2kqSgAty6FIyEuCt68
        z0VynkQQbhp3GYefq73yd/Tu1xOnfmIu4+Y1FbIg40Z4nDcCaQdDGI6bwtOdz/Je/fstIrrgs41s3tPG
        0eGCldOVXBoPi4A5ARSQYTSXUEBTbiAKj/tj+cyu3BWV94M9OzfC9nUjUXguDL9+7Y+xrL582WLo7/H0
        dEYdhrXGDeugR7eWeGv5aFxLG4/SW+EoEQEzygS0TJgFCYXzUAuvRu8VARui4BIFvB/JEBqGwQM7KA+U
        RQpiZM3Q3bo0wzsbWSDdj8HjB+OxaV0A2rfy5bhObJhrwMuFjbPcy3fV4BijR3SgBwbg0bVYfMfw3Pb1
        WuUElKOE0J5dmmDP5wH4/XIESm+Pg/lWJD2Q87oZjFvpwYibMA4+zOXifarKVG0R50gPrM5ipmfP7jjJ
        d0nvLH/XLOUmfpofhowDI7jGFvAQAWXjWt+prcuJFfP6xf64eWoCPTCUzX2gFkKtpal4IYVDnuTCYNw5
        E4Ctq/uxAqprJ4Yj7VvVxpuL+iHjSBA+2TwSg/q2LHeP9Hf16vjg9aYNMWxwZ+xg63E/j4tmP1fCEKoE
        XNUTNb3KCyjthLdHDQzo08gm4LefhWGIVUC535Ge3Vvi3c3j8Ntvk1B8byI2rw9G+za1uYtZIHBsTxY9
        0hLIT2PO1f4O/9Gd1K85j67H2QmoF1EQ72jdsjY+e38kHlyNgImiWX4IH0sicT8nHG+vCmOed2FlbOll
        VVGmWgJLOK3l7YbNKweg6EI4BeQmuBKBJ1njkHHIn1GlFbykFxbPlfexaBKkdpAi540F4bh2PF6FUJPk
        wAyrB6qkSC9UIVSF0SA8vBSKn3f6Y4zkQU+GGgdRNFo19cSi1M44/Ysfm/GB6MiSV3/dkvvc0KRBbbRr
        2RxR4QOx5xuGzkLmjbvkThgFDFQC1vJyU4ayE5DhzsezBgb2pYASQjUBB3VUP3dp3iFHWawXjdyvV1ts
        e5dtwd1EPLkfj3c30APb1qYYLhTPmd7HeTHMiSAefDY4oDsO7Q5GyY147P5iHDq0qaOE04un3kNqebtg
        rF8b/PRJFHNyAu7kTcLDK4kovRGD4qshOPSd9JFeloqT0UPaABcK6OzMnCjrYW7t2KoB4iO7YPm83lg+
        uyfemNUT54+OY1RpB29uYvmBQN4la5Gqt2G9WqjLCPbm0jhcPT6JtYpFs+J08cAcJkRJjESFUfHAy0F4
        nhuKzF/9MWVCG4a/Gnai6GnW0B3JCZ2w90t/LJvdnV7mZXfdSyrP2l5o1qgBK7hWmJ86ApnHY/GsKIpF
        AHfg/TAUsJnfsKaP8lQ3TtxNSm0u2JML8WLVWMvDFcP7NWWxQ8EfROD7z4MxfFB7VSh4ODuTGqpS9eGC
        a3l4YGDvTtguAt6Lx5MHUdi03h/tKKCriMw2Rt7j6+3KgsJN/ZISET4Ah/aG49n9aBz5JQA9O/pyTEYD
        NxGOIZybyIth0ItCeLEAqsV3tWnsy/RRHyMGt8XyOUOQeSQMz9gS5Z4I4lzbwVMqavaaXi4vwcXlb6ju
        9hpbGNls3Jwynlt1pgyO5SN9Xm1cOhyPoYPbw9ubIZQh3oubtravBxo18MXrLRqjWeMmWL8qAYUnJzLl
        iaP549El+SWGAsoJlf+0EMpyuDQ/FAXH/bB0Ri+0aFRxP1jX1xkRge3w4Tv+SJnUBY3qe9hdr+nFHVS3
        DoVtgv692mHjWyNxMycapjtR9L5IFgFRKMoYi00r+zF3uqlfOaSJ91DeQihKvTpeGD6wFa5mROH57+Px
        7a5ILrYzy/O/01DOKux4e1NAHwpYy5WhqAPe3zwBv9+cSg+Mw9YNoejUtiHFqKHybB1vT9SvyZxcvy43
        ly/GR4zAsX3jYfotBmlHIjDWvxeqv/Z/KCD7MZkPN5GrK0t6d4ZDhnM3iurNvO5JAXxruyM6oj1O72VY
        u8lWonAc3lvnT1GaKE+SvtTbWapLNv4UzlU8UebN8O1FMT2IeHzW4QkYPagz6tBekkMb+DLlsLVo/3oz
        dG3XBu1aNcc7b03EtVMioEWz4kwJoTmsaCR8WsVDPr8zqZryQ3DnXADef2sYenSwD4t6fLycMIB5b8GM
        URgXygn4llWtEj5r1/Rm+KzHyq4J/Ib3wKfbA/DblXCKFwEoxrKRD8E7K3qxYefuVD9VMby5utKrnFHT
        25khpCb8h7LfzIhE6YNo/MAwN3JoF5VbfDzduUk8Gdo81I6tQwYPaI8P3o2mgJPx5E4CtqwLRef2dXlP
        NeYnbijfmmjMiliiQuP6dRAfNQIn9zGP3Q/HzdwobHkngqIxT1JwL4rlw9Bck1HAR4zr4cmN5q08XX4g
        qF+3FmLHdcGZfRTwFiMKW4rMI+GYGtMTTRp6q4pSvNab3u9BL3Z3FaQa5pHnJRd3alMP2YdiMXJwN9T3
        9UV9bqrmjRsxFzdH147tWVV3QpeOHbDprRR64FS2ENQnKxyPMuMoYLaETibiHDbJeYSeBznSCx9lhGL/
        5/7sB1vQUBX3g61fb4TAUT3oYa24SBfbeW/u9vq1fdjY1mfz2oJxfygOfDeerQMrN4ZPMP+Z74YzB4Yy
        B/ajEG5q13p4sOdiCPFl61G/tjfDRz2EjOrCdkO8ZDx++iocfiM7U+AaXLAPdysX7VsLDWrXRAN669CB
        bfHelnDcv8kQemcyW4qx6N61AerWZkiq58tetA6aN2VUaN4EzZsxH8WMxIl9kepHgmd3xyPrQgzGDBiA
        Oj4+yrtre3mhPj834mZszPc0oviNanvyfS5o0aQuEsf3xNl9NCorUinMnlwfy++BmB7TA83q+qI6854r
        vdmT4kkz70EPk83txaOPuyu6tGuAnMPRCB7VE683qc+IVx8d6HHdO7ZBn24dmRK6oV+Pzti6PhnXziRS
        p0B2CsF4qIoYETCHL89hVZVHz6B4yOU5CvmMVVU6+8HJ0e1Qz7dMGEfq+HrTwxoyH9r/BUMScBPG8NYt
        mrIBbo8FM8cgg1XUs6Lx9ECW4BSwlM1wYWYINq4eQPE94SphhfmiFp+tV6c2GtBDmjevh4BRHVDARvn5
        w0j8+E0k/Eb3YsJ3Qd26XqhHD21Qj/fWq8P7fRleO1HAsRQwBiV3YrFtQxR692hJ8eqqnW3512ZNufFa
        Mr80Q0LsMBzfNw7P70VzPmNZGYfj8A+xiAjugUaNfeDJjeTLNTaoT89tWFe9oymPjRvUQovmjZEY1wdn
        D9Net0NgusG1cX1P2eflHQvHshl96aVeDMlsp5wYRl3cUKumO6MF8zCjQZ1avujeuQXyj8fwfYPQtk1L
        9sqvo0unDujVoxv69+nFinsA+nXrhQ82pqLoTJLqGErZOTzkhrZ6IF9MDzTlsZjJC6OArPYYRksp5pWj
        /lg0vTuaN7IvThyRn8F8uKu0796eTswv3vS+Bqw+W2JAvy5Yt2Y0vYhGusn8J/3TbcmD0SjKisBH7wxG
        l/aN0O712ujMkNKjQ2P06twEvbs1xeD+rTGZIelGJp+9Nx4Hd4/DpNj+6Na+AXp1achd2gj9u7+O/t3a
        cqFtEBPRH1/tjMGjIjbyd+Lx9UcT2J/2xIDerTG0fzuMHNgJowZ1w4iBXTGgZ3vMmzEc5w8xhN6ewDnF
        sKcbTxFjkHNhAkN+BJLjB8BvSDsM7vs6BvR6HYN6d8Ggnp0woHsHDO3XCQtnDETaYTbmbCVQxI15g9GF
        rcWza6EoSgvD3l0RWDh1KIb2qIt2LXwYWmuiYQMvNKRNmzarR4G64MqpBKROHgO/ET0whqkmeEx/hAcN
        RVToaIwLYXoaMwRfvZeI22cS2MTT4ajZo0uJmoBSiRLmQTOrT+RxMZIHKeat0yHYsmoIurav/O+DeuSX
        k9qs8BqzdRDv69SuLT2mPz7cEoLbOVFKQBObYNNtFjE8PrkRyTAai6P7knDwlwk4tj8Ox39NYFhLxKn9
        k8hkZDN5P2WpLp77W+F4ZJyJx9E9CTh1MB5nDsXj5IGJzGOTyGSknUjCzfxEPGfLUXovinltEs4dT8Lx
        g1Nx6sBUnD6YxGcm48zhKTixfwqyzyfhQQEbZHqfVMaSn5/fmoDnFPThVRZZ6RHIOxuB9OOROHcwju+Y
        hjN87gTff/rXeF6L4330vBvR6o/NuM4xbtCGFNLEdPEkPxq3Lo5FxlFW0J+MxTurw7Fyvh+WzRuKVUtG
        4INNYSjOjUfO0Um4sG8iLuxNxEXa4uK+ZKT9moz0/Sk4tycJ1y9OxBNGSnOO/KjNvjV9cpkHKqSVUAIy
        N6lCJox5cCz2sB8cNaiJ+su4kWCOaL3L680bokPb19GrawckjB+JvV/H4PfLMdypXCyrT0n6kjdMDHWm
        mxPwpIghj58f34rBU35XFMXyGEdjxuA5vbX0LsPo3WiGqwl4zHuf8twzGvvRnTjmO95/m/ffjmc4nIBn
        v4XjOXNm6Z3xeMznS8gT3vv0ZgLDeBzHSMDTWxOVWKWMBiaJCHJkYWW6HafmiFv0KLXZxnLjMUdei0fJ
        9Xg8vjGRFSfffY1zkpZIeR4FpHAmJSDXdoPPXef6rss1eXYsHuZH4R7L/zvnI3D7fBhuXwzFvXR67+UE
        PL8cTbGj8OQyQ3AeYbX+NI/j547jZ647h+NYfwuVIqb4khKQgmkwFyKXWAXEZfZGVPzCzwGIDW+Duj6V
        /31QkF9TGjDJS57p0KYVE3FHDOnXE4tnByD9aCyeXKN4DFEmGubRtUSc2TsT766YgdWzpmPNvPnYuWEl
        Ci9M53WGMwpVejcW9wuT8P3O+di4fCbWLpqBj9YvxYWD82BiOC1l4QGKmX5qMb7esQw7Ni3DJ5tX4vPN
        q/DFtqXMbauQfmw5fvh0NT7ZshSf8txn763GrndXYufmZXj/7cX47sOV+PXrpcg8lUpxWfiwob94dBGO
        /TgF97PjkX54Gg5+Ow9XzsygqNw0RYnYt2sG8k/OREnhRApL0RhFfs+ejPfXzsc3H8xC7qkpeFTITVfE
        VERBH+ZNxOFvZuHj9Uvw/sqF+PCNBfhiyyKc3D0Lv+dR6Dy2SFcjkX0kGbs/XcxrK/HNu2uwa/Nb2LVl
        Nb59fzGKTnFTZYdTK8I0V2wJoQ7iKQFZzFgFlMIm/0gQ5if3RvMGPoaiacjPROJ58s/RO7ZriR5dO6Ff
        r54IHDMYm9ayr7wYiWdcKG5Fo+DsdKydn4KwodMRE7AAMyavwvSJi5EYMhcHvlpET03G7cwp+PHjmUgM
        n4SQ4VMwZew8TI1cgKiAVOaEZKxInYMLx+bRk+KxadVShA5fgLiwJViUsgZLZryJFQvW4LMda3F4zxvY
        8tZbWDHrTUwZv4r5bBmi/ZZgweQ1mJWwBNtWr8ei5Dewcs5MnGNILC5IwIEfpyMpcho+eWcOFiSnYmHy
        HGQensd5ReP7HfMQOnQGDn+dTDEnqEhy4/wkbF65GG0bxaF/6yl4e24SK8t4lBaKMFG4emoqpkRNZ56e
        jdjQeUiOnY+Y8HkY6zcLy5Km4fmVGPbesfhiw3RMDl+EKeNWYO3s1Xhj1ttYNXsD3ly8CrkM+aXihVnB
        lhyYHluZgFRZBOTx9rkIvLd2NHq0N/77oPxWV7umh8p5bVo2RddObdGnZxc21L0wbHB/TIgKwK4P43A7
        KwbPGFLAHHjyx5kYPzoFAf1m4OMNi3HxyCzknJyISwcTeF8ie8Mp3HkpSAhOReqEKTj6czIyjiQg89hE
        5h0aa81URPnPxOzEVFw9l4yVs1chcnQqm+gpyDozDXlnpiLvHPNG9jTcv5yEgrQk5J5NxEcbZyJyzDIs
        TpmGrFOTkHliEi7z+U82z8S8pJn44M1k3M9JQO7pOEyKnInAIXMxrOdcrOFm+y1vEvLPTGJ7MBUzopci
        4wANynTwmIIf/XY6N+MMFh1LOI85iKUwX783Fb/l0gspYP7xKYgeORX+/Zfjm60pyNifiN07UjEjbiH8
        e87A/ayZeM7xP1ydjKSwNdi4aBkFm4grh6Ygn/n6yqFEehw3Sw5zK8WTMFqcHlNRCJV2wiKi+TJdleX7
        wS+DuJhW8GV/phfPhw2uVJvN2b+ofMemc0DfHhSuH0YMG4SAMcOwIGUsTvzEkHI5DqWSJ25G4/LZZGxY
        Ng8xgYuQNHYB1i+agp3vUMCjqXhUMBFpBydhwZQUjBuVgkPfTWaeimaBwTx0l7nk1njksB9atywF/n1m
        Yd9nU7F67tsIHzUPi2ek4osPZuLrD1Lw7QfJ+PWLVNzLoafcSuCz4/HLpymYGLKc4TiR3zkXKVYY0i8e
        SMLKmTMxMyYVuceSUHx5Ara+SY9pn4ToMbPw665k3MuagJ92psJ/wFx8995M3L4wma1CMr7cOhNTIqej
        Y8PJiI1YgIjhyejRfCpWpKTiysnJKGVOu3J8GuIDkzG0ywIsmzYJH70xEesWzOBmWIwFExfxfVPw9HI8
        dryVjLig1ZgdvwxfbUrEVxum4KuN07Dr7YnI3jsVjy9JGGWfLr/EsPCzCkjRBJ0XioDIY0VFnlP1rP0h
        3C090JTlrwjn4erKBtcDDdmotmLI7NyhNcNlVzbRfTFq+CD4jxoO/zEjET02EFvfGI+rJ1k0FErBYvn5
        7Mm1WFzmbt73eQree3MBVs5dhOighYgJmk3PW8A8QmPGzUBS+BxcPTvJUkww/5hESAp4PzcO33w4HSGD
        lzNfJGP1wvUY57ecoXA1Pt++jq3DWvywYz32fvom7ucxRMmzt6Px884ZiA9egY2Lp3Au8Rw3luNG01MS
        sGvrZEwMn4x3uDGeMtR/uyMJg7tOxOyE6Ug/MB0Zh5KYq5cxIsxD9tEpKMlnrjwwE0tTF6F/x7nw770Q
        W5a/yXtWYniPWRg3cj5++GA6nrFCvXx8OuJDZlLAhViasgQLp63gxliBlCjm2m+mo5Re+owb/BMKmBi0
        EvMmcg3bV5JV+HrbW/h442rkHJyDJ+ny5yTxwDCUXJpSJqBKjmzcbTBJIneswkRunx/LfDAaQ/o0Z7h0
        Z77zRGM2zm3YCHft1A79+nTH8CEDlHDBfqMQGuCHiNAAzJ4eiV8+S8S9DFZ616Qai1QeCPZMpdeiUHI5
        lrmO4e1EKpvnFMSHpnIHzqVHLcLM+EVICJqD9CNJNDbnwucs1et4FGUmYMeWGQgashzf7ZiO9Yvfohcs
        xVfb5rDsn4mbmam4lTkTdzOmszCJVe2KVJM/fcLQHbKGHj+V0YACUsTS2+PxjGOnU6CVKXMRMiAVl88n
        443F89G91WT4M8y/9+Y8fLN9LhICaFB63G9ZjCasQDMOTqEgc+l1c/D9tmkoOp2CKyem4N2lqQgZMhsr
        ZqbiQTZD+PFkTAmfiYTQtdj/+Syc+HYO3pq7DJPHrmBYX8XcFstqMx6fvpmCGTFr8eGaN9jCTWHfN5nH
        abh+YiqK0+JhyqQdsiwRsyRdFTHifQyVOsDKU8MsPSG98CmTZ/bBcKyZM4RNbEu0btkQbdtSPLYI/foy
        1w0bCD//EQgO9sPYsEDERoVgdvI47NzCfHI8EY+vsPJkOW2iaKbrE3A7bSIKTk/F7Ywp9MZEPCuYzNw3
        G7MnLULEkDnYvHQh3l2+FHGBSxjuliOTRc/DK0l4fG0Sn0nGns8XMGetwLTIxWzCk7Bp+RtIjV+KX79M
        odhxylNLKTRu8L3SGlB4+fHgx49TERm4BusYskvZooAimsQ7GRkkxO/9LAVBg2Zh1sSFiBi9jO9fjuAh
        C1k48X2TlmB+4hLOO5l5jSV/QRz2fzWdoX4RliYvwnVGCjNbCzMjTfaBZG7AmZg8bhGOfLkCBaemIyVm
        NlLiNyOT/d2TLHrvjylYN3chJo1bivM/zUdxZiI+XZdCj1+LzzetwBNGmaesgp/msmXKZlskfwTIliKG
        mpDi9IkiIAWrSEB6nkVAeiFL3UcZkUj7ZRy2rvLDtNh+iAjuS08biIigIQyVwxEXE4CkiRGYmxqFdStj
        8d3Oicg5noBi9jTifSbV3Ibh+bUEHPpmBjYsWYS1C+fj43em49P1zInzFyDSbylz0Tyc+TkFlxi2tq+Z
        j1lx67B41nJ8uHE+Pts6F5tWLMOSqWuxKnUN9tCjHlyJxfY3VmNm4jIalAKyxzOxUCoVT79Bz+exVFoX
        Hnd/lIoJfiuwbiHzKvtOs/rpS+6jyKTwQgI2rJiHTg1TETVqPr7btpi5aglChi5kNFiAnz5YzM3GsMue
        7g4b6e1rKGziUubEuShmSH3Oxt3EyrMkj960Uarm1Vg/fyWb9MUsuBZgdtJ2ZB1IYW3Be1hFnvsxGavm
        LMPcSRRx7xx8tG4Opo1fiSXJK/H9e3w/W5/vNs/Gl+tTKHwKNRBPZY+exR5d2gj5ZVsTUO95dgKykTTz
        c2n+WDzJjmSYGI8zP0bhuw/GYsfGMHywLhAfrg/Czk0R+Oq9GOz/Ml5Vk7eYcKUpfULvKxXvI/Irhalw
        AjIOJzLPJWPbWwuwac0qetBqbKfH7Vibiov7k9jwyr/0moCrLFb2f8r+cONcfLp5OT5/dxU+Y//23Yfz
        cXbfDPyeM5HChOHU3tn48bO5yGPRIA21iaLIDwTKA5WHWbws68gkfPHuXBz5JkkVVGAViRu8j/0prtOr
        CmNZgU7FW3NYBG1LRdHZybj463S+dwa/p+Dmuem8n+soDGXfl8hWYhZ++WQGCk4mwlQQTQG52Qu4Geih
        eUen4qeP5uCXHbP43Bzs/ng29ny6kk0853iFNr1Mr8+Ix4W9s/AR+8ecg7Nx6ocZ+GrrLOzaNBe7P1yE
        H7cvxS/bFuL7Tbz+6wyKLpWo/JOKUDxKS7AIqHAUkIJpAkoONNMDzZd5JKb8aLrzBDxIj8Zt9na3mB9v
        nR/Hqiwa99LG42HmeBTS8A95j4QaCSnP6YHPGT7NhTRUQQyFjWIeiWL/lIi8E2x8j81iCGIJnzGRxQ49
        g4Y10cDPmWeK8+JYASbg5oVk3Dw/nTs/EQ/yOYaMdU28iIZg5fpb3mQ8pujyzxvM6tcREYfvpJhyDre4
        ATn2/RyW5BzTzGuSi9V1ime6Lu8cz3eOx52MGDxgCHtOMUrYo93PjqNgsXjOzWfinHCNG5HXHjF3PZTw
        xvWYr9DTC7lRGUZNbMqf8tzvbJ1+y4zF0/wJ/JyIR5m0SS43NAV8fjWCTsH7cuJw99wkPM6IY2/HAu1i
        LEUm55hqziXg3pk45sEklKQxl2dSm0xWoTwWX2IVas6i9wlWL6wohAqgBwqycyzIZwp7RSZvWYAcTVe5
        iAJeK+A1LsRcIGFlnCpaZPFm7lQzjyYJqxJyrrOJFUOLh16jAOI1FED9REVxxFPUdSUYn5Nzco1GN/N5
        SxikYeW3UgoKCmH5WYvwHnB8y++UHEOFS16/Lu+Td3Ge6ucuGY/vlQ0h416nLXjeLJtO5mxF5qoElLVx
        jSKiWg/XWKo+W9ZrvsINc4X2usI64irfQZuYGYnM8rdQ+b+O5DmRX2DM+UQchHWG/HAi6coS9eT3aG4U
        etxzOS/FJbWCiJhFAdPl74HqC0+KcHpsInICVm+UlkIKGlM+Bydgjwh15GStKCHlqAmnFsWdLkfmBhEM
        FFMMo8SzGkiOYPgBdy94nzKeMrKIKIaT64LlvIglBjdfp3GkslUeJPeLYCIA7xXRxdg0OkQsdY3vV+8U
        0eScZS4WOI7cL3AO4PzB8h4FEjUs56RIASOKnJd1mXhNwqVs2ufqPMeQKKW3hdrkcuR3epyZTmAS8vg5
        l88oB7HYWQlIu5vZukE17YKcs0ZJqUIV8ltovFVAfqlURCWkvMQei0dSQA1OFGrnlWEWA2iIkJphuFgx
        gBJXztmwGs/2mWhGFQHFSEpI8Qg+L62J8hQjeJ3Pa95jQc5ZxreIpXuP7b1EBJO5yVzV0QGKpkQUZJ1q
        rXLkfEQwBb9rAgq0kfI0RztqDkI7m3PpoaqV4+ds2l3TglWnQulFMsIZUpkD5YMIqIoZEdFRSA5kJ6KD
        kHYCaiIKOhFtcIGGxnBEjFnReZ2xjUWzItd5X0UYjv8CRCC1BqO1aTjaQ4febjb7aeJZBbRDp4PkPJt4
        xgLyqEepr0M3sKOIsqvsJluRgILeG7XP2ncbVRRQvLhC/kEB9XOzogQ0WpMevR0csLeZBXsBrTjannpU
        LqASkcLosLmswDhsG1xDkqwKBxbgIKIKF8obuSg91oVakrwOA4PZYWTkfwSjd+hxnJ8exzUprOvV2cBm
        i8qE0zkGWMSAjbqd7YmdeJpemoDm9DIB9ZQTUZDBBXmRhiRhimkvKAUiYMIuhyRyG7zP0RBGBhOMjKzy
        UEUY3a/D6B161NwsotjDa6waDddGtLXrN7cdqroU29Gejoij6Oxt51BKNO1IqFvJRfHASzxpIKLtQW3A
        TL40S+BE2cyXiWdP2QIswIrtu1q8RUAjA9iJKZQzrhhfg0JViP4+I4zGtqJE0sO1at6kjnxeg22AoF9z
        hSjhDNAcQyeeYNNAEPHSeT5djoS6lVyQn9KUgIJFVYuY/E7MWsmqH1h7mfJELk4/ESMR5bvDOTsDvIAy
        b+V3BcX5J6AfV/VhOsrNQTd3NX+raArrmm1rrxCKrxfLEeUcgghnxaqDJp5ZtNKhBJSTjhcsSpMMDkjM
        9D4N5YEa2VzAC0Q08bOgN0BlOBrvX05VvccIJYw9drZwRCKX3n4amWWflY2tdrfpYCCeUOaBjugetA1m
        pbyIOpSYGjSMkPsHyaN3VBG8AKNnymE0hxcADa6vDIf16+xitqK+620niHhiW3EQB1trwhk6GbEImEaR
        0spf/EMiaugma5u03SKt5NLARAmsId//HbDORZujQubsiEQfHbb1aueM7GNF2U9Db1u9zYleOPMlnlNa
        lVFyXv4eqAn4IhE1RECriI7YJqgTUI9+wQojwwiOBqwIo2f1GD2jx+gZIxznLegEqQrlbFUF4fReZyRg
        8TmbB1bAJRncGDNd34L9xNTkZMKaaA4LsUOJKliNYmS8ytAb1BGj+yvC6Hk9RnN/AUZ2UWTwuhLNGBFK
        Q69Fue8XI14goIhng98dX6YmUoGAIqwswmFRdhgZxtFwLyKrEozur4iK5uF4vgoY2UNFLA1HOzpg5GlC
        xQLyg/6CDU08g5fYsIpoiCRnwci4VsyVUGZghrp/MeXe/SIyK8HODmIjB5tVhp3TECNdNKhbiU1ADceb
        XiRgRegXIYsyMsJ/KrIp9eurCCO7VILysCoKp2EVUE46oG6WgaxoAytkcnp4rjKMFmelLI9WjKER/4kY
        vtNgrjaM1viHkDGIzqZmOertXQ6dNhc0GELPioAXeAMxn5eTls+4yO8EaXxRRVyiAYSqiFgRWoj5H4dz
        VpSdk9ykYTjXqqITxobNg6y2ewFi+3JYNdJTfEb+UZPDSUEJqnvY6CU2xCM1MY1I/0+D61EYXbNitM5y
        WO1TRZSdNQcywEgnJSDOUTDieFE9VBUBjdAvxMgA/46ojUhDqhRhcF1D1mS0Zo0XXXdAs3FFAtr0sOqk
        p0QJqMTiTQ5YciEH0OKwdYF/CNnJGVzQvztKHOt8qzJn/b2OGNnBELGpxb42G4uIOuz0cIS6WQSsqAIV
        jGL8i9AnfOF/XRVKHNdYEUb2qQh9ztQw0kSjrAotK0ulORTsbpSBjF5YVYwWZsWoAtQwNN6/AMN3Cwbz
        tWG0To0XXTdAWoiKGngbOp00is/KH3SN4q64sEG8tmGX4/jdYFJVQX5WUjgY8H8czsHESlRQfxEwmGuV
        cfQiO3S2M7KrAY75UV+nVKkKNRpUYRORnx3zwP/PGOS5sn6O5yrjolBmezsMdLJVoY4X9J4oGL6sImzC
        WnGs4v7N+Z+uQgWbna1212PTRDCqQs3nOAAxnbfHfCEKpotlmNOi/ziXSPr4irn0b8QfmbN+jf8gdjam
        zfXY6WHVSU/x6Un4v76VWyPJyp5AAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>About...</value>
  </data>
  <data name="decriptionRichTextBox.Text" xml:space="preserve">
    <value></value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Forums:</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib">
    <value>True</value>
  </metadata>
</root>