<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="$this.Text" xml:space="preserve">
    <value>TIFF Encoder Settings</value>
  </data>
  <data name="fileVersionGroupBox.Text" xml:space="preserve">
    <value>File version</value>
  </data>
  <data name="fileFormatGroupBox.Text" xml:space="preserve">
    <value>File format</value>
  </data>
  <data name="fileTabPage.Text" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="copyExifMetadataCheckBox.Text" xml:space="preserve">
    <value>Copy Exif metadata</value>
  </data>
  <data name="copyGpsMetadataCheckBox.Text" xml:space="preserve">
    <value>Copy Gps metadata</value>
  </data>
  <data name="copyCommonMetadataCheckBox.Text" xml:space="preserve">
    <value>Copy common metadata</value>
  </data>
  <data name="metadataTabPage.Text" xml:space="preserve">
    <value>Metadata</value>
  </data>
  <data name="annotationsTabPage.Text" xml:space="preserve">
    <value>Annotations</value>
  </data>
  <data name="jpeg2000SettingsButton.Text" xml:space="preserve">
    <value>JPEG2000 Settings...</value>
  </data>
  <data name="jpeg2000CompressionAdvancedSettingsGroupBox.Text" xml:space="preserve">
    <value>JPEG2000 Advanced Settings</value>
  </data>
  <data name="zipUsePredictorCheckBox.Text" xml:space="preserve">
    <value>Use predictor</value>
  </data>
  <data name="zipCompressionAdvancedSettingsGroupBox.Text" xml:space="preserve">
    <value>ZIP Advanced Settings</value>
  </data>
  <data name="lzwUsePredictorCheckBox.Text" xml:space="preserve">
    <value>Use predictor</value>
  </data>
  <data name="lzwCompressionAdvancedSettingsGroupBox.Text" xml:space="preserve">
    <value>LZW Advanced Settings</value>
  </data>
  <data name="useTilesRadioButton.Text" xml:space="preserve">
    <value>Use tiles</value>
  </data>
  <data name="useStripsRadioButton.Text" xml:space="preserve">
    <value>Use strips</value>
  </data>
  <data name="rowsPerStripLabel.Text" xml:space="preserve">
    <value>Rows per strip</value>
  </data>
  <data name="tileWidthLabel.Text" xml:space="preserve">
    <value>Tile width</value>
  </data>
  <data name="tileHeightLabel.Text" xml:space="preserve">
    <value>Tile height</value>
  </data>
  <data name="stripsAndTilesGroupBox.Text" xml:space="preserve">
    <value>Strips and Tiles</value>
  </data>
  <data name="binarizationThresholdLabel.Text" xml:space="preserve">
    <value>Threshold</value>
  </data>
  <data name="binarizationAdvancedSettingsGroupBox.Text" xml:space="preserve">
    <value>Binarization Advanced Settings</value>
  </data>
  <data name="compressionTabPage.Text" xml:space="preserve">
    <value>Compression</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="zipLevelLabel.Text" xml:space="preserve">
    <value>ZIP Level</value>
  </data>
  <data name="jpegQualityLabel.Text" xml:space="preserve">
    <value>JPEG Quality</value>
  </data>
  <data name="jpegGrayscaleCheckBox.Text" xml:space="preserve">
    <value>Grayscale JPEG</value>
  </data>
  <data name="jpegCompressionAdvancedSettingsGroupBox.Text" xml:space="preserve">
    <value>JPEG Advanced Settings</value>
  </data>
  <data name="autoCompressionRadioButton.Text" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="addImagesToExistingFileCheckBox.Text" xml:space="preserve">
    <value>Append to existing document</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib">
    <value>True</value>
  </metadata>
</root>