﻿using Newtonsoft.Json.Linq;
using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Net.Mime.MediaTypeNames;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace InduVision.LkControls.SetPanel
{
    public partial class WwwlPanel : UserControl
    {
        MainForm mainForm;

        double maxVal, minVal;

        int widthValue = 0;
        int centerValue = 0;
        int[] histogram = new int[65536];


        public WwwlPanel(MainForm mainForm)
        {
            InitializeComponent();

            this.mainForm = mainForm;
            CalculateHistogram();

            centerTrackBar.Scroll += CenterTrackBar_Scroll;
            widthTrackBar.Scroll += WidthTrackBar_Scroll;

            widthValue = (int)mainForm.getDicomViewerTool().DicomImageVoiLut.WindowWidth;

            if (widthValue < 200)
            {
                widthValue = 200;
            }
            else if (widthValue > 65535)
            {
                widthValue = 65535;
            }

            this.widthTrackBar.Minimum = 200;
            this.widthTrackBar.Maximum = 65535;
            this.widthTrackBar.Value = widthValue;
            this.wWTextBox.Value = widthValue;
            this.widthMinLabel.Text = 200.ToString();
            this.widthMaxLabel.Text = 65535.ToString();

            centerValue = (int)mainForm.getDicomViewerTool().DicomImageVoiLut.WindowCenter;

            if (centerValue < 200)
            {
                centerValue = 200;
            }
            else if (centerValue > 65535)
            {
                centerValue = 65535;
            }

            this.centerTrackBar.Minimum = 200;
            this.centerTrackBar.Maximum = 65535;
            this.centerTrackBar.Value = centerValue;
            this.wLTextBox.Value = centerValue;
            this.centerMinLabel.Text = 200.ToString();
            this.centerMaxLabel.Text = 65535.ToString();

            this.wLTextBox.ValueChanged += WLTextBox_ValueChanged;
            this.wWTextBox.ValueChanged += WWTextBox_ValueChanged;

            pictureBox1.Invalidate();
        }

        private void CalculateHistogram()
        {
            Array.Clear(histogram, 0, histogram.Length);
            if (MainForm.RawMat != null && MainForm.RawMat.Type() == MatType.CV_16UC1)
            {
                ushort[] data = new ushort[MainForm.RawMat.Rows * MainForm.RawMat.Cols];
                MainForm.RawMat.GetArray(out data);
                foreach (var p in data)
                {
                    histogram[p]++;
                }
            }
        }

        private void WWTextBox_ValueChanged(object sender, EventArgs e)
        {
            if (this.wWTextBox.Value < this.widthTrackBar.Maximum && this.wLTextBox.Value > this.widthTrackBar.Minimum)
            {
                this.widthTrackBar.Value = (int)this.wWTextBox.Value;
                widthValue = widthTrackBar.Value;
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            else if (this.wWTextBox.Value > this.widthTrackBar.Maximum)
            {
                this.widthTrackBar.Value = (int)this.widthTrackBar.Maximum;
                widthValue = widthTrackBar.Value;
                this.wWTextBox.Value = this.widthTrackBar.Maximum;
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            else if (this.wWTextBox.Value < this.widthTrackBar.Minimum)
            {
                this.widthTrackBar.Value = (int)this.widthTrackBar.Minimum;
                widthValue = widthTrackBar.Value;
                this.wWTextBox.Value = this.widthTrackBar.Minimum;
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            pictureBox1.Invalidate();
        }

        private void WLTextBox_ValueChanged(object sender, EventArgs e)
        {
            if (this.wLTextBox.Value < this.centerTrackBar.Maximum && this.wLTextBox.Value > this.centerTrackBar.Minimum)
            {
                this.centerTrackBar.Value = (int)this.wLTextBox.Value;
                centerValue = centerTrackBar.Value;
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            else if (this.wLTextBox.Value > this.centerTrackBar.Maximum)
            {
                this.centerTrackBar.Value = (int)this.centerTrackBar.Maximum;
                centerValue = centerTrackBar.Value;
                this.wLTextBox.Value = this.centerTrackBar.Maximum;
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            else if (this.wLTextBox.Value < this.centerTrackBar.Minimum)
            {
                this.centerTrackBar.Value = (int)this.centerTrackBar.Minimum;
                centerValue = centerTrackBar.Value;
                this.wLTextBox.Value = this.centerTrackBar.Minimum;
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            pictureBox1.Invalidate();
        }

        private void WidthTrackBar_Scroll(object sender, System.EventArgs e)
        {
            widthValue = widthTrackBar.Value;
            this.wWTextBox.Text = widthTrackBar.Value.ToString();
            if (mainForm.imageViewer1.Image != null)
            {
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            pictureBox1.Update();
        }

        private void CenterTrackBar_Scroll(object sender, System.EventArgs e)
        {
            centerValue = centerTrackBar.Value;
            this.wLTextBox.Text = centerTrackBar.Value.ToString();
            if (mainForm.imageViewer1.Image != null)
            {
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(centerTrackBar.Value, widthTrackBar.Value);
            }
            pictureBox1.Update();
        }

        private void PictureBox_Paint(object sender, PaintEventArgs e)
        {
            if (histogram == null) return;

            Graphics g = e.Graphics;
            g.Clear(Color.Black);

            int w = pictureBox1.Width;
            int h = pictureBox1.Height;

            int binSize = 65536 / w;
            double[] bins = new double[w];

            for (int i = 0; i < w; i++)
            {
                int sum = 0;
                for (int j = i * binSize; j < (i + 1) * binSize && j < histogram.Length; j++)
                {
                    sum += histogram[j];
                }

                // 取对数，避免对0取log
                bins[i] = Math.Log10(sum + 1);
            }

            double max = bins.Max();
            if (max == 0) max = 1;

            for (int i = 0; i < w; i++)
            {
                int barHeight = (int)(bins[i] / max * h);
                g.DrawLine(Pens.White, i, h, i, h - barHeight);
            }

            // 绘制窗宽窗位
            int wlLeft = (int)(((double)(centerValue - widthValue / 2) / 65535) * w);
            int wlRight = (int)(((double)(centerValue + widthValue / 2) / 65535) * w);
            // 确保边界安全，考虑线宽
            wlLeft = Math.Max(1, Math.Min(wlLeft, w - 2));
            wlRight = Math.Max(1, Math.Min(wlRight, w - 2));

            Pen penWL = new Pen(Color.Red, 2);
            g.DrawLine(penWL, wlLeft, 1, wlLeft, h - 2); // 避开上下边缘1px
            g.DrawLine(penWL, wlRight, 1, wlRight, h - 2);
            if (wlLeft >= 0 && wlLeft < w)
                g.DrawLine(penWL, wlLeft, 0, wlLeft, h);
            if (wlRight >= 0 && wlRight < w)
                g.DrawLine(penWL, wlRight, 0, wlRight, h);
            penWL.Dispose();

            // 绘制灰度轴标注
            using (System.Drawing.Font font = new System.Drawing.Font("Arial", 8))
            using (Brush brush = new SolidBrush(Color.Red))
            {
                // 左侧标注 “低位”
                g.DrawString("高位", font, brush, 5, h - 20);

                // 右侧标注 “高位”
                SizeF highSize = g.MeasureString("高位", font);
                g.DrawString("低位", font, brush, w - highSize.Width - 5, h - 20);
            }
        }
    }
}
