﻿using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InduVision.LkControls.SetPanel
{
    public partial class ExportImagePanel : UserControl
    {
        MainForm mainForm;
        public ExportImagePanel(MainForm _mainForm)
        {
            InitializeComponent();
            formatCombox.SelectedIndex = 0;
            numberCombox.SelectedIndex = 0;
            this.mainForm = _mainForm;
        }

        private void applyButton_Click(object sender, EventArgs e)
        {
            if (numberCombox.SelectedIndex == 0) {
                mainForm.saveImageAs(MainForm.currentFileName, (formatCombox.SelectedIndex + 1));
            } else if (numberCombox.SelectedIndex == 1) {
                using (FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog())
                {
                    folderBrowserDialog.Description = "请选择一个文件夹"; // 设置对话框标题
                    folderBrowserDialog.ShowNewFolderButton = true; // 是否允许创建新文件夹

                    if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
                    {
                        string[] rawFiles = Directory.GetFiles(mainForm.WorkDirectory, "*.dcm");
                        foreach (string file in rawFiles)
                        {

                            var dicomFile1 = FellowOakDicom.DicomFile.Open(file);

                            int iWidth = dicomFile1.Dataset.GetSingleValue<int>(FellowOakDicom.DicomTag.Columns);
                            int iHeight = dicomFile1.Dataset.GetSingleValue<int>(FellowOakDicom.DicomTag.Rows);

                            // 获取像素数据
                            byte[] rawData = dicomFile1.Dataset.GetValues<byte>(FellowOakDicom.DicomTag.PixelData);

                            string fileName = Path.GetFileNameWithoutExtension(file);

                            if (formatCombox.SelectedIndex == 3)
                            {
                                string destFile = Path.Combine(folderBrowserDialog.SelectedPath, fileName + ".raw");

                                File.WriteAllBytes(destFile, rawData);
                            }
                            else {
                                Mat rawMat = new Mat(iHeight, iWidth, MainForm.IMAGE_MATTYPE);

                                int bytesPerPixel = (MainForm.IMAGE_MATTYPE == MatType.CV_16U) ? 2 : 1;
                                int length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH * bytesPerPixel;

                                IntPtr ptr = rawMat.Data;  // 获取 Mat 的数据指针
                                Marshal.Copy(rawData, 0, ptr, rawData.Length);

                                Rect roi = new Rect(MainForm.CROPWIDTH, 0, rawMat.Cols - MainForm.CROPWIDTH, rawMat.Rows);

                                // 使用矩形区域来裁剪图像
                                using (Mat croppedMat = new Mat(rawMat, roi))
                                {
                                    rawMat.Dispose();
                                    rawMat = croppedMat.Clone();  // Clone() 创建一个新的 Mat，确保 croppedMat 在 using 结束后不会释放 rawMat
                                }

                                byte[] imageData1;
                                Cv2.ImEncode(".tiff", rawMat, out imageData1, new int[] { (int)ImwriteFlags.TiffCompression, 1 });

                                if (formatCombox.SelectedIndex == 0)
                                {
                                    string destFile = Path.Combine(folderBrowserDialog.SelectedPath, fileName + ".tiff");
                                    Cv2.ImWrite(destFile, rawMat, new int[] { (int)ImwriteFlags.TiffCompression, 1 });
                                }
                                else if (formatCombox.SelectedIndex == 1)
                                {
                                    string destFile = Path.Combine(folderBrowserDialog.SelectedPath, fileName + ".jpg");
                                    Mat rawMat8Bit = new Mat();
                                    Cv2.Normalize(rawMat, rawMat8Bit, 0, 255, NormTypes.MinMax, MatType.CV_8U);
                                    Cv2.ImWrite(destFile, rawMat8Bit, new int[] { (int)ImwriteFlags.JpegQuality, 95 });
                                    rawMat8Bit.Dispose();
                                }
                                else if (formatCombox.SelectedIndex == 2)
                                {
                                    string destFile = Path.Combine(folderBrowserDialog.SelectedPath, fileName + ".png");
                                    Cv2.ImWrite(destFile, rawMat, new int[] { (int)ImwriteFlags.PngCompression, 9 });
                                }
                                rawMat.Dispose();
                            }
                        }
                    }
                }
            }
            
        }

        private void closeButton_Click(object sender, EventArgs e)
        {
            mainForm.toolsPanel.splitContainer1.Panel2Collapsed = true;

            mainForm.toolsPanel.splitContainer1.SplitterDistance = mainForm.toolsPanel.splitContainer1.Height;

            mainForm.toolsPanel.splitContainer1.Panel2.Controls.Clear();
        }
    }
}
