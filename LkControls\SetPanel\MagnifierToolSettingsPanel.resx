<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 33</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 20</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>宽度</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 117</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 20</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>缩放</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="widthNumericUpDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>83, 31</value>
  </data>
  <data name="widthNumericUpDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 27</value>
  </data>
  <data name="widthNumericUpDown.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;widthNumericUpDown.Name" xml:space="preserve">
    <value>widthNumericUpDown</value>
  </data>
  <data name="&gt;&gt;widthNumericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;widthNumericUpDown.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;widthNumericUpDown.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="heightNumericUpDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>83, 72</value>
  </data>
  <data name="heightNumericUpDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 27</value>
  </data>
  <data name="heightNumericUpDown.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;heightNumericUpDown.Name" xml:space="preserve">
    <value>heightNumericUpDown</value>
  </data>
  <data name="&gt;&gt;heightNumericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;heightNumericUpDown.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;heightNumericUpDown.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="zoomNumericUpDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>83, 113</value>
  </data>
  <data name="zoomNumericUpDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 27</value>
  </data>
  <data name="zoomNumericUpDown.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;zoomNumericUpDown.Name" xml:space="preserve">
    <value>zoomNumericUpDown</value>
  </data>
  <data name="&gt;&gt;zoomNumericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;zoomNumericUpDown.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;zoomNumericUpDown.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="borderWidthNumericUpDown.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left, Right</value>
  </data>
  <data name="borderWidthNumericUpDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>72, 75</value>
  </data>
  <data name="borderWidthNumericUpDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 27</value>
  </data>
  <data name="borderWidthNumericUpDown.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;borderWidthNumericUpDown.Name" xml:space="preserve">
    <value>borderWidthNumericUpDown</value>
  </data>
  <data name="&gt;&gt;borderWidthNumericUpDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;borderWidthNumericUpDown.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;borderWidthNumericUpDown.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="borderColorPanelControl.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left, Right</value>
  </data>
  <data name="borderColorPanelControl.Location" type="System.Drawing.Point, System.Drawing">
    <value>72, 34</value>
  </data>
  <data name="borderColorPanelControl.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 20</value>
  </data>
  <data name="borderColorPanelControl.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;borderColorPanelControl.Name" xml:space="preserve">
    <value>borderColorPanelControl</value>
  </data>
  <data name="&gt;&gt;borderColorPanelControl.Type" xml:space="preserve">
    <value>DemosCommonCode.CustomControls.ColorPanelControl, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;borderColorPanelControl.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;borderColorPanelControl.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 77</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 20</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>宽度</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 34</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 20</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>颜色</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="ellipticalOutlineCheckBox.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="ellipticalOutlineCheckBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 33</value>
  </data>
  <data name="ellipticalOutlineCheckBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 24</value>
  </data>
  <data name="ellipticalOutlineCheckBox.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="ellipticalOutlineCheckBox.Text" xml:space="preserve">
    <value>椭圆轮廓</value>
  </data>
  <data name="&gt;&gt;ellipticalOutlineCheckBox.Name" xml:space="preserve">
    <value>ellipticalOutlineCheckBox</value>
  </data>
  <data name="&gt;&gt;ellipticalOutlineCheckBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ellipticalOutlineCheckBox.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;ellipticalOutlineCheckBox.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="invertCheckBox.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="invertCheckBox.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="invertCheckBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>137, 33</value>
  </data>
  <data name="invertCheckBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 24</value>
  </data>
  <data name="invertCheckBox.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="invertCheckBox.Text" xml:space="preserve">
    <value>反转</value>
  </data>
  <data name="&gt;&gt;invertCheckBox.Name" xml:space="preserve">
    <value>invertCheckBox</value>
  </data>
  <data name="&gt;&gt;invertCheckBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;invertCheckBox.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;invertCheckBox.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grayscaleCheckBox.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="grayscaleCheckBox.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grayscaleCheckBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 109</value>
  </data>
  <data name="grayscaleCheckBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 24</value>
  </data>
  <data name="grayscaleCheckBox.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="grayscaleCheckBox.Text" xml:space="preserve">
    <value>灰度</value>
  </data>
  <data name="&gt;&gt;grayscaleCheckBox.Name" xml:space="preserve">
    <value>grayscaleCheckBox</value>
  </data>
  <data name="&gt;&gt;grayscaleCheckBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;grayscaleCheckBox.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;grayscaleCheckBox.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="posterizeCheckBox.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="posterizeCheckBox.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="posterizeCheckBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 72</value>
  </data>
  <data name="posterizeCheckBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 24</value>
  </data>
  <data name="posterizeCheckBox.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="posterizeCheckBox.Text" xml:space="preserve">
    <value>海报</value>
  </data>
  <data name="&gt;&gt;posterizeCheckBox.Name" xml:space="preserve">
    <value>posterizeCheckBox</value>
  </data>
  <data name="&gt;&gt;posterizeCheckBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;posterizeCheckBox.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;posterizeCheckBox.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="oilPaintingCheckBox.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="oilPaintingCheckBox.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="oilPaintingCheckBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 72</value>
  </data>
  <data name="oilPaintingCheckBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 24</value>
  </data>
  <data name="oilPaintingCheckBox.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="oilPaintingCheckBox.Text" xml:space="preserve">
    <value>油画</value>
  </data>
  <data name="&gt;&gt;oilPaintingCheckBox.Name" xml:space="preserve">
    <value>oilPaintingCheckBox</value>
  </data>
  <data name="&gt;&gt;oilPaintingCheckBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;oilPaintingCheckBox.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;oilPaintingCheckBox.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 74</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 20</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>高度</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 33</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>(px)</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label7.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 74</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>(px)</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label8.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 117</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 20</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>(%)</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 148</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>221, 125</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>边框</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 280</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>221, 154</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>效果</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="applyButton.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="applyButton.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="applyButton.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 451</value>
  </data>
  <data name="applyButton.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 46</value>
  </data>
  <data name="applyButton.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="applyButton.Text" xml:space="preserve">
    <value>应用</value>
  </data>
  <data name="&gt;&gt;applyButton.Name" xml:space="preserve">
    <value>applyButton</value>
  </data>
  <data name="&gt;&gt;applyButton.Type" xml:space="preserve">
    <value>LkControls.common.RoundButton, InduVision, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;applyButton.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;applyButton.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 11</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>265, 511</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>放大镜设置</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>293, 539</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>MagnifierToolSettingsPanel</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>