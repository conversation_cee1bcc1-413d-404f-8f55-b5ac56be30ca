﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace InduVision.Localization {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Strings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Strings() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("InduVision.Localization.Strings", typeof(Strings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 About... 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_ABOUT {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_ABOUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} has invalid format. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_ARG0_HAS_INVALID_FORMAT {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_ARG0_HAS_INVALID_FORMAT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Click on the panel if current color must be changed. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_CLICK_ON_THE_PANEL_IF_CURRENT_COLOR_MUST_BE_CHANGED {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_CLICK_ON_THE_PANEL_IF_CURRENT_COLOR_MUST_BE_CHANGE" +
                        "D", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Click the button if the current color must be changed. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_CLICK_THE_BUTTON_IF_THE_CURRENT_COLOR_MUST_BE_CHANGED {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_CLICK_THE_BUTTON_IF_THE_CURRENT_COLOR_MUST_BE_CHAN" +
                        "GED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Click the button if the current color must be set to the default color. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_CLICK_THE_BUTTON_IF_THE_CURRENT_COLOR_MUST_BE_SET_TO_THE_DEFAULT_COLOR {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_CLICK_THE_BUTTON_IF_THE_CURRENT_COLOR_MUST_BE_SET_" +
                        "TO_THE_DEFAULT_COLOR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Double click on the panel if the current color must be changed. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_DOUBLE_CLICK_ON_THE_PANEL_IF_THE_CURRENT_COLOR_MUST_BE_CHANGED {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_DOUBLE_CLICK_ON_THE_PANEL_IF_THE_CURRENT_COLOR_MUS" +
                        "T_BE_CHANGED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Find reached the starting point of the search. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_FIND_REACHED_THE_STARTING_POINT_OF_THE_SEARCH {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_FIND_REACHED_THE_STARTING_POINT_OF_THE_SEARCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 The specified text was not found:
        ///{0} 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_THE_SPECIFIED_TEXT_WAS_NOT_FOUNDRNARG0 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_THE_SPECIFIED_TEXT_WAS_NOT_FOUNDRNARG0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Tree View Search Control 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_TREE_VIEW_SEARCH_CONTROL {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_TREE_VIEW_SEARCH_CONTROL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Tree View Search Control 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_TREE_VIEW_SEARCH_CONTROL_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_TREE_VIEW_SEARCH_CONTROL_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Value Name 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_CUSTOMCONTROLS_VALUE_NAME {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_CUSTOMCONTROLS_VALUE_NAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_ERROR {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_ERROR_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_ERROR_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_ERROR_ALT2 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_ERROR_ALT2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_ERROR_ALT3 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_ERROR_ALT3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似  Save the measurement annotations to a file 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING__SAVE_THE_MEASUREMENT_ANNOTATIONS_TO_A_FILE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING__SAVE_THE_MEASUREMENT_ANNOTATIONS_TO_A_FILE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Add DICOM Data Element... 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ADD_DICOM_DATA_ELEMENT {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ADD_DICOM_DATA_ELEMENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Add DICOM Sequence Item 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ADD_DICOM_SEQUENCE_ITEM {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ADD_DICOM_SEQUENCE_ITEM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Add DicomAnnotatedViewerTool first. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ADD_DICOMANNOTATEDVIEWERTOOL_FIRST {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ADD_DICOMANNOTATEDVIEWERTOOL_FIRST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Angle Measure 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ANGLE_MEASURE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ANGLE_MEASURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Binary Annotations(*.vsabm)|*.vsabm|XMP Annotations(*.xmpm)|*.xmpm|All Formats(*.vsabm;*.xmpm)|*.vsabm;*.xmpm 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_BINARY_ANNOTATIONSVSABMVSABMXMP_ANNOTATIONSXMPMXMPMALL_FORMATSVSABMXMPMVSABMXMPM {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_BINARY_ANNOTATIONSVSABMVSABMXMP_ANNOTATIONSXMPMXMPMALL_FO" +
                        "RMATSVSABMXMPMVSABMXMPM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Binary Annotations|*.vsabm|XMP Annotations|*.xmpm 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_BINARY_ANNOTATIONSVSABMXMP_ANNOTATIONSXMPM {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_BINARY_ANNOTATIONSVSABMXMP_ANNOTATIONSXMPM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Can not find encoder for &apos;{0}&apos;. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_CAN_NOT_FIND_ENCODER_FOR_ARG0 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_CAN_NOT_FIND_ENCODER_FOR_ARG0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Can not find encoder for &apos;{0}&apos;. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_CAN_NOT_FIND_ENCODER_FOR_ARG0_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_CAN_NOT_FIND_ENCODER_FOR_ARG0_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Confirm overwrite 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_CONFIRM_OVERWRITE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_CONFIRM_OVERWRITE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Best Compression 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_BEST_COMPRESSION {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_BEST_COMPRESSION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Best Speed 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_BEST_SPEED {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_BEST_SPEED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Fast 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_FAST {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_FAST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Important: some data from annotations will be lost. Do you want to continue anyway? 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_IMPORTANT_SOME_DATA_FROM_ANNOTATIONS_WILL_BE_LOST_DO_YOU_WANT_TO_CONTINUE_ANYWAY {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_IMPORTANT_SOME_DATA_FROM_ANNOTATIONS_WILL_" +
                        "BE_LOST_DO_YOU_WANT_TO_CONTINUE_ANYWAY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Normal 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_NORMAL {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_NORMAL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Normal 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_NORMAL_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_NORMAL_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Normal 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_NORMAL_ALT2 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_NORMAL_ALT2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Tile height must be multiple 16. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_TILE_HEIGHT_MUST_BE_MULTIPLE_16 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_TILE_HEIGHT_MUST_BE_MULTIPLE_16", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Tile width must be multiple 16. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_TILE_WIDTH_MUST_BE_MULTIPLE_16 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_TILE_WIDTH_MUST_BE_MULTIPLE_16", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 value 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 value 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 value 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT2 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 value 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT3 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 value 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT4 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_VALUE_ALT4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Warning 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_WARNING {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_DIALOGS_WARNING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 File &apos;{0}&apos; already exists. Overwrite it? 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_FILE_ARG0_ALREADY_EXISTS_OVERWRITE_IT {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_FILE_ARG0_ALREADY_EXISTS_OVERWRITE_IT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Ndpi 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_CODECS_NDPI {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_CODECS_NDPI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {DecoderName} - decoder name
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_DECODERNAME_DECODER_NAMERN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_DECODERNAME_DECODER_NAMERN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DICOM Metadata Editor 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_DICOM_METADATA_EDITOR {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_DICOM_METADATA_EDITOR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DICOM Metadata Viewer 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_DICOM_METADATA_VIEWER {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_DICOM_METADATA_VIEWER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {DirectoryName} - directory name
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_DIRECTORYNAME_DIRECTORY_NAMERN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_DIRECTORYNAME_DIRECTORY_NAMERN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Ellipse Measure 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ELLIPSE_MEASURE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ELLIPSE_MEASURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Enter string values (value per line) 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ENTER_STRING_VALUES_VALUE_PER_LINE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ENTER_STRING_VALUES_VALUE_PER_LINE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ERROR {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ERROR_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ERROR_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ERROR_ALT2 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ERROR_ALT2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_ERROR_ALT3 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_ERROR_ALT3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Examples:
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_EXAMPLESRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_EXAMPLESRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 &apos;File {Filename}, page {PageNumber}&apos;
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_FILE_FILENAME_PAGE_PAGENUMBERRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_FILE_FILENAME_PAGE_PAGENUMBERRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {Filename} - filename without directory
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_FILENAME_FILENAME_WITHOUT_DIRECTORYRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_FILENAME_FILENAME_WITHOUT_DIRECTORYRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Focused 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_FOCUSED {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_FOCUSED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Focused Image Appearance Settings 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_FOCUSED_IMAGE_APPEARANCE_SETTINGS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_FOCUSED_IMAGE_APPEARANCE_SETTINGS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {FullFilename} - full filename
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_FULLFILENAME_FULL_FILENAMERN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_FULLFILENAME_FULL_FILENAMERN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Hovered 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_HOVERED {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_HOVERED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Image Measure Tool 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGE_MEASURE_TOOL {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGE_MEASURE_TOOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Image Measure Tool 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGE_MEASURE_TOOL_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGE_MEASURE_TOOL_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Image measure tool settings 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGE_MEASURE_TOOL_SETTINGS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGE_MEASURE_TOOL_SETTINGS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageBitsPerPixel} - source image bits per pixel 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGEBITSPERPIXEL_SOURCE_IMAGE_BITS_PER_PIXEL {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGEBITSPERPIXEL_SOURCE_IMAGE_BITS_PER_PIXEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageHeightPx} - source image height, in pixels
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGEHEIGHTPX_SOURCE_IMAGE_HEIGHT_IN_PIXELSRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGEHEIGHTPX_SOURCE_IMAGE_HEIGHT_IN_PIXELSRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageHRes} - source image horizontal resolution, in DPI
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGEHRES_SOURCE_IMAGE_HORIZONTAL_RESOLUTION_IN_DPIRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGEHRES_SOURCE_IMAGE_HORIZONTAL_RESOLUTION_IN_DPIRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageIndex} - image index, in image collection
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGEINDEX_IMAGE_INDEX_IN_IMAGE_COLLECTIONRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGEINDEX_IMAGE_INDEX_IN_IMAGE_COLLECTIONRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageNumber} - image number, in image collection
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGENUMBER_IMAGE_NUMBER_IN_IMAGE_COLLECTIONRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGENUMBER_IMAGE_NUMBER_IN_IMAGE_COLLECTIONRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageSizeMpx} - source image size, in megapixels
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGESIZEMPX_SOURCE_IMAGE_SIZE_IN_MEGAPIXELSRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGESIZEMPX_SOURCE_IMAGE_SIZE_IN_MEGAPIXELSRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageVRes} - source image vertical resolution, in DPI
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGEVRES_SOURCE_IMAGE_VERTICAL_RESOLUTION_IN_DPIRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGEVRES_SOURCE_IMAGE_VERTICAL_RESOLUTION_IN_DPIRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {ImageWidthPx} - source image width, in pixels
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_IMAGEWIDTHPX_SOURCE_IMAGE_WIDTH_IN_PIXELSRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_IMAGEWIDTHPX_SOURCE_IMAGE_WIDTH_IN_PIXELSRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Invalid DicomDateTime format. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_INVALID_DICOMDATETIME_FORMAT {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_INVALID_DICOMDATETIME_FORMAT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Line Measure 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_LINE_MEASURE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_LINE_MEASURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Lines Measure 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_LINES_MEASURE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_LINES_MEASURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 List of predefined format variables:
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_LIST_OF_PREDEFINED_FORMAT_VARIABLESRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_LIST_OF_PREDEFINED_FORMAT_VARIABLESRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Load Measurements... 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_LOAD_MEASUREMENTS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_LOAD_MEASUREMENTS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Load the measurement annotations from a file 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_LOAD_THE_MEASUREMENT_ANNOTATIONS_FROM_A_FILE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_LOAD_THE_MEASUREMENT_ANNOTATIONS_FROM_A_FILE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Measurement Properties... 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_MEASUREMENT_PROPERTIES {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_MEASUREMENT_PROPERTIES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Measurement settings 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_MEASUREMENT_SETTINGS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_MEASUREMENT_SETTINGS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Megabytes 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_MEGABYTES {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_MEGABYTES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Megapixels 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_MEGAPIXELS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_MEGAPIXELS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Normal 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_NORMAL {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_NORMAL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Not Focused Image Appearance Settings 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_NOT_FOCUSED_IMAGE_APPEARANCE_SETTINGS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_NOT_FOCUSED_IMAGE_APPEARANCE_SETTINGS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Not ready 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_NOT_READY {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_NOT_READY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {PageIndex} - page index, in source image file
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_PAGEINDEX_PAGE_INDEX_IN_SOURCE_IMAGE_FILERN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_PAGEINDEX_PAGE_INDEX_IN_SOURCE_IMAGE_FILERN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {PageLabel} - page label
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_PAGELABEL_PAGE_LABELRN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_PAGELABEL_PAGE_LABELRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {PageNumber} - page number, in source image file
        /// 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_PAGENUMBER_PAGE_NUMBER_IN_SOURCE_IMAGE_FILERN {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_PAGENUMBER_PAGE_NUMBER_IN_SOURCE_IMAGE_FILERN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Properties... 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_PROPERTIES {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_PROPERTIES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Refresh all measurements of focused image 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_REFRESH_ALL_MEASUREMENTS_OF_FOCUSED_IMAGE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_REFRESH_ALL_MEASUREMENTS_OF_FOCUSED_IMAGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Refresh Measurements 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_REFRESH_MEASUREMENTS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_REFRESH_MEASUREMENTS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Save Measurements... 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_SAVE_MEASUREMENTS {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_SAVE_MEASUREMENTS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Selected 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_SELECTED {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_SELECTED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Sequence 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_SEQUENCE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_SEQUENCE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Show properties form for focused measurement annotation 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_SHOW_PROPERTIES_FORM_FOR_FOCUSED_MEASUREMENT_ANNOTATION {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_SHOW_PROPERTIES_FORM_FOR_FOCUSED_MEASUREMENT_ANNOTATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Show properties form for image measure tool 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_SHOW_PROPERTIES_FORM_FOR_IMAGE_MEASURE_TOOL {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_SHOW_PROPERTIES_FORM_FOR_IMAGE_MEASURE_TOOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Starts the measurement using angle 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_ANGLE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_ANGLE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Starts the measurement using ellipse 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_ELLIPSE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_ELLIPSE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Starts the measurement using line 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_LINE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_LINE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Starts the measurement using lines 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_LINES {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_STARTS_THE_MEASUREMENT_USING_LINES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 This demo application can edit array only if array length does not exceed {0}. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_THIS_DEMO_APPLICATION_CAN_EDIT_ARRAY_ONLY_IF_ARRAY_LENGTH_DOES_NOT_EXCEED_ARG0 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_THIS_DEMO_APPLICATION_CAN_EDIT_ARRAY_ONLY_IF_ARRAY_LENGTH" +
                        "_DOES_NOT_EXCEED_ARG0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 This node is read-only. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_THIS_NODE_IS_READONLY {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_THIS_NODE_IS_READONLY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 ThumbnailCaption.CaptionFormat property 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_THUMBNAILCAPTIONCAPTIONFORMAT_PROPERTY {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_THUMBNAILCAPTIONCAPTIONFORMAT_PROPERTY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Units Of Measure 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_UNITS_OF_MEASURE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_UNITS_OF_MEASURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Units Of Measure 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_UNITS_OF_MEASURE_ALT1 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_UNITS_OF_MEASURE_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Value 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_VALUE {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_VALUE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Visual tool 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_IMAGING_VISUAL_TOOL {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_IMAGING_VISUAL_TOOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Information 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_INFORMATION {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_INFORMATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Inner exception: {0} 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_INNER_EXCEPTION_ARG0 {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_INNER_EXCEPTION_ARG0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Resource &apos;{0}&apos; was not found. 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_RESOURCE_ARG0_WAS_NOT_FOUND {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_RESOURCE_ARG0_WAS_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Warning 的本地化字符串。
        /// </summary>
        internal static string DEMOSCOMMONCODE_WARNING {
            get {
                return ResourceManager.GetString("DEMOSCOMMONCODE_WARNING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Annotation Properties 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_ANNOTATION_PROPERTIES {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_ANNOTATION_PROPERTIES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Annotations 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_ANNOTATIONS {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_ANNOTATIONS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Annotations 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_ANNOTATIONS_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_ANNOTATIONS_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Binary Annotations(*.vsab)|*.vsab 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_BINARY_ANNOTATIONSVSABVSAB {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_BINARY_ANNOTATIONSVSABVSAB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Binary Annotations(*.vsab)|*.vsab 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_BINARY_ANNOTATIONSVSABVSAB_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_BINARY_ANNOTATIONSVSABVSAB_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Building annotation 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_BUILDING_ANNOTATION {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_BUILDING_ANNOTATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Burn annotations on images?
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_BURN_ANNOTATIONS_ON_IMAGESRN {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_BURN_ANNOTATIONS_ON_IMAGESRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Burn annotations on images?
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_BURN_ANNOTATIONS_ON_IMAGESRN_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_BURN_ANNOTATIONS_ON_IMAGESRN_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Calculated {0} 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CALCULATED_ARG0 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CALCULATED_ARG0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 125   Width 250 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_125_WIDTH_250 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_125_WIDTH_250", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 1280 Width 2560 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_1280_WIDTH_2560 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_1280_WIDTH_2560", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 15000 Width 30000 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_15000_WIDTH_30000 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_15000_WIDTH_30000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 1875  Width 3750 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_1875_WIDTH_3750 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_1875_WIDTH_3750", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 20   Width 40 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_20_WIDTH_40 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_20_WIDTH_40", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 2560 Width 5120 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_2560_WIDTH_5120 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_2560_WIDTH_5120", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 30    Width 60 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_30_WIDTH_60 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_30_WIDTH_60", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 30000 Width 60000 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_30000_WIDTH_60000 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_30000_WIDTH_60000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 3750  Width 7500 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_3750_WIDTH_7500 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_3750_WIDTH_7500", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 40   Width 80 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_40_WIDTH_80 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_40_WIDTH_80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 500   Width 1000 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_500_WIDTH_1000 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_500_WIDTH_1000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 600  Width 1280 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_600_WIDTH_1280 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_600_WIDTH_1280", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 7500  Width 15000 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_7500_WIDTH_15000 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_7500_WIDTH_15000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Center 80   Width 160 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CENTER_80_WIDTH_160 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CENTER_80_WIDTH_160", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Copy 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_COPY {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_COPY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Current image is not DICOM frame. 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CURRENT_IMAGE_IS_NOT_DICOM_FRAME {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CURRENT_IMAGE_IS_NOT_DICOM_FRAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Custom VOI LUT... 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CUSTOM_VOI_LUT {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CUSTOM_VOI_LUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Cut 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_CUT {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_CUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Default VOI LUT 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_DEFAULT_VOI_LUT {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_DEFAULT_VOI_LUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Delete 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_DELETE {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_DELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Delete All 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_DELETE_ALL {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_DELETE_ALL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DICOM annotations cannot be converted into Vintasoft annotations but annotations can be burned on image.
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_DICOM_ANNOTATIONS_CANNOT_BE_CONVERTED_INTO_VINTASOFT_ANNOTATIONS_BUT_ANNOTATIONS_CAN_BE_BURNED_ON_IMAGERN {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_DICOM_ANNOTATIONS_CANNOT_BE_CONVERTED_INTO_VINTASOFT_ANNOTATIONS_" +
                        "BUT_ANNOTATIONS_CAN_BE_BURNED_ON_IMAGERN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DICOM annotations cannot be converted into Vintasoft annotations but annotations can be burned on image.
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_DICOM_ANNOTATIONS_CANNOT_BE_CONVERTED_INTO_VINTASOFT_ANNOTATIONS_BUT_ANNOTATIONS_CAN_BE_BURNED_ON_IMAGERN_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_DICOM_ANNOTATIONS_CANNOT_BE_CONVERTED_INTO_VINTASOFT_ANNOTATIONS_" +
                        "BUT_ANNOTATIONS_CAN_BE_BURNED_ON_IMAGERN_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DICOM files|*.dcm 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_DICOM_FILESDCM {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_DICOM_FILESDCM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DICOM images can can be saved to the source file (if source file is focused in viewer) or to a new file. 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_DICOM_IMAGES_CAN_CAN_BE_SAVED_TO_THE_SOURCE_FILE_IF_SOURCE_FILE_IS_FOCUSED_IN_VIEWER_OR_TO_A_NEW_FILE {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_DICOM_IMAGES_CAN_CAN_BE_SAVED_TO_THE_SOURCE_FILE_IF_SOURCE_FILE_I" +
                        "S_FOCUSED_IN_VIEWER_OR_TO_A_NEW_FILE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Error 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_ERROR {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Magnifier 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_MAGNIFIER {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_MAGNIFIER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Magnifier Tool 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_MAGNIFIER_TOOL {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_MAGNIFIER_TOOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Mouse Wheel 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_MOUSE_WHEEL {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_MOUSE_WHEEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Overlay images are damaged. 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_OVERLAY_IMAGES_ARE_DAMAGED {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_OVERLAY_IMAGES_ARE_DAMAGED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 OverlayImage: {0} 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_OVERLAYIMAGE_ARG0 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_OVERLAYIMAGE_ARG0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Page  的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PAGE {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PAGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Paste 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PASTE {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PASTE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Presentation state file is saved. 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESENTATION_STATE_FILE_IS_SAVED {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESENTATION_STATE_FILE_IS_SAVED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Presentation State File(*.pre)|*.pre 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESENTATION_STATE_FILEPREPRE {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESENTATION_STATE_FILEPREPRE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Presentation State File(*.pre)|*.pre|All Formats(*.*)|*.* 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESENTATION_STATE_FILEPREPREALL_FORMATS {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESENTATION_STATE_FILEPREPREALL_FORMATS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Press &apos;Cancel&apos; to cancel saving. 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESS_CANCEL_TO_CANCEL_SAVING {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESS_CANCEL_TO_CANCEL_SAVING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Press &apos;Cancel&apos; to cancel saving. 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESS_CANCEL_TO_CANCEL_SAVING_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESS_CANCEL_TO_CANCEL_SAVING_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Press &apos;No&apos; if you want save images without annotations.
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESS_NO_IF_YOU_WANT_SAVE_IMAGES_WITHOUT_ANNOTATIONSRN {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESS_NO_IF_YOU_WANT_SAVE_IMAGES_WITHOUT_ANNOTATIONSRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Press &apos;No&apos; if you want save images without annotations.
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESS_NO_IF_YOU_WANT_SAVE_IMAGES_WITHOUT_ANNOTATIONSRN_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESS_NO_IF_YOU_WANT_SAVE_IMAGES_WITHOUT_ANNOTATIONSRN_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Press &apos;Yes&apos; if you want save images with burned annotations.
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESS_YES_IF_YOU_WANT_SAVE_IMAGES_WITH_BURNED_ANNOTATIONSRN {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESS_YES_IF_YOU_WANT_SAVE_IMAGES_WITH_BURNED_ANNOTATIONSRN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Press &apos;Yes&apos; if you want save images with burned annotations.
        /// 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_PRESS_YES_IF_YOU_WANT_SAVE_IMAGES_WITH_BURNED_ANNOTATIONSRN_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_PRESS_YES_IF_YOU_WANT_SAVE_IMAGES_WITH_BURNED_ANNOTATIONSRN_ALT1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Size={0}x{1}; PixelFormat={2}; Resolution={3} 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_SIZEARG0XARG1_PIXELFORMATARG2_RESOLUTIONARG3 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_SIZEARG0XARG1_PIXELFORMATARG2_RESOLUTIONARG3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TIFF Files|*.tif;*.tiff|JPEG Files|*.jpg;*.jpeg|PNG Files|.png 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_TIFF_FILESTIFTIFFJPEG_FILESJPGJPEGPNG_FILESPNG {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_TIFF_FILESTIFTIFFJPEG_FILESJPGJPEGPNG_FILESPNG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 XMP Annotations(*.xmp)|*.xmp 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_XMP_ANNOTATIONSXMPXMP {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_XMP_ANNOTATIONSXMPXMP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 XMP Annotations(*.xmp)|*.xmp 的本地化字符串。
        /// </summary>
        internal static string DICOMVIEWERDEMO_XMP_ANNOTATIONSXMPXMP_ALT1 {
            get {
                return ResourceManager.GetString("DICOMVIEWERDEMO_XMP_ANNOTATIONSXMPXMP_ALT1", resourceCulture);
            }
        }
    }
}
