<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
    <GenerateAssemblyVersionAttribute>false</GenerateAssemblyVersionAttribute>  
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyDescriptionAttribute>false</GenerateAssemblyDescriptionAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <GenerateAssemblyTitleAttribute>false</GenerateAssemblyTitleAttribute>    
    <EnableDefaultItems>false</EnableDefaultItems>
    <Version>********</Version>
    <AssemblyName>InduVision</AssemblyName>
    <RootNamespace>InduVision</RootNamespace>
    <StartupObject>InduVision.Program</StartupObject>
    <ApplicationIcon>App.ico</ApplicationIcon>
    <Platforms>AnyCPU;x86</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>NETCORE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <DefineConstants>NETCORE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>NETCORE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <DefineConstants>NETCORE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <AdditionalFiles Remove="app.manifest" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="fo-dicom" Version="5.2.2" />
    <PackageReference Include="fo-dicom.Imaging.ImageSharp" Version="5.2.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.4.0" />
    <PackageReference Include="OpenCvSharp4" Version="4.11.0.20250507" />
    <PackageReference Include="OpenCvSharp4.Extensions" Version="4.10.0.20241108" />
    <PackageReference Include="OpenCvSharp4.runtime.win" Version="4.11.0.20250507" />
    <PackageReference Include="OpenCvSharp4.Windows" Version="4.10.0.20241108" />
    <PackageReference Include="OxyPlot.WindowsForms" Version="2.2.0" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
    <PackageReference Include="Svg" Version="3.4.7" />
    <PackageReference Include="System.Management" Version="9.0.2" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomControls\AnchorTypeEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\AnchorTypeEditorControl.designer.cs">
      <DependentUpon>AnchorTypeEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\CheckedToolStripSplitButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorPanelControl\ColorPanelControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorPanelControl\ColorPanelControl.designer.cs">
      <DependentUpon>ColorPanelControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\ColorPickerControl\ColorPickerControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorPickerControl\ColorPickerControl.designer.cs">
      <DependentUpon>ColorPickerControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\ColorSampleControl\ColorSampleControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorSampleControl\ColorSampleControl.designer.cs">
      <DependentUpon>ColorSampleControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\PaddingFEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\PaddingFEditorControl.designer.cs">
      <DependentUpon>PaddingFEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\TreeViewSearchControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\TreeViewSearchControl.designer.cs">
      <DependentUpon>TreeViewSearchControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\ValueEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ValueEditorControl.designer.cs">
      <DependentUpon>ValueEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\AssembliesLoader\Jpeg2000AssemblyLoader.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\CodecsFileFilters.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.designer.cs">
      <DependentUpon>BmpEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.designer.cs">
      <DependentUpon>GifEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.designer.cs">
      <DependentUpon>JpegEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.designer.cs">
      <DependentUpon>PbmEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.designer.cs">
      <DependentUpon>PngEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.designer.cs">
      <DependentUpon>SvgEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.designer.cs">
      <DependentUpon>TgaEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.designer.cs">
      <DependentUpon>TiffEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.designer.cs">
      <DependentUpon>WebpEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\ImagingEncoderFactory.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.designer.cs">
      <DependentUpon>Jpeg2000EncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ColorPickerDialogForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ColorPickerDialogForm.designer.cs">
      <DependentUpon>ColorPickerDialogForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.Designer.cs">
      <DependentUpon>ImageViewerToolStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewerToolStrip\PageIndexChangedEventArgs.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.designer.cs">
      <DependentUpon>ImageRenderingRequirementAddForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.designer.cs">
      <DependentUpon>ImageRenderingRequirementsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.designer.cs">
      <DependentUpon>ImageViewerSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.designer.cs">
      <DependentUpon>AddDicomDataElementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomDataElementMetadataConverter.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.designer.cs">
      <DependentUpon>DicomMetadataEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.designer.cs">
      <DependentUpon>DicomMetadataEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataTreeView.designer.cs">
      <DependentUpon>DicomMetadataTreeView.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\MetadataTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\MetadataTreeView.designer.cs">
      <DependentUpon>MetadataTreeView.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\PropertyGridForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\PropertyGridForm.designer.cs">
      <DependentUpon>PropertyGridForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\RenderingSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\RenderingSettingsForm.designer.cs">
      <DependentUpon>RenderingSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Resources\DemosResourcesManager.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\SaveImageFileForm.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.designer.cs">
      <DependentUpon>ThumbnailAppearanceSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.designer.cs">
      <DependentUpon>ThumbnailViewerSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\TypeEditorRegistrator\AnnotationTypeEditorRegistrator.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Actions\NoneAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Actions\SeparatorToolStripAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Actions\VisualToolAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualToolsToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualToolsToolStrip.designer.cs">
      <DependentUpon>VisualToolsToolStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\Actions\ImageMeasureToolAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\Actions\ImageMeasureToolUnitsOfMeasureAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\MeasurementVisualToolActionFactory.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\ZoomVisualTools\Actions\MagnifierToolAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.designer.cs">
      <DependentUpon>MagnifierToolSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode\AboutBoxBaseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode\AboutBoxBaseForm.designer.cs">
      <DependentUpon>AboutBoxBaseForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode\DemosTools.cs">
    </Compile>
    <Compile Include="AnnotationsToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\DicomAnnotatedViewerToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Dialogs\AboutBoxForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\AboutBoxForm.Designer.cs">
      <DependentUpon>AboutBoxForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\AnnotationsInfoForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\AnnotationsInfoForm.Designer.cs">
      <DependentUpon>AnnotationsInfoForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\DicomOverlaySettingEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\DicomOverlaySettingEditorForm.Designer.cs">
      <DependentUpon>DicomOverlaySettingEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\OverlayImagesViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\OverlayImagesViewer.Designer.cs">
      <DependentUpon>OverlayImagesViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\PresentationStateInfoForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\PresentationStateInfoForm.Designer.cs">
      <DependentUpon>PresentationStateInfoForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\VoiLutParamsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\VoiLutParamsForm.Designer.cs">
      <DependentUpon>VoiLutParamsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\DicomViewerToolInteractionButtonToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\DicomViewerToolInteractionButtonToolStrip.Designer.cs">
      <DependentUpon>DicomViewerToolInteractionButtonToolStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Initialization.cs" />
    <Compile Include="IRayLibrary\Common\Detector.cs" />
    <Compile Include="IRayLibrary\Common\DisplayProgressbar.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayAttrDef.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayAuthDef.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayCmdDef.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayDetFinder.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayEnumDef.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayErrDef.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayEventDef.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayFpdSys.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayFpdSysEx.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayImage.cs" />
    <Compile Include="IRayLibrary\Common\iRayInterface\IRayVariant.cs" />
    <Compile Include="LicenseRegistration\AboutLicenseForm.cs" />
    <Compile Include="LicenseRegistration\LicenseCheckService.cs" />
    <Compile Include="LicenseRegistration\LicenseManager.cs" />
    <Compile Include="LicenseRegistration\LicenseRegisterForm.cs" />
    <Compile Include="LicenseRegistration\LicenseRegisterForm.Designer.cs" />
    <Compile Include="LkControls\CommonControls\CustomTabControl.cs" />
    <Compile Include="LkControls\CommonControls\DuplexIQIAnnotationData.cs" />
    <Compile Include="LkControls\CommonControls\DuplexIQIForm.cs" />
    <Compile Include="LkControls\CommonControls\DuplexIQIForm.Designer.cs" />
    <Compile Include="LkControls\CommonControls\DynamicImageViewer.cs" />
    <Compile Include="LkControls\CommonControls\NsnrAnnotationData.cs" />
    <Compile Include="LkControls\CommonControls\RangeForm.cs" />
    <Compile Include="LkControls\CommonControls\RangeForm.Designer.cs" />
    <Compile Include="LkControls\CommonControls\RegionAnnotationData.cs" />
    <Compile Include="LkControls\CommonControls\ScalingAnnotationData.cs" />
    <Compile Include="LkControls\CommonControls\WallAnnotationData.cs" />
    <Compile Include="LkControls\FPD\IFPDPanel.cs" />
    <Compile Include="LkControls\FPD\IRay\ConnectDevice.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="LkControls\FPD\IRay\ConnectDevice.Designer.cs" />
    <Compile Include="LkControls\FPD\IRay\DynamicFPD.cs" />
    <Compile Include="LkControls\FPD\IRay\DynamicOperatePanel .cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="LkControls\FPD\IRay\DynamicOperatePanel .Designer.cs" />
    <Compile Include="LkControls\FPD\IRay\FPDPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="LkControls\FPD\IRay\FPDPanel.Designer.cs" />
    <Compile Include="LkControls\FPD\IRay\StaticFPD.cs" />
    <Compile Include="LkControls\FPD\IRay\StaticOperatePanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="LkControls\FPD\IRay\StaticOperatePanel.Designer.cs" />
    <Compile Include="LkControls\FPD\Nice\DynamicFPD.cs" />
    <Compile Include="LkControls\FPD\Nice\DynamicOperatePanel .cs" />
    <Compile Include="LkControls\FPD\Nice\DynamicOperatePanel .Designer.cs" />
    <Compile Include="LkControls\SetPanel\DuplexIQIPanel.cs" />
    <Compile Include="LkControls\SetPanel\DuplexIQIPanel.Designer.cs" />
    <Compile Include="LkControls\SetPanel\EnhancePanel.cs" />
    <Compile Include="LkControls\SetPanel\EnhancePanel.Designer.cs" />
    <Compile Include="LkControls\FPD\Nice\ConnectDevice.cs" />
    <Compile Include="LkControls\FPD\Nice\ConnectDevice.Designer.cs" />
    <Compile Include="LkControls\FPD\Nice\StaticFPD.cs" />
    <Compile Include="LkControls\FPD\Nice\StaticOperatePanel.cs" />
    <Compile Include="LkControls\FPD\Nice\StaticOperatePanel.Designer.cs" />
    <Compile Include="LkControls\LayoutControls\LeftPanel.cs" />
    <Compile Include="LkControls\LayoutControls\LeftPanel.Designer.cs" />
    <Compile Include="LkControls\SetPanel\ExportImagePanel.cs" />
    <Compile Include="LkControls\SetPanel\ExportImagePanel.Designer.cs" />
    <Compile Include="LkControls\SetPanel\MagnifierToolSettingsPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="LkControls\SetPanel\MagnifierToolSettingsPanel.designer.cs">
      <DependentUpon>MagnifierToolSettingsPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="LkControls\SetPanel\WwwlPanel.cs" />
    <Compile Include="LkControls\SetPanel\WwwlPanel.Designer.cs" />
    <Compile Include="LkControls\Utils\DeviceInfo.cs" />
    <Compile Include="LkControls\Utils\Histogram.cs" />
    <Compile Include="LkControls\Utils\MatProcesser.cs" />
    <Compile Include="LkControls\CommonControls\RenameDialog.cs" />
    <Compile Include="LkControls\CommonControls\RenameDialog.Designer.cs" />
    <Compile Include="LkControls\CommonControls\RoundBorderPanel.cs" />
    <Compile Include="LkControls\CommonControls\RoundButton.cs" />
    <Compile Include="LkControls\CommonControls\RoundPanel.cs" />
    <Compile Include="LkControls\FPD\Nice\FPDPanel.cs" />
    <Compile Include="LkControls\FPD\Nice\FPDPanel.Designer.cs" />
    <Compile Include="LkControls\LayoutControls\ThumbnailViewer.cs" />
    <Compile Include="LkControls\LayoutControls\ToolsPanel.cs" />
    <Compile Include="LkControls\LayoutControls\ToolsPanel.Designer.cs" />
    <Compile Include="LkControls\LayoutControls\TopPanel.cs" />
    <Compile Include="LkControls\LayoutControls\TopPanel.Designer.cs" />
    <Compile Include="LkControls\Utils\Utils.cs" />
    <Compile Include="Localization\Strings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Strings.resx</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PresentationStateFileController.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Welcome.cs" />
    <Compile Include="Welcome.Designer.cs" />
    <EmbeddedResource Include="CustomControls\AnchorTypeEditorControl.resx">
      <DependentUpon>AnchorTypeEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ColorPickerControl\ColorPickerControl.de.resx">
      <DependentUpon>ColorPickerControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ColorPickerControl\ColorPickerControl.resx">
      <DependentUpon>ColorPickerControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ColorSampleControl\ColorSampleControl.resx">
      <DependentUpon>ColorSampleControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\TreeViewSearchControl.de.resx">
      <DependentUpon>TreeViewSearchControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\TreeViewSearchControl.resx">
      <DependentUpon>TreeViewSearchControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ValueEditorControl.de.resx">
      <DependentUpon>ValueEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.de.resx">
      <DependentUpon>BmpEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.de.resx">
      <DependentUpon>GifEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.de.resx">
      <DependentUpon>JpegEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.de.resx">
      <DependentUpon>PbmEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.resx">
      <DependentUpon>PbmEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.de.resx">
      <DependentUpon>PngEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.de.resx">
      <DependentUpon>SvgEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.resx">
      <DependentUpon>SvgEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.de.resx">
      <DependentUpon>TgaEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.resx">
      <DependentUpon>TgaEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.de.resx">
      <DependentUpon>TiffEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.de.resx">
      <DependentUpon>WebpEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.resx">
      <DependentUpon>WebpEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.de.resx">
      <DependentUpon>Jpeg2000EncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ColorPickerDialogForm.de.resx">
      <DependentUpon>ColorPickerDialogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ColorPickerDialogForm.resx">
      <DependentUpon>ColorPickerDialogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.de.resx">
      <DependentUpon>ImageViewerToolStrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.resx">
      <DependentUpon>ImageViewerToolStrip.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.de.resx">
      <DependentUpon>ImageRenderingRequirementAddForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.de.resx">
      <DependentUpon>ImageRenderingRequirementsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.de.resx">
      <DependentUpon>ImageViewerSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.de.resx">
      <DependentUpon>AddDicomDataElementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.de.resx">
      <DependentUpon>DicomMetadataEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.resx">
      <DependentUpon>DicomMetadataEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.de.resx">
      <DependentUpon>DicomMetadataEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.resx">
      <DependentUpon>DicomMetadataEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\PropertyGridForm.de.resx">
      <DependentUpon>PropertyGridForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\RenderingSettingsForm.de.resx">
      <DependentUpon>RenderingSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.de.resx">
      <DependentUpon>ThumbnailAppearanceSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.de.resx">
      <DependentUpon>ThumbnailViewerSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.de.resx">
      <DependentUpon>MagnifierToolSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.resx">
      <DependentUpon>MagnifierToolSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode\AboutBoxBaseForm.de.resx">
      <DependentUpon>AboutBoxBaseForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AboutBoxForm.de.resx">
      <DependentUpon>AboutBoxForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AboutBoxForm.resx">
      <DependentUpon>AboutBoxForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AnnotationsInfoForm.de.resx">
      <DependentUpon>AnnotationsInfoForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\DicomOverlaySettingEditorForm.de.resx">
      <DependentUpon>DicomOverlaySettingEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\DicomOverlaySettingEditorForm.resx">
      <DependentUpon>DicomOverlaySettingEditorForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\OverlayImagesViewer.de.resx">
      <DependentUpon>OverlayImagesViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\OverlayImagesViewer.resx">
      <DependentUpon>OverlayImagesViewer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\PresentationStateInfoForm.de.resx">
      <DependentUpon>PresentationStateInfoForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\VoiLutParamsForm.de.resx">
      <DependentUpon>VoiLutParamsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\VoiLutParamsForm.resx">
      <DependentUpon>VoiLutParamsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="LicenseRegistration\AboutLicenseForm.resx" />
    <EmbeddedResource Include="LicenseRegistration\LicenseRegisterForm.resx" />
    <EmbeddedResource Include="LkControls\CommonControls\DuplexIQIForm.resx" />
    <EmbeddedResource Include="LkControls\CommonControls\RangeForm.resx" />
    <EmbeddedResource Include="LkControls\FPD\IFPDPanel.resx" />
    <EmbeddedResource Include="LkControls\FPD\IRay\ConnectDevice.resx" />
    <EmbeddedResource Include="LkControls\FPD\IRay\DynamicOperatePanel .resx" />
    <EmbeddedResource Include="LkControls\FPD\IRay\FPDPanel.resx" />
    <EmbeddedResource Include="LkControls\FPD\IRay\StaticOperatePanel.resx" />
    <EmbeddedResource Include="LkControls\FPD\Nice\DynamicOperatePanel .resx" />
    <EmbeddedResource Include="LkControls\SetPanel\DuplexIQIPanel.resx" />
    <EmbeddedResource Include="LkControls\SetPanel\EnhancePanel.resx" />
    <EmbeddedResource Include="LkControls\FPD\Nice\ConnectDevice.resx" />
    <EmbeddedResource Include="LkControls\FPD\Nice\StaticOperatePanel.resx" />
    <EmbeddedResource Include="LkControls\LayoutControls\LeftPanel.resx" />
    <EmbeddedResource Include="LkControls\SetPanel\ExportImagePanel.resx" />
    <EmbeddedResource Include="LkControls\SetPanel\MagnifierToolSettingsPanel.resx" />
    <EmbeddedResource Include="LkControls\FPD\Nice\FPDPanel.resx" />
    <EmbeddedResource Include="LkControls\CommonControls\RenameDialog.resx" />
    <EmbeddedResource Include="LkControls\LayoutControls\ToolsPanel.resx" />
    <EmbeddedResource Include="LkControls\LayoutControls\TopPanel.resx" />
    <EmbeddedResource Include="LkControls\SetPanel\WwwlPanel.resx" />
    <EmbeddedResource Include="Localization\Strings.de.resx" />
    <EmbeddedResource Include="Localization\Strings.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.de.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\ZoomVisualTools\Resources\MagnifierTool.png">
    </EmbeddedResource>
    <Content Include="CustomControls\ColorSampleControl\ColorSampleBackground.png">
    </Content>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\ZoomVisualTools\Resources\PanTool.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\MouseWheel.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\camera.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_beginning.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_end.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_left.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_right.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\open.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\printer.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\save.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\scanner.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\zoom_in.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\zoom_out.png">
    </EmbeddedResource>
    <Content Include="directives.txt" />
    <EmbeddedResource Include="CustomControls\ColorPanelControl\ColorPanelControl.resx">
      <DependentUpon>ColorPanelControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\PaddingFEditorControl.resx">
      <DependentUpon>PaddingFEditorControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ValueEditorControl.resx">
      <DependentUpon>ValueEditorControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.resx">
      <DependentUpon>BmpEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.resx">
      <DependentUpon>GifEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.resx">
      <DependentUpon>JpegEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.resx">
      <DependentUpon>PngEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.resx">
      <DependentUpon>TiffEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.resx">
      <DependentUpon>Jpeg2000EncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.resx">
      <DependentUpon>ImageRenderingRequirementAddForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.resx">
      <DependentUpon>ImageRenderingRequirementsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.resx">
      <DependentUpon>ImageViewerSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.resx">
      <DependentUpon>AddDicomDataElementForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\PropertyGridForm.resx">
      <DependentUpon>PropertyGridForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\RenderingSettingsForm.resx">
      <DependentUpon>RenderingSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.resx">
      <DependentUpon>ThumbnailAppearanceSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.resx">
      <DependentUpon>ThumbnailViewerSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Resources\None.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\Resources\ImageMeasureTool.png">
    </EmbeddedResource>
    <Content Include="App.ico" />
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualToolsToolStrip.resx">
      <DependentUpon>VisualToolsToolStrip.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode\AboutBoxBaseForm.resx">
      <DependentUpon>AboutBoxBaseForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="AnnotationsToolStrip.resx">
      <DependentUpon>AnnotationsToolStrip.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AnnotationsInfoForm.resx">
      <DependentUpon>AnnotationsInfoForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\PresentationStateInfoForm.resx">
      <DependentUpon>PresentationStateInfoForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Icons\Arrow.png" />
    <EmbeddedResource Include="Icons\Ellipse.png" />
    <EmbeddedResource Include="Icons\Interpolated.png" />
    <EmbeddedResource Include="Icons\Multiline.png" />
    <EmbeddedResource Include="Icons\Point.png" />
    <EmbeddedResource Include="Icons\Polyline.png" />
    <EmbeddedResource Include="Icons\Rangeline.png" />
    <EmbeddedResource Include="Icons\Rectangle.png" />
    <EmbeddedResource Include="Icons\Text.png" />
    <EmbeddedResource Include="Icons\Circle.png" />
    <EmbeddedResource Include="Icons\Axis.png" />
    <EmbeddedResource Include="Icons\Ruler.png" />
    <EmbeddedResource Include="Icons\Crosshair.png" />
    <EmbeddedResource Include="Icons\Cutline.png" />
    <EmbeddedResource Include="Icons\Infiniteline.png" />
    <EmbeddedResource Include="Resources\VOI LUT.png" />
    <EmbeddedResource Include="Welcome.resx" />
  </ItemGroup>
  
  <ItemGroup>
  <Content Include="Icons\**\*.svg">
    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
  </Content>
</ItemGroup>

  <ItemGroup>
    <None Include="Config\InduVisionV222.nrproj" />
    <None Include="Config\V222Key.mkey" />
    <None Include="LicenseGenerator.py" />
    <Content Include="NLog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="License">
      <HintPath>libs\License.dll</HintPath>
    </Reference>
    <Reference Include="NHunspell">
      <HintPath>libs\NHunspell.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging">
      <HintPath>libs\Vintasoft.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation">
      <HintPath>libs\Vintasoft.Imaging.Annotation.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.AspNetCore.ApiControllers">
      <HintPath>libs\Vintasoft.Imaging.Annotation.AspNetCore.ApiControllers.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Dicom">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Dicom.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Dicom.UI">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Dicom.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Dicom.Wpf.UI">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Dicom.Wpf.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Office">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Office.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Office.UI">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Office.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Office.Wpf.UI">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Office.Wpf.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.UI">
      <HintPath>libs\Vintasoft.Imaging.Annotation.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Web.Services">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Web.Services.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Wpf.UI">
      <HintPath>libs\Vintasoft.Imaging.Annotation.Wpf.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.AspNetCore.ApiControllers">
      <HintPath>libs\Vintasoft.Imaging.AspNetCore.ApiControllers.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Dicom">
      <HintPath>libs\Vintasoft.Imaging.Dicom.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Dicom.AspNetCore.ApiControllers">
      <HintPath>libs\Vintasoft.Imaging.Dicom.AspNetCore.ApiControllers.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Dicom.UI">
      <HintPath>libs\Vintasoft.Imaging.Dicom.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Dicom.Web.Services">
      <HintPath>libs\Vintasoft.Imaging.Dicom.Web.Services.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Dicom.Wpf.UI">
      <HintPath>libs\Vintasoft.Imaging.Dicom.Wpf.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Drawing.SkiaSharp">
      <HintPath>libs\Vintasoft.Imaging.Drawing.SkiaSharp.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Gdi">
      <HintPath>libs\Vintasoft.Imaging.Gdi.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Jpeg2000Codec">
      <HintPath>libs\Vintasoft.Imaging.Jpeg2000Codec.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Media">
      <HintPath>libs\Vintasoft.Imaging.Media.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Office.AspNetCore.ApiControllers">
      <HintPath>libs\Vintasoft.Imaging.Office.AspNetCore.ApiControllers.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Office.OpenXml">
      <HintPath>libs\Vintasoft.Imaging.Office.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Office.OpenXml.UI">
      <HintPath>libs\Vintasoft.Imaging.Office.OpenXml.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Office.OpenXml.Wpf.UI">
      <HintPath>libs\Vintasoft.Imaging.Office.OpenXml.Wpf.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Office.Web.Services">
      <HintPath>libs\Vintasoft.Imaging.Office.Web.Services.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.RawCodec">
      <HintPath>libs\Vintasoft.Imaging.RawCodec.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.UI">
      <HintPath>libs\Vintasoft.Imaging.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Web.Services">
      <HintPath>libs\Vintasoft.Imaging.Web.Services.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.WebpCodec">
      <HintPath>libs\Vintasoft.Imaging.WebpCodec.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Wpf">
      <HintPath>libs\Vintasoft.Imaging.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Wpf.UI">
      <HintPath>libs\Vintasoft.Imaging.Wpf.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.WsiCodec">
      <HintPath>libs\Vintasoft.Imaging.WsiCodec.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Shared">
      <HintPath>libs\Vintasoft.Shared.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Shared.Web">
      <HintPath>libs\Vintasoft.Shared.Web.dll</HintPath>
    </Reference>
  </ItemGroup>
  
</Project>