﻿using LkControls.common;

namespace InduVision.LkControls.FPD.Nice
{
    partial class StaticOperatePanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            namePanel = new RoundBorderPanel();
            nameTextBox = new System.Windows.Forms.TextBox();
            panel3 = new System.Windows.Forms.Panel();
            label3 = new System.Windows.Forms.Label();
            saveButton = new RoundButton();
            captureButton = new RoundButton();
            openButton = new RoundButton();
            disconnectButton = new RoundButton();
            flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            flowLayoutPanel2 = new System.Windows.Forms.FlowLayoutPanel();
            roundBorderPanel4 = new RoundBorderPanel();
            captureMode = new System.Windows.Forms.ComboBox();
            panel5 = new System.Windows.Forms.Panel();
            label5 = new System.Windows.Forms.Label();
            roundBorderPanel5 = new RoundBorderPanel();
            captureTime = new System.Windows.Forms.NumericUpDown();
            panel6 = new System.Windows.Forms.Panel();
            label6 = new System.Windows.Forms.Label();
            roundBorderPanel1 = new RoundBorderPanel();
            isOverlay = new System.Windows.Forms.CheckBox();
            panel1 = new System.Windows.Forms.Panel();
            label1 = new System.Windows.Forms.Label();
            roundBorderPanel3 = new RoundBorderPanel();
            overlayMode = new System.Windows.Forms.ComboBox();
            panel4 = new System.Windows.Forms.Panel();
            label4 = new System.Windows.Forms.Label();
            roundBorderPanel2 = new RoundBorderPanel();
            overlayNumber = new System.Windows.Forms.NumericUpDown();
            panel2 = new System.Windows.Forms.Panel();
            label2 = new System.Windows.Forms.Label();
            roundBorderPanel6 = new RoundBorderPanel();
            onlyOverlay = new System.Windows.Forms.CheckBox();
            panel7 = new System.Windows.Forms.Panel();
            label7 = new System.Windows.Forms.Label();
            namePanel.SuspendLayout();
            flowLayoutPanel1.SuspendLayout();
            flowLayoutPanel2.SuspendLayout();
            roundBorderPanel4.SuspendLayout();
            roundBorderPanel5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)captureTime).BeginInit();
            roundBorderPanel1.SuspendLayout();
            roundBorderPanel3.SuspendLayout();
            roundBorderPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)overlayNumber).BeginInit();
            roundBorderPanel6.SuspendLayout();
            SuspendLayout();
            // 
            // namePanel
            // 
            namePanel.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            namePanel.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            namePanel.BorderWidth = 1;
            namePanel.Controls.Add(nameTextBox);
            namePanel.Controls.Add(panel3);
            namePanel.Controls.Add(label3);
            namePanel.CornerRadius = 6;
            namePanel.Location = new System.Drawing.Point(3, 11);
            namePanel.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            namePanel.Name = "namePanel";
            namePanel.Size = new System.Drawing.Size(293, 52);
            namePanel.TabIndex = 27;
            // 
            // nameTextBox
            // 
            nameTextBox.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            nameTextBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
            nameTextBox.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            nameTextBox.ForeColor = System.Drawing.Color.White;
            nameTextBox.Location = new System.Drawing.Point(105, 13);
            nameTextBox.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            nameTextBox.Name = "nameTextBox";
            nameTextBox.PlaceholderText = "请输入文件名";
            nameTextBox.Size = new System.Drawing.Size(170, 26);
            nameTextBox.TabIndex = 2;
            // 
            // panel3
            // 
            panel3.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel3.Location = new System.Drawing.Point(90, 0);
            panel3.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel3.Name = "panel3";
            panel3.Size = new System.Drawing.Size(1, 49);
            panel3.TabIndex = 1;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label3.ForeColor = System.Drawing.Color.White;
            label3.Location = new System.Drawing.Point(13, 16);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(54, 19);
            label3.TabIndex = 0;
            label3.Text = "文件名";
            // 
            // saveButton
            // 
            saveButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            saveButton.BorderRadius = 8;
            saveButton.BorderSize = 1;
            saveButton.ButtonTextColor = System.Drawing.Color.White;
            saveButton.CustomEnabled = false;
            saveButton.DisableColor = System.Drawing.Color.FromArgb(60, 60, 60);
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            saveButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            saveButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            saveButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            saveButton.IconImage = null;
            saveButton.IconSize = 60;
            saveButton.IconVisible = true;
            saveButton.IsPressed = false;
            saveButton.Location = new System.Drawing.Point(5, 72);
            saveButton.Margin = new System.Windows.Forms.Padding(5, 11, 5, 4);
            saveButton.Name = "saveButton";
            saveButton.NormalBorderColor = System.Drawing.Color.DimGray;
            saveButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            saveButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.Size = new System.Drawing.Size(135, 46);
            saveButton.TabIndex = 35;
            saveButton.Text = "保存图像";
            saveButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            saveButton.UseVisualStyleBackColor = false;
            saveButton.Click += saveButton_Click;
            // 
            // captureButton
            // 
            captureButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            captureButton.BorderRadius = 8;
            captureButton.BorderSize = 1;
            captureButton.ButtonTextColor = System.Drawing.Color.White;
            captureButton.CustomEnabled = true;
            captureButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            captureButton.FlatAppearance.BorderSize = 0;
            captureButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            captureButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            captureButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            captureButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            captureButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            captureButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            captureButton.IconImage = null;
            captureButton.IconSize = 60;
            captureButton.IconVisible = true;
            captureButton.IsPressed = false;
            captureButton.Location = new System.Drawing.Point(150, 11);
            captureButton.Margin = new System.Windows.Forms.Padding(5, 11, 5, 4);
            captureButton.Name = "captureButton";
            captureButton.NormalBorderColor = System.Drawing.Color.DimGray;
            captureButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            captureButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            captureButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            captureButton.Size = new System.Drawing.Size(135, 46);
            captureButton.TabIndex = 34;
            captureButton.Text = "采集图像";
            captureButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            captureButton.UseVisualStyleBackColor = false;
            captureButton.Click += captureButton_Click;
            // 
            // openButton
            // 
            openButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            openButton.BorderRadius = 8;
            openButton.BorderSize = 1;
            openButton.ButtonTextColor = System.Drawing.Color.White;
            openButton.CustomEnabled = true;
            openButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            openButton.FlatAppearance.BorderSize = 0;
            openButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            openButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            openButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            openButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            openButton.IconImage = null;
            openButton.IconSize = 60;
            openButton.IconVisible = true;
            openButton.IsPressed = false;
            openButton.Location = new System.Drawing.Point(5, 11);
            openButton.Margin = new System.Windows.Forms.Padding(5, 11, 5, 4);
            openButton.Name = "openButton";
            openButton.NormalBorderColor = System.Drawing.Color.DimGray;
            openButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            openButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openButton.Size = new System.Drawing.Size(135, 46);
            openButton.TabIndex = 33;
            openButton.Text = "准备";
            openButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            openButton.UseVisualStyleBackColor = false;
            openButton.Click += openButton_Click;
            // 
            // disconnectButton
            // 
            disconnectButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            disconnectButton.BorderRadius = 8;
            disconnectButton.BorderSize = 1;
            disconnectButton.ButtonTextColor = System.Drawing.Color.White;
            disconnectButton.CustomEnabled = true;
            disconnectButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            disconnectButton.FlatAppearance.BorderSize = 0;
            disconnectButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            disconnectButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            disconnectButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            disconnectButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            disconnectButton.IconImage = null;
            disconnectButton.IconSize = 60;
            disconnectButton.IconVisible = true;
            disconnectButton.IsPressed = false;
            disconnectButton.Location = new System.Drawing.Point(3, 637);
            disconnectButton.Margin = new System.Windows.Forms.Padding(3, 20, 3, 4);
            disconnectButton.Name = "disconnectButton";
            disconnectButton.NormalBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.NormalColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.Size = new System.Drawing.Size(293, 60);
            disconnectButton.TabIndex = 34;
            disconnectButton.Text = "断开连接";
            disconnectButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            disconnectButton.UseVisualStyleBackColor = false;
            disconnectButton.Click += disconnectButton_Click;
            // 
            // flowLayoutPanel1
            // 
            flowLayoutPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            flowLayoutPanel1.Controls.Add(openButton);
            flowLayoutPanel1.Controls.Add(captureButton);
            flowLayoutPanel1.Controls.Add(saveButton);
            flowLayoutPanel1.Location = new System.Drawing.Point(3, 480);
            flowLayoutPanel1.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            flowLayoutPanel1.Name = "flowLayoutPanel1";
            flowLayoutPanel1.Size = new System.Drawing.Size(293, 133);
            flowLayoutPanel1.TabIndex = 35;
            // 
            // flowLayoutPanel2
            // 
            flowLayoutPanel2.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            flowLayoutPanel2.Controls.Add(namePanel);
            flowLayoutPanel2.Controls.Add(roundBorderPanel4);
            flowLayoutPanel2.Controls.Add(roundBorderPanel5);
            flowLayoutPanel2.Controls.Add(roundBorderPanel1);
            flowLayoutPanel2.Controls.Add(roundBorderPanel3);
            flowLayoutPanel2.Controls.Add(roundBorderPanel2);
            flowLayoutPanel2.Controls.Add(roundBorderPanel6);
            flowLayoutPanel2.Controls.Add(flowLayoutPanel1);
            flowLayoutPanel2.Controls.Add(disconnectButton);
            flowLayoutPanel2.Location = new System.Drawing.Point(3, 0);
            flowLayoutPanel2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            flowLayoutPanel2.Name = "flowLayoutPanel2";
            flowLayoutPanel2.Size = new System.Drawing.Size(320, 722);
            flowLayoutPanel2.TabIndex = 36;
            // 
            // roundBorderPanel4
            // 
            roundBorderPanel4.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel4.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel4.BorderWidth = 1;
            roundBorderPanel4.Controls.Add(captureMode);
            roundBorderPanel4.Controls.Add(panel5);
            roundBorderPanel4.Controls.Add(label5);
            roundBorderPanel4.CornerRadius = 6;
            roundBorderPanel4.Location = new System.Drawing.Point(3, 78);
            roundBorderPanel4.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel4.Name = "roundBorderPanel4";
            roundBorderPanel4.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel4.TabIndex = 40;
            // 
            // captureMode
            // 
            captureMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            captureMode.FormattingEnabled = true;
            captureMode.Items.AddRange(new object[] { "单张采集", "连续采集" });
            captureMode.Location = new System.Drawing.Point(106, 15);
            captureMode.Name = "captureMode";
            captureMode.Size = new System.Drawing.Size(151, 28);
            captureMode.TabIndex = 2;
            // 
            // panel5
            // 
            panel5.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel5.Location = new System.Drawing.Point(90, 0);
            panel5.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel5.Name = "panel5";
            panel5.Size = new System.Drawing.Size(1, 49);
            panel5.TabIndex = 1;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label5.ForeColor = System.Drawing.Color.White;
            label5.Location = new System.Drawing.Point(13, 16);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(69, 19);
            label5.TabIndex = 0;
            label5.Text = "采集模式";
            // 
            // roundBorderPanel5
            // 
            roundBorderPanel5.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel5.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel5.BorderWidth = 1;
            roundBorderPanel5.Controls.Add(captureTime);
            roundBorderPanel5.Controls.Add(panel6);
            roundBorderPanel5.Controls.Add(label6);
            roundBorderPanel5.CornerRadius = 6;
            roundBorderPanel5.Location = new System.Drawing.Point(3, 145);
            roundBorderPanel5.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel5.Name = "roundBorderPanel5";
            roundBorderPanel5.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel5.TabIndex = 41;
            // 
            // captureTime
            // 
            captureTime.Location = new System.Drawing.Point(107, 16);
            captureTime.Maximum = new decimal(new int[] { 86400, 0, 0, 0 });
            captureTime.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            captureTime.Name = "captureTime";
            captureTime.Size = new System.Drawing.Size(150, 27);
            captureTime.TabIndex = 2;
            captureTime.Value = new decimal(new int[] { 300, 0, 0, 0 });
            // 
            // panel6
            // 
            panel6.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel6.Location = new System.Drawing.Point(90, 0);
            panel6.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel6.Name = "panel6";
            panel6.Size = new System.Drawing.Size(1, 49);
            panel6.TabIndex = 1;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label6.ForeColor = System.Drawing.Color.White;
            label6.Location = new System.Drawing.Point(13, 16);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(69, 19);
            label6.TabIndex = 0;
            label6.Text = "采集计时";
            // 
            // roundBorderPanel1
            // 
            roundBorderPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel1.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel1.BorderWidth = 1;
            roundBorderPanel1.Controls.Add(isOverlay);
            roundBorderPanel1.Controls.Add(panel1);
            roundBorderPanel1.Controls.Add(label1);
            roundBorderPanel1.CornerRadius = 6;
            roundBorderPanel1.Location = new System.Drawing.Point(3, 212);
            roundBorderPanel1.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel1.Name = "roundBorderPanel1";
            roundBorderPanel1.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel1.TabIndex = 37;
            // 
            // isOverlay
            // 
            isOverlay.AutoSize = true;
            isOverlay.ForeColor = System.Drawing.Color.White;
            isOverlay.Location = new System.Drawing.Point(105, 14);
            isOverlay.Name = "isOverlay";
            isOverlay.Size = new System.Drawing.Size(61, 24);
            isOverlay.TabIndex = 2;
            isOverlay.Text = "启用";
            isOverlay.UseVisualStyleBackColor = true;
            // 
            // panel1
            // 
            panel1.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel1.Location = new System.Drawing.Point(90, 0);
            panel1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel1.Name = "panel1";
            panel1.Size = new System.Drawing.Size(1, 49);
            panel1.TabIndex = 1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label1.ForeColor = System.Drawing.Color.White;
            label1.Location = new System.Drawing.Point(13, 16);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(69, 19);
            label1.TabIndex = 0;
            label1.Text = "多帧叠加";
            // 
            // roundBorderPanel3
            // 
            roundBorderPanel3.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel3.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel3.BorderWidth = 1;
            roundBorderPanel3.Controls.Add(overlayMode);
            roundBorderPanel3.Controls.Add(panel4);
            roundBorderPanel3.Controls.Add(label4);
            roundBorderPanel3.CornerRadius = 6;
            roundBorderPanel3.Location = new System.Drawing.Point(3, 279);
            roundBorderPanel3.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel3.Name = "roundBorderPanel3";
            roundBorderPanel3.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel3.TabIndex = 39;
            // 
            // overlayMode
            // 
            overlayMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            overlayMode.FormattingEnabled = true;
            overlayMode.Items.AddRange(new object[] { "叠加平均", "能量融合" });
            overlayMode.Location = new System.Drawing.Point(106, 15);
            overlayMode.Name = "overlayMode";
            overlayMode.Size = new System.Drawing.Size(151, 28);
            overlayMode.TabIndex = 2;
            // 
            // panel4
            // 
            panel4.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel4.Location = new System.Drawing.Point(90, 0);
            panel4.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel4.Name = "panel4";
            panel4.Size = new System.Drawing.Size(1, 49);
            panel4.TabIndex = 1;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label4.ForeColor = System.Drawing.Color.White;
            label4.Location = new System.Drawing.Point(13, 16);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(69, 19);
            label4.TabIndex = 0;
            label4.Text = "叠加模式";
            // 
            // roundBorderPanel2
            // 
            roundBorderPanel2.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel2.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel2.BorderWidth = 1;
            roundBorderPanel2.Controls.Add(overlayNumber);
            roundBorderPanel2.Controls.Add(panel2);
            roundBorderPanel2.Controls.Add(label2);
            roundBorderPanel2.CornerRadius = 6;
            roundBorderPanel2.Location = new System.Drawing.Point(3, 346);
            roundBorderPanel2.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel2.Name = "roundBorderPanel2";
            roundBorderPanel2.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel2.TabIndex = 38;
            // 
            // overlayNumber
            // 
            overlayNumber.Location = new System.Drawing.Point(105, 13);
            overlayNumber.Minimum = new decimal(new int[] { 2, 0, 0, 0 });
            overlayNumber.Name = "overlayNumber";
            overlayNumber.Size = new System.Drawing.Size(160, 27);
            overlayNumber.TabIndex = 2;
            overlayNumber.Value = new decimal(new int[] { 2, 0, 0, 0 });
            // 
            // panel2
            // 
            panel2.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel2.Location = new System.Drawing.Point(90, 0);
            panel2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel2.Name = "panel2";
            panel2.Size = new System.Drawing.Size(1, 49);
            panel2.TabIndex = 1;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label2.ForeColor = System.Drawing.Color.White;
            label2.Location = new System.Drawing.Point(13, 16);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(69, 19);
            label2.TabIndex = 0;
            label2.Text = "叠加数量";
            // 
            // roundBorderPanel6
            // 
            roundBorderPanel6.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel6.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel6.BorderWidth = 1;
            roundBorderPanel6.Controls.Add(onlyOverlay);
            roundBorderPanel6.Controls.Add(panel7);
            roundBorderPanel6.Controls.Add(label7);
            roundBorderPanel6.CornerRadius = 6;
            roundBorderPanel6.Location = new System.Drawing.Point(3, 413);
            roundBorderPanel6.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel6.Name = "roundBorderPanel6";
            roundBorderPanel6.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel6.TabIndex = 42;
            // 
            // onlyOverlay
            // 
            onlyOverlay.AutoSize = true;
            onlyOverlay.ForeColor = System.Drawing.SystemColors.ControlLightLight;
            onlyOverlay.Location = new System.Drawing.Point(107, 14);
            onlyOverlay.Name = "onlyOverlay";
            onlyOverlay.Size = new System.Drawing.Size(106, 24);
            onlyOverlay.TabIndex = 2;
            onlyOverlay.Text = "仅显示合成";
            onlyOverlay.UseVisualStyleBackColor = true;
            // 
            // panel7
            // 
            panel7.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel7.Location = new System.Drawing.Point(90, 0);
            panel7.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel7.Name = "panel7";
            panel7.Size = new System.Drawing.Size(1, 49);
            panel7.TabIndex = 1;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label7.ForeColor = System.Drawing.Color.White;
            label7.Location = new System.Drawing.Point(13, 16);
            label7.Name = "label7";
            label7.Size = new System.Drawing.Size(69, 19);
            label7.TabIndex = 0;
            label7.Text = "显示方式";
            // 
            // StaticOperatePanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            Controls.Add(flowLayoutPanel2);
            Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            Name = "StaticOperatePanel";
            Size = new System.Drawing.Size(324, 744);
            namePanel.ResumeLayout(false);
            namePanel.PerformLayout();
            flowLayoutPanel1.ResumeLayout(false);
            flowLayoutPanel2.ResumeLayout(false);
            roundBorderPanel4.ResumeLayout(false);
            roundBorderPanel4.PerformLayout();
            roundBorderPanel5.ResumeLayout(false);
            roundBorderPanel5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)captureTime).EndInit();
            roundBorderPanel1.ResumeLayout(false);
            roundBorderPanel1.PerformLayout();
            roundBorderPanel3.ResumeLayout(false);
            roundBorderPanel3.PerformLayout();
            roundBorderPanel2.ResumeLayout(false);
            roundBorderPanel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)overlayNumber).EndInit();
            roundBorderPanel6.ResumeLayout(false);
            roundBorderPanel6.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private RoundBorderPanel namePanel;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Label label3;
        private RoundButton captureButton;
        private RoundButton openButton;
        private RoundButton disconnectButton;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        public RoundButton saveButton;
        public System.Windows.Forms.TextBox nameTextBox;
        private RoundBorderPanel roundBorderPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private RoundBorderPanel roundBorderPanel2;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label label2;
        public System.Windows.Forms.CheckBox isOverlay;
        public System.Windows.Forms.NumericUpDown overlayNumber;
        private RoundBorderPanel roundBorderPanel3;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox overlayMode;
        private RoundBorderPanel roundBorderPanel4;
        private System.Windows.Forms.Panel panel5;
        private System.Windows.Forms.Label label5;
        private RoundBorderPanel roundBorderPanel5;
        private System.Windows.Forms.Panel panel6;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown captureTime;
        private RoundBorderPanel roundBorderPanel6;
        private System.Windows.Forms.Panel panel7;
        private System.Windows.Forms.Label label7;
        public System.Windows.Forms.CheckBox onlyOverlay;
        public System.Windows.Forms.ComboBox captureMode;
    }
}
