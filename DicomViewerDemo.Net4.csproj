﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="15.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FB13CDD8-6DD9-4EA5-A382-A451B45F0F07}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DicomViewerDemo</RootNamespace>
    <AssemblyName>DicomViewerDemo</AssemblyName>
    <ApplicationIcon>App.ico</ApplicationIcon>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE; DEBUG</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Optimize>false</Optimize>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Optimize>true</Optimize>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE; DEBUG</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Optimize>false</Optimize>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Optimize>true</Optimize>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE; DEBUG</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Optimize>false</Optimize>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Optimize>true</Optimize>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Vintasoft.Imaging">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Gdi">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Gdi.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Annotation.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Dicom">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Annotation.Dicom.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.Dicom.UI">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Annotation.Dicom.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Annotation.UI">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Annotation.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Dicom">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Dicom.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Dicom.UI">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Dicom.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.Jpeg2000Codec">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.Jpeg2000Codec.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Imaging.UI">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Imaging.UI.dll</HintPath>
    </Reference>
    <Reference Include="Vintasoft.Shared">
      <HintPath>..\..\..\..\Bin\DotNet4\AnyCPU\Vintasoft.Shared.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomControls\AnchorTypeEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\AnchorTypeEditorControl.designer.cs">
      <DependentUpon>AnchorTypeEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\CheckedToolStripSplitButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorPanelControl\ColorPanelControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorPanelControl\ColorPanelControl.designer.cs">
      <DependentUpon>ColorPanelControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\ColorPickerControl\ColorPickerControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorPickerControl\ColorPickerControl.designer.cs">
      <DependentUpon>ColorPickerControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\ColorSampleControl\ColorSampleControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ColorSampleControl\ColorSampleControl.designer.cs">
      <DependentUpon>ColorSampleControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\PaddingFEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\PaddingFEditorControl.designer.cs">
      <DependentUpon>PaddingFEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\TreeViewSearchControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\TreeViewSearchControl.designer.cs">
      <DependentUpon>TreeViewSearchControl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomControls\ValueEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CustomControls\ValueEditorControl.designer.cs">
      <DependentUpon>ValueEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\AssembliesLoader\Jpeg2000AssemblyLoader.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\CodecsFileFilters.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.designer.cs">
      <DependentUpon>BmpEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.designer.cs">
      <DependentUpon>GifEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.designer.cs">
      <DependentUpon>JpegEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.designer.cs">
      <DependentUpon>PbmEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.designer.cs">
      <DependentUpon>PngEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.designer.cs">
      <DependentUpon>SvgEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.designer.cs">
      <DependentUpon>TgaEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.designer.cs">
      <DependentUpon>TiffEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.designer.cs">
      <DependentUpon>WebpEncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\ImagingEncoderFactory.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.designer.cs">
      <DependentUpon>Jpeg2000EncoderSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ColorPickerDialogForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ColorPickerDialogForm.designer.cs">
      <DependentUpon>ColorPickerDialogForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.Designer.cs">
      <DependentUpon>ImageViewerToolStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewerToolStrip\PageIndexChangedEventArgs.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.designer.cs">
      <DependentUpon>ImageRenderingRequirementAddForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.designer.cs">
      <DependentUpon>ImageRenderingRequirementsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.designer.cs">
      <DependentUpon>ImageViewerSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.designer.cs">
      <DependentUpon>AddDicomDataElementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomDataElementMetadataConverter.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.designer.cs">
      <DependentUpon>DicomMetadataEditorControl.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.designer.cs">
      <DependentUpon>DicomMetadataEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataTreeView.designer.cs">
      <DependentUpon>DicomMetadataTreeView.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\MetadataTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\MetadataEditor\MetadataTreeView.designer.cs">
      <DependentUpon>MetadataTreeView.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\PropertyGridForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\PropertyGridForm.designer.cs">
      <DependentUpon>PropertyGridForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\RenderingSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\RenderingSettingsForm.designer.cs">
      <DependentUpon>RenderingSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\Resources\DemosResourcesManager.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\SaveImageFileForm.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.designer.cs">
      <DependentUpon>ThumbnailAppearanceSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.designer.cs">
      <DependentUpon>ThumbnailViewerSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\TypeEditorRegistrator\AnnotationTypeEditorRegistrator.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Actions\NoneAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Actions\SeparatorToolStripAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Actions\VisualToolAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualToolsToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualToolsToolStrip.designer.cs">
      <DependentUpon>VisualToolsToolStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\Actions\ImageMeasureToolAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\Actions\ImageMeasureToolUnitsOfMeasureAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\MeasurementVisualToolActionFactory.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\ZoomVisualTools\Actions\MagnifierToolAction.cs">
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.designer.cs">
      <DependentUpon>MagnifierToolSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode\AboutBoxBaseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DemosCommonCode\AboutBoxBaseForm.designer.cs">
      <DependentUpon>AboutBoxBaseForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode\DemosTools.cs">
    </Compile>
    <Compile Include="AnnotationsToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging\VisualToolsToolStrip\DicomAnnotatedViewerToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Dialogs\AboutBoxForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\AboutBoxForm.Designer.cs">
      <DependentUpon>AboutBoxForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\AnnotationsInfoForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\AnnotationsInfoForm.Designer.cs">
      <DependentUpon>AnnotationsInfoForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\DicomOverlaySettingEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\DicomOverlaySettingEditorForm.Designer.cs">
      <DependentUpon>DicomOverlaySettingEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\OverlayImagesViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\OverlayImagesViewer.Designer.cs">
      <DependentUpon>OverlayImagesViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\PresentationStateInfoForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\PresentationStateInfoForm.Designer.cs">
      <DependentUpon>PresentationStateInfoForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Dialogs\VoiLutParamsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Dialogs\VoiLutParamsForm.Designer.cs">
      <DependentUpon>VoiLutParamsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\DicomViewerToolInteractionButtonToolStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\DicomViewerToolInteractionButtonToolStrip.Designer.cs">
      <DependentUpon>DicomViewerToolInteractionButtonToolStrip.cs</DependentUpon>
    </Compile>
    <Compile Include="Localization\Strings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Strings.resx</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PresentationStateFileController.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="CustomControls\AnchorTypeEditorControl.resx">
      <DependentUpon>AnchorTypeEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ColorPickerControl\ColorPickerControl.de.resx">
      <DependentUpon>ColorPickerControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ColorPickerControl\ColorPickerControl.resx">
      <DependentUpon>ColorPickerControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ColorSampleControl\ColorSampleControl.resx">
      <DependentUpon>ColorSampleControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\TreeViewSearchControl.de.resx">
      <DependentUpon>TreeViewSearchControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\TreeViewSearchControl.resx">
      <DependentUpon>TreeViewSearchControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ValueEditorControl.de.resx">
      <DependentUpon>ValueEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.de.resx">
      <DependentUpon>BmpEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.de.resx">
      <DependentUpon>GifEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.de.resx">
      <DependentUpon>JpegEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.de.resx">
      <DependentUpon>PbmEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PbmEncoderSettingsForm.resx">
      <DependentUpon>PbmEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.de.resx">
      <DependentUpon>PngEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.de.resx">
      <DependentUpon>SvgEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\SvgEncoderSettingsForm.resx">
      <DependentUpon>SvgEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.de.resx">
      <DependentUpon>TgaEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TgaEncoderSettingsForm.resx">
      <DependentUpon>TgaEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.de.resx">
      <DependentUpon>TiffEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.de.resx">
      <DependentUpon>WebpEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\WebpEncoderSettingsForm.resx">
      <DependentUpon>WebpEncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.de.resx">
      <DependentUpon>Jpeg2000EncoderSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ColorPickerDialogForm.de.resx">
      <DependentUpon>ColorPickerDialogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ColorPickerDialogForm.resx">
      <DependentUpon>ColorPickerDialogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.de.resx">
      <DependentUpon>ImageViewerToolStrip.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\ImageViewerToolStrip.resx">
      <DependentUpon>ImageViewerToolStrip.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.de.resx">
      <DependentUpon>ImageRenderingRequirementAddForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.de.resx">
      <DependentUpon>ImageRenderingRequirementsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.de.resx">
      <DependentUpon>ImageViewerSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.de.resx">
      <DependentUpon>AddDicomDataElementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.de.resx">
      <DependentUpon>DicomMetadataEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorControl.resx">
      <DependentUpon>DicomMetadataEditorControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.de.resx">
      <DependentUpon>DicomMetadataEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\DicomMetadataEditorForm.resx">
      <DependentUpon>DicomMetadataEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\PropertyGridForm.de.resx">
      <DependentUpon>PropertyGridForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\RenderingSettingsForm.de.resx">
      <DependentUpon>RenderingSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.de.resx">
      <DependentUpon>ThumbnailAppearanceSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.de.resx">
      <DependentUpon>ThumbnailViewerSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.de.resx">
      <DependentUpon>MagnifierToolSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualTools\MagnifierToolSettingsForm.resx">
      <DependentUpon>MagnifierToolSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode\AboutBoxBaseForm.de.resx">
      <DependentUpon>AboutBoxBaseForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AboutBoxForm.de.resx">
      <DependentUpon>AboutBoxForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AboutBoxForm.resx">
      <DependentUpon>AboutBoxForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AnnotationsInfoForm.de.resx">
      <DependentUpon>AnnotationsInfoForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\DicomOverlaySettingEditorForm.de.resx">
      <DependentUpon>DicomOverlaySettingEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\DicomOverlaySettingEditorForm.resx">
      <DependentUpon>DicomOverlaySettingEditorForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\OverlayImagesViewer.de.resx">
      <DependentUpon>OverlayImagesViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\OverlayImagesViewer.resx">
      <DependentUpon>OverlayImagesViewer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\PresentationStateInfoForm.de.resx">
      <DependentUpon>PresentationStateInfoForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\VoiLutParamsForm.de.resx">
      <DependentUpon>VoiLutParamsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\VoiLutParamsForm.resx">
      <DependentUpon>VoiLutParamsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Localization\Strings.de.resx" />
    <EmbeddedResource Include="Localization\Strings.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.de.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\ZoomVisualTools\Resources\MagnifierTool.png">
    </EmbeddedResource>
    <Content Include="CustomControls\ColorSampleControl\ColorSampleBackground.png">
    </Content>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\ZoomVisualTools\Resources\PanTool.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Browse_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\MouseWheel.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Pan_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\WindowLevel_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_000.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_001.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_010.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_011.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_100.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_101.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_110.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging.Dicom\DicomViewerToolInteractionButton\Icons\Zoom_111.png" />
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\camera.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_beginning.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_end.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_left.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\navigate_right.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\open.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\printer.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\save.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\scanner.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\zoom_in.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewerToolStrip\Resources\zoom_out.png">
    </EmbeddedResource>
    <Content Include="directives.txt" />
    <EmbeddedResource Include="CustomControls\ColorPanelControl\ColorPanelControl.resx">
      <DependentUpon>ColorPanelControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\PaddingFEditorControl.resx">
      <DependentUpon>PaddingFEditorControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomControls\ValueEditorControl.resx">
      <DependentUpon>ValueEditorControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\BmpEncoderSettingsForm.resx">
      <DependentUpon>BmpEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\GifEncoderSettingsForm.resx">
      <DependentUpon>GifEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\JpegEncoderSettingsForm.resx">
      <DependentUpon>JpegEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\PngEncoderSettingsForm.resx">
      <DependentUpon>PngEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\ImagingEncoderFactory\Dialogs\TiffEncoderSettingsForm.resx">
      <DependentUpon>TiffEncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\Codecs\PluginsEncoderFactory\Dialogs\Jpeg2000EncoderSettingsForm.resx">
      <DependentUpon>Jpeg2000EncoderSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementAddForm.resx">
      <DependentUpon>ImageRenderingRequirementAddForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageRenderingRequirementsForm.resx">
      <DependentUpon>ImageRenderingRequirementsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ImageViewer\ImageViewerSettingsForm.resx">
      <DependentUpon>ImageViewerSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\MetadataEditor\Dicom\AddDicomDataElementForm.resx">
      <DependentUpon>AddDicomDataElementForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\PropertyGridForm.resx">
      <DependentUpon>PropertyGridForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\RenderingSettingsForm.resx">
      <DependentUpon>RenderingSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailAppearanceSettingsForm.resx">
      <DependentUpon>ThumbnailAppearanceSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\ThumbnailViewer\ThumbnailViewerSettingsForm.resx">
      <DependentUpon>ThumbnailViewerSettingsForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\Resources\None.png">
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualTools\MeasurementVisualTools\Resources\ImageMeasureTool.png">
    </EmbeddedResource>
    <Content Include="App.ico" />
    <EmbeddedResource Include="DemosCommonCode.Imaging\VisualToolsToolStrip\VisualToolsToolStrip.resx">
      <DependentUpon>VisualToolsToolStrip.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DemosCommonCode\AboutBoxBaseForm.resx">
      <DependentUpon>AboutBoxBaseForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="AnnotationsToolStrip.resx">
      <DependentUpon>AnnotationsToolStrip.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\AnnotationsInfoForm.resx">
      <DependentUpon>AnnotationsInfoForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dialogs\PresentationStateInfoForm.resx">
      <DependentUpon>PresentationStateInfoForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Icons\Arrow.png" />
    <EmbeddedResource Include="Icons\Ellipse.png" />
    <EmbeddedResource Include="Icons\Interpolated.png" />
    <EmbeddedResource Include="Icons\Multiline.png" />
    <EmbeddedResource Include="Icons\Point.png" />
    <EmbeddedResource Include="Icons\Polyline.png" />
    <EmbeddedResource Include="Icons\Rangeline.png" />
    <EmbeddedResource Include="Icons\Rectangle.png" />
    <EmbeddedResource Include="Icons\Text.png" />
    <EmbeddedResource Include="Icons\Circle.png" />
    <EmbeddedResource Include="Icons\Axis.png" />
    <EmbeddedResource Include="Icons\Ruler.png" />
    <EmbeddedResource Include="Icons\Crosshair.png" />
    <EmbeddedResource Include="Icons\Cutline.png" />
    <EmbeddedResource Include="Icons\Infiniteline.png" />
    <EmbeddedResource Include="Resources\VOI LUT.png" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>