﻿using InduVision.LkControls.FPD;
using LkControls.common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;


namespace LkControls.LayoutControls
{
    partial class LeftPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            customTabControl1 = new CustomTabControl();
            tabPage1 = new System.Windows.Forms.TabPage();

            string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            string workDirPath = Path.Combine(appDirectory, "config.txt");
            var config = ReadConfig(workDirPath);

            string fpdType = config.ContainsKey("FPDType") ? config["FPDType"] : "1";
            if(fpdType == "2")
                fpdPanel1 = new InduVision.LkControls.FPD.IRay.FPDPanel();
            else
                fpdPanel1 = new InduVision.LkControls.FPD.Nice.FPDPanel();

            tabPage2 = new System.Windows.Forms.TabPage();
            thumbnailViewer1 = new ThumbnailViewer();
            customTabControl1.SuspendLayout();
            tabPage1.SuspendLayout();
            tabPage2.SuspendLayout();
            SuspendLayout();
            // 
            // customTabControl1
            // 
            customTabControl1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left;
            customTabControl1.Controls.Add(tabPage1);
            customTabControl1.Controls.Add(tabPage2);
            customTabControl1.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            customTabControl1.ItemSize = new System.Drawing.Size(125, 30);
            customTabControl1.Location = new System.Drawing.Point(0, 0);
            customTabControl1.Margin = new System.Windows.Forms.Padding(6, 6, 6, 6);
            customTabControl1.Name = "customTabControl1";
            customTabControl1.SelectedIndex = 0;
            customTabControl1.SelectedTabBackColor = System.Drawing.Color.White;
            customTabControl1.SelectedTabBorderColor = System.Drawing.Color.Blue;
            customTabControl1.Size = new System.Drawing.Size(650, 1737);
            customTabControl1.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            customTabControl1.TabBackColor = System.Drawing.Color.LightGray;
            customTabControl1.TabBorderColor = System.Drawing.Color.Gray;
            customTabControl1.TabControlBackColor = System.Drawing.Color.WhiteSmoke;
            customTabControl1.TabForeColor = System.Drawing.Color.Black;
            customTabControl1.TabIndex = 0;
            customTabControl1.SelectedIndexChanged += CustomTabControl1_SelectedIndexChanged;
            // 
            // tabPage1
            // 
            tabPage1.BackColor = System.Drawing.Color.Transparent;
            tabPage1.Controls.Add(fpdPanel1);
            tabPage1.Location = new System.Drawing.Point(4, 34);
            tabPage1.Margin = new System.Windows.Forms.Padding(6, 6, 6, 6);
            tabPage1.Name = "tabPage1";
            tabPage1.Padding = new System.Windows.Forms.Padding(6, 6, 6, 6);
            tabPage1.Size = new System.Drawing.Size(642, 1699);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "设备控制";
            // 
            // fpdPanel1
            // 
            fpdPanel1.AutoScroll = true;
            fpdPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            fpdPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            fpdPanel1.Location = new System.Drawing.Point(6, 6);
            fpdPanel1.Margin = new System.Windows.Forms.Padding(12, 12, 12, 12);
            fpdPanel1.Name = "fpdPanel1";
            fpdPanel1.Padding = new System.Windows.Forms.Padding(10, 10, 10, 10);
            fpdPanel1.Size = new System.Drawing.Size(630, 1687);
            fpdPanel1.TabIndex = 0;
            // 
            // tabPage2
            // 
            tabPage2.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            tabPage2.Controls.Add(thumbnailViewer1);
            tabPage2.Location = new System.Drawing.Point(4, 34);
            tabPage2.Margin = new System.Windows.Forms.Padding(6, 6, 6, 6);
            tabPage2.Name = "tabPage2";
            tabPage2.Padding = new System.Windows.Forms.Padding(6, 6, 6, 6);
            tabPage2.Size = new System.Drawing.Size(642, 1699);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "文件列表";
            // 
            // thumbnailViewer1
            // 
            thumbnailViewer1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left;
            thumbnailViewer1.AutoScroll = true;
            thumbnailViewer1.FlowDirection = System.Windows.Forms.FlowDirection.TopDown;
            thumbnailViewer1.Location = new System.Drawing.Point(6, 6);
            thumbnailViewer1.Margin = new System.Windows.Forms.Padding(6, 6, 6, 6);
            thumbnailViewer1.Name = "thumbnailViewer1";
            thumbnailViewer1.Size = new System.Drawing.Size(622, 1652);
            thumbnailViewer1.TabIndex = 0;
            thumbnailViewer1.WrapContents = false;
            thumbnailViewer1.ImageSelected += ThumbnailViewer1_ImageSelected;
            // 
            // LeftPanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(18F, 39F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            AutoSize = true;
            BackColor = System.Drawing.Color.FromArgb(20, 20, 20);
            Controls.Add(customTabControl1);
            Margin = new System.Windows.Forms.Padding(6, 6, 6, 6);
            Name = "LeftPanel";
            Padding = new System.Windows.Forms.Padding(20, 0, 20, 20);
            Size = new System.Drawing.Size(758, 1763);
            customTabControl1.ResumeLayout(false);
            tabPage1.ResumeLayout(false);
            tabPage2.ResumeLayout(false);
            ResumeLayout(false);
        }

        public static Dictionary<string, string> ReadConfig(string path)
        {
            var config = new Dictionary<string, string>();

            if (!File.Exists(path))
                return config;

            foreach (var line in File.ReadAllLines(path))
            {
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#")) continue;

                var parts = line.Split('=', 2);
                if (parts.Length == 2)
                    config[parts[0].Trim()] = parts[1].Trim();
            }

            return config;
        }

        #endregion
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        public ThumbnailViewer thumbnailViewer1;
        public CustomTabControl customTabControl1;
        public IFPDPanel fpdPanel1;
    }
}
