﻿using System.Windows.Forms;
using System.Windows.Forms.PropertyGridInternal;
using LkControls.common;

namespace LkControls.LayoutControls
{
    partial class ToolsPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ToolsPanel));
            splitContainer1 = new SplitContainer();
            flowLayoutPanel1 = new FlowLayoutPanel();
            panel1 = new Panel();
            label1 = new Label();
            flowLayoutPanel2 = new FlowLayoutPanel();
            openProjectButton = new RoundButton();
            importImageButton = new RoundButton();
            screenshotButton = new RoundButton();
            exportImageButton = new RoundButton();
            panButton = new RoundButton();
            zoomButton = new RoundButton();
            wwwlButton = new RoundButton();
            resetButton = new RoundButton();
            panel2 = new Panel();
            label3 = new Label();
            flowLayoutPanel3 = new FlowLayoutPanel();
            clockwiseButton = new RoundButton();
            counterclockwiseButton = new RoundButton();
            verticalButton = new RoundButton();
            horizontalButton = new RoundButton();
            negativeButton = new RoundButton();
            regionButton = new RoundButton();
            enhancementTitlePanel = new Panel();
            pictureBox5 = new PictureBox();
            label5 = new Label();
            enhancementButtonPanel = new FlowLayoutPanel();
            enhanceBtn1 = new RoundButton();
            enhanceBtn2 = new RoundButton();
            enhanceBtn3 = new RoundButton();
            noiseBtn1 = new RoundButton();
            noiseBtn2 = new RoundButton();
            noiseBtn3 = new RoundButton();
            pseudoColorBtn = new RoundButton();
            rilievoBtn = new RoundButton();
            measureTitlePanel = new Panel();
            label2 = new Label();
            measureButtonPanel = new FlowLayoutPanel();
            scalingButton = new RoundButton();
            magnifierButton = new RoundButton();
            lineMeasureButton = new RoundButton();
            ellipseMeasureButton = new RoundButton();
            linesMeasureButton = new RoundButton();
            angleMeasureButton = new RoundButton();
            nsnrButton = new RoundButton();
            duplexIQIButton = new RoundButton();
            WallThicknessBtn = new RoundButton();
            annotationTitlePanel = new Panel();
            label4 = new Label();
            annotationButtonPanel = new FlowLayoutPanel();
            pointButton = new RoundButton();
            circleButton = new RoundButton();
            polylineButton = new RoundButton();
            interpolatedButton = new RoundButton();
            rectangleButton = new RoundButton();
            ellipseButton = new RoundButton();
            multilineButton = new RoundButton();
            rangelineButton = new RoundButton();
            infinitelineButton = new RoundButton();
            cutlineButton = new RoundButton();
            arrowButton = new RoundButton();
            axisButton = new RoundButton();
            rulerButton = new RoundButton();
            crosshairButton = new RoundButton();
            textButton = new RoundButton();
            roundPanel1 = new RoundPanel();
            ((System.ComponentModel.ISupportInitialize)splitContainer1).BeginInit();
            splitContainer1.Panel1.SuspendLayout();
            splitContainer1.SuspendLayout();
            flowLayoutPanel1.SuspendLayout();
            panel1.SuspendLayout();
            flowLayoutPanel2.SuspendLayout();
            panel2.SuspendLayout();
            flowLayoutPanel3.SuspendLayout();
            enhancementTitlePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox5).BeginInit();
            enhancementButtonPanel.SuspendLayout();
            measureTitlePanel.SuspendLayout();
            measureButtonPanel.SuspendLayout();
            annotationTitlePanel.SuspendLayout();
            annotationButtonPanel.SuspendLayout();
            roundPanel1.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer1
            // 
            splitContainer1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            splitContainer1.Dock = DockStyle.Fill;
            splitContainer1.Location = new System.Drawing.Point(0, 0);
            splitContainer1.Margin = new Padding(3, 4, 3, 4);
            splitContainer1.Name = "splitContainer1";
            splitContainer1.Orientation = Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            splitContainer1.Panel1.Controls.Add(flowLayoutPanel1);
            splitContainer1.Panel1.RightToLeft = RightToLeft.No;
            // 
            // splitContainer1.Panel2
            // 
            splitContainer1.Panel2.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            splitContainer1.Panel2.RightToLeft = RightToLeft.No;
            splitContainer1.Panel2Collapsed = true;
            splitContainer1.RightToLeft = RightToLeft.No;
            splitContainer1.Size = new System.Drawing.Size(381, 1374);
            splitContainer1.SplitterDistance = 29;
            splitContainer1.TabIndex = 12;
            // 
            // flowLayoutPanel1
            // 
            flowLayoutPanel1.AutoScroll = true;
            flowLayoutPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            flowLayoutPanel1.Controls.Add(panel1);
            flowLayoutPanel1.Controls.Add(flowLayoutPanel2);
            flowLayoutPanel1.Controls.Add(panel2);
            flowLayoutPanel1.Controls.Add(flowLayoutPanel3);
            flowLayoutPanel1.Controls.Add(enhancementTitlePanel);
            flowLayoutPanel1.Controls.Add(enhancementButtonPanel);
            flowLayoutPanel1.Controls.Add(measureTitlePanel);
            flowLayoutPanel1.Controls.Add(measureButtonPanel);
            flowLayoutPanel1.Controls.Add(annotationTitlePanel);
            flowLayoutPanel1.Controls.Add(annotationButtonPanel);
            flowLayoutPanel1.Dock = DockStyle.Fill;
            flowLayoutPanel1.FlowDirection = FlowDirection.TopDown;
            flowLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            flowLayoutPanel1.Margin = new Padding(3, 2, 3, 2);
            flowLayoutPanel1.Name = "flowLayoutPanel1";
            flowLayoutPanel1.Padding = new Padding(10, 9, 0, 0);
            flowLayoutPanel1.Size = new System.Drawing.Size(381, 1374);
            flowLayoutPanel1.TabIndex = 15;
            flowLayoutPanel1.WrapContents = false;
            // 
            // panel1
            // 
            panel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            panel1.Controls.Add(label1);
            panel1.Location = new System.Drawing.Point(13, 13);
            panel1.Margin = new Padding(3, 4, 3, 4);
            panel1.Name = "panel1";
            panel1.Padding = new Padding(0, 4, 0, 6);
            panel1.Size = new System.Drawing.Size(357, 40);
            panel1.TabIndex = 1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.ForeColor = System.Drawing.SystemColors.Window;
            label1.Location = new System.Drawing.Point(13, 12);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(69, 20);
            label1.TabIndex = 0;
            label1.Text = "基础操作";
            // 
            // flowLayoutPanel2
            // 
            flowLayoutPanel2.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            flowLayoutPanel2.Controls.Add(openProjectButton);
            flowLayoutPanel2.Controls.Add(importImageButton);
            flowLayoutPanel2.Controls.Add(screenshotButton);
            flowLayoutPanel2.Controls.Add(exportImageButton);
            flowLayoutPanel2.Controls.Add(panButton);
            flowLayoutPanel2.Controls.Add(zoomButton);
            flowLayoutPanel2.Controls.Add(wwwlButton);
            flowLayoutPanel2.Controls.Add(resetButton);
            flowLayoutPanel2.Location = new System.Drawing.Point(13, 59);
            flowLayoutPanel2.Margin = new Padding(3, 2, 3, 2);
            flowLayoutPanel2.Name = "flowLayoutPanel2";
            flowLayoutPanel2.Padding = new Padding(3, 4, 3, 4);
            flowLayoutPanel2.Size = new System.Drawing.Size(357, 247);
            flowLayoutPanel2.TabIndex = 16;
            // 
            // openProjectButton
            // 
            openProjectButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            openProjectButton.BorderRadius = 8;
            openProjectButton.BorderSize = 1;
            openProjectButton.ButtonTextColor = System.Drawing.Color.White;
            openProjectButton.CustomEnabled = true;
            openProjectButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            openProjectButton.FlatStyle = FlatStyle.Flat;
            openProjectButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            openProjectButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            openProjectButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openProjectButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openProjectButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            openProjectButton.IconImage = null;
            openProjectButton.IconSize = 60;
            openProjectButton.IconVisible = true;
            openProjectButton.IsPressed = false;
            openProjectButton.Location = new System.Drawing.Point(8, 10);
            openProjectButton.Margin = new Padding(5, 6, 5, 6);
            openProjectButton.Name = "openProjectButton";
            openProjectButton.NormalBorderColor = System.Drawing.Color.DimGray;
            openProjectButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            openProjectButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openProjectButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            openProjectButton.Size = new System.Drawing.Size(159, 48);
            openProjectButton.TabIndex = 5;
            openProjectButton.Text = "打开项目";
            openProjectButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            openProjectButton.UseVisualStyleBackColor = false;
            openProjectButton.Click += openProjectButton_Click;
            // 
            // importImageButton
            // 
            importImageButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            importImageButton.BorderRadius = 8;
            importImageButton.BorderSize = 1;
            importImageButton.ButtonTextColor = System.Drawing.Color.White;
            importImageButton.CustomEnabled = true;
            importImageButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            importImageButton.FlatStyle = FlatStyle.Flat;
            importImageButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            importImageButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            importImageButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            importImageButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            importImageButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            importImageButton.IconImage = null;
            importImageButton.IconSize = 60;
            importImageButton.IconVisible = true;
            importImageButton.IsPressed = false;
            importImageButton.Location = new System.Drawing.Point(177, 10);
            importImageButton.Margin = new Padding(5, 6, 5, 6);
            importImageButton.Name = "importImageButton";
            importImageButton.NormalBorderColor = System.Drawing.Color.DimGray;
            importImageButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            importImageButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            importImageButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            importImageButton.Size = new System.Drawing.Size(159, 48);
            importImageButton.TabIndex = 6;
            importImageButton.Text = "导入图像";
            importImageButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            importImageButton.UseVisualStyleBackColor = false;
            importImageButton.Click += importImageButton_Click;
            // 
            // screenshotButton
            // 
            screenshotButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            screenshotButton.BorderRadius = 8;
            screenshotButton.BorderSize = 1;
            screenshotButton.ButtonTextColor = System.Drawing.Color.White;
            screenshotButton.CustomEnabled = true;
            screenshotButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            screenshotButton.FlatStyle = FlatStyle.Flat;
            screenshotButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            screenshotButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            screenshotButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            screenshotButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            screenshotButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            screenshotButton.IconImage = null;
            screenshotButton.IconSize = 60;
            screenshotButton.IconVisible = true;
            screenshotButton.IsPressed = false;
            screenshotButton.Location = new System.Drawing.Point(8, 70);
            screenshotButton.Margin = new Padding(5, 6, 5, 6);
            screenshotButton.Name = "screenshotButton";
            screenshotButton.NormalBorderColor = System.Drawing.Color.DimGray;
            screenshotButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            screenshotButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            screenshotButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            screenshotButton.Size = new System.Drawing.Size(159, 48);
            screenshotButton.TabIndex = 7;
            screenshotButton.Text = "截图";
            screenshotButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            screenshotButton.UseVisualStyleBackColor = false;
            screenshotButton.Click += screenshotButton_Click;
            // 
            // exportImageButton
            // 
            exportImageButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            exportImageButton.BorderRadius = 8;
            exportImageButton.BorderSize = 1;
            exportImageButton.ButtonTextColor = System.Drawing.Color.White;
            exportImageButton.CustomEnabled = true;
            exportImageButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            exportImageButton.FlatStyle = FlatStyle.Flat;
            exportImageButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            exportImageButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            exportImageButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            exportImageButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            exportImageButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            exportImageButton.IconImage = null;
            exportImageButton.IconSize = 60;
            exportImageButton.IconVisible = true;
            exportImageButton.IsPressed = false;
            exportImageButton.Location = new System.Drawing.Point(177, 70);
            exportImageButton.Margin = new Padding(5, 6, 5, 6);
            exportImageButton.Name = "exportImageButton";
            exportImageButton.NormalBorderColor = System.Drawing.Color.DimGray;
            exportImageButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            exportImageButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            exportImageButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            exportImageButton.Size = new System.Drawing.Size(159, 48);
            exportImageButton.TabIndex = 11;
            exportImageButton.Text = "图像导出";
            exportImageButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            exportImageButton.UseVisualStyleBackColor = false;
            exportImageButton.Click += exportImageButton_Click;
            // 
            // panButton
            // 
            panButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            panButton.BorderRadius = 8;
            panButton.BorderSize = 1;
            panButton.ButtonTextColor = System.Drawing.Color.White;
            panButton.CustomEnabled = true;
            panButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            panButton.FlatStyle = FlatStyle.Flat;
            panButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            panButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            panButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            panButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            panButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            panButton.IconImage = null;
            panButton.IconSize = 60;
            panButton.IconVisible = true;
            panButton.IsPressed = false;
            panButton.Location = new System.Drawing.Point(8, 130);
            panButton.Margin = new Padding(5, 6, 5, 6);
            panButton.Name = "panButton";
            panButton.NormalBorderColor = System.Drawing.Color.DimGray;
            panButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            panButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            panButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            panButton.Size = new System.Drawing.Size(159, 48);
            panButton.TabIndex = 8;
            panButton.Text = "平移";
            panButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            panButton.UseVisualStyleBackColor = false;
            panButton.Click += panButton_Click;
            // 
            // zoomButton
            // 
            zoomButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            zoomButton.BorderRadius = 8;
            zoomButton.BorderSize = 1;
            zoomButton.ButtonTextColor = System.Drawing.Color.White;
            zoomButton.CustomEnabled = true;
            zoomButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            zoomButton.FlatStyle = FlatStyle.Flat;
            zoomButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            zoomButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            zoomButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            zoomButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            zoomButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            zoomButton.IconImage = null;
            zoomButton.IconSize = 60;
            zoomButton.IconVisible = true;
            zoomButton.IsPressed = false;
            zoomButton.Location = new System.Drawing.Point(177, 130);
            zoomButton.Margin = new Padding(5, 6, 5, 6);
            zoomButton.Name = "zoomButton";
            zoomButton.NormalBorderColor = System.Drawing.Color.DimGray;
            zoomButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            zoomButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            zoomButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            zoomButton.Size = new System.Drawing.Size(159, 48);
            zoomButton.TabIndex = 9;
            zoomButton.Text = "缩放";
            zoomButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            zoomButton.UseVisualStyleBackColor = false;
            zoomButton.Click += zoomButton_Click;
            // 
            // wwwlButton
            // 
            wwwlButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            wwwlButton.BorderRadius = 8;
            wwwlButton.BorderSize = 1;
            wwwlButton.ButtonTextColor = System.Drawing.Color.White;
            wwwlButton.CustomEnabled = true;
            wwwlButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            wwwlButton.FlatStyle = FlatStyle.Flat;
            wwwlButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            wwwlButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            wwwlButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            wwwlButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            wwwlButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            wwwlButton.IconImage = null;
            wwwlButton.IconSize = 60;
            wwwlButton.IconVisible = true;
            wwwlButton.IsPressed = false;
            wwwlButton.Location = new System.Drawing.Point(8, 190);
            wwwlButton.Margin = new Padding(5, 6, 5, 6);
            wwwlButton.Name = "wwwlButton";
            wwwlButton.NormalBorderColor = System.Drawing.Color.DimGray;
            wwwlButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            wwwlButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            wwwlButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            wwwlButton.Size = new System.Drawing.Size(159, 48);
            wwwlButton.TabIndex = 10;
            wwwlButton.Text = "窗宽窗位";
            wwwlButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            wwwlButton.UseVisualStyleBackColor = false;
            wwwlButton.Click += wwwlButton_Click;
            // 
            // resetButton
            // 
            resetButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            resetButton.BorderRadius = 8;
            resetButton.BorderSize = 1;
            resetButton.ButtonTextColor = System.Drawing.Color.White;
            resetButton.CustomEnabled = true;
            resetButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            resetButton.FlatStyle = FlatStyle.Flat;
            resetButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            resetButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            resetButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            resetButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            resetButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            resetButton.IconImage = null;
            resetButton.IconSize = 60;
            resetButton.IconVisible = true;
            resetButton.IsPressed = false;
            resetButton.Location = new System.Drawing.Point(177, 190);
            resetButton.Margin = new Padding(5, 6, 5, 6);
            resetButton.Name = "resetButton";
            resetButton.NormalBorderColor = System.Drawing.Color.DimGray;
            resetButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            resetButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            resetButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            resetButton.Size = new System.Drawing.Size(159, 48);
            resetButton.TabIndex = 12;
            resetButton.Text = "重置";
            resetButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            resetButton.UseVisualStyleBackColor = false;
            resetButton.Click += resetButton_Click;
            // 
            // panel2
            // 
            panel2.Controls.Add(label3);
            panel2.Location = new System.Drawing.Point(13, 312);
            panel2.Margin = new Padding(3, 4, 3, 4);
            panel2.Name = "panel2";
            panel2.Size = new System.Drawing.Size(357, 40);
            panel2.TabIndex = 8;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.ForeColor = System.Drawing.Color.White;
            label3.Location = new System.Drawing.Point(13, 8);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(69, 20);
            label3.TabIndex = 0;
            label3.Text = "图像操作";
            // 
            // flowLayoutPanel3
            // 
            flowLayoutPanel3.Controls.Add(clockwiseButton);
            flowLayoutPanel3.Controls.Add(counterclockwiseButton);
            flowLayoutPanel3.Controls.Add(verticalButton);
            flowLayoutPanel3.Controls.Add(horizontalButton);
            flowLayoutPanel3.Controls.Add(negativeButton);
            flowLayoutPanel3.Controls.Add(regionButton);
            flowLayoutPanel3.Location = new System.Drawing.Point(13, 358);
            flowLayoutPanel3.Margin = new Padding(3, 2, 3, 2);
            flowLayoutPanel3.Name = "flowLayoutPanel3";
            flowLayoutPanel3.Size = new System.Drawing.Size(357, 240);
            flowLayoutPanel3.TabIndex = 16;
            // 
            // clockwiseButton
            // 
            clockwiseButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            clockwiseButton.BorderRadius = 8;
            clockwiseButton.BorderSize = 1;
            clockwiseButton.ButtonTextColor = System.Drawing.Color.White;
            clockwiseButton.CustomEnabled = true;
            clockwiseButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            clockwiseButton.FlatStyle = FlatStyle.Flat;
            clockwiseButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            clockwiseButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            clockwiseButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            clockwiseButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            clockwiseButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            clockwiseButton.IconImage = null;
            clockwiseButton.IconSize = 60;
            clockwiseButton.IconVisible = true;
            clockwiseButton.IsPressed = false;
            clockwiseButton.Location = new System.Drawing.Point(5, 6);
            clockwiseButton.Margin = new Padding(5, 6, 5, 6);
            clockwiseButton.Name = "clockwiseButton";
            clockwiseButton.NormalBorderColor = System.Drawing.Color.DimGray;
            clockwiseButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            clockwiseButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            clockwiseButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            clockwiseButton.Size = new System.Drawing.Size(159, 48);
            clockwiseButton.TabIndex = 1;
            clockwiseButton.Text = "顺时针旋转";
            clockwiseButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            clockwiseButton.UseVisualStyleBackColor = false;
            clockwiseButton.Click += clockwiseButton_Click;
            // 
            // counterclockwiseButton
            // 
            counterclockwiseButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            counterclockwiseButton.BorderRadius = 8;
            counterclockwiseButton.BorderSize = 1;
            counterclockwiseButton.ButtonTextColor = System.Drawing.Color.White;
            counterclockwiseButton.CustomEnabled = true;
            counterclockwiseButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            counterclockwiseButton.FlatStyle = FlatStyle.Flat;
            counterclockwiseButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            counterclockwiseButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            counterclockwiseButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            counterclockwiseButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            counterclockwiseButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            counterclockwiseButton.IconImage = null;
            counterclockwiseButton.IconSize = 60;
            counterclockwiseButton.IconVisible = true;
            counterclockwiseButton.IsPressed = false;
            counterclockwiseButton.Location = new System.Drawing.Point(174, 6);
            counterclockwiseButton.Margin = new Padding(5, 6, 5, 6);
            counterclockwiseButton.Name = "counterclockwiseButton";
            counterclockwiseButton.NormalBorderColor = System.Drawing.Color.DimGray;
            counterclockwiseButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            counterclockwiseButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            counterclockwiseButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            counterclockwiseButton.Size = new System.Drawing.Size(159, 48);
            counterclockwiseButton.TabIndex = 2;
            counterclockwiseButton.Text = "逆时针旋转";
            counterclockwiseButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            counterclockwiseButton.UseVisualStyleBackColor = false;
            counterclockwiseButton.Click += counterclockwiseButton_Click;
            // 
            // verticalButton
            // 
            verticalButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            verticalButton.BorderRadius = 8;
            verticalButton.BorderSize = 1;
            verticalButton.ButtonTextColor = System.Drawing.Color.White;
            verticalButton.CustomEnabled = true;
            verticalButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            verticalButton.FlatStyle = FlatStyle.Flat;
            verticalButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            verticalButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            verticalButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            verticalButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            verticalButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            verticalButton.IconImage = null;
            verticalButton.IconSize = 60;
            verticalButton.IconVisible = true;
            verticalButton.IsPressed = false;
            verticalButton.Location = new System.Drawing.Point(5, 66);
            verticalButton.Margin = new Padding(5, 6, 5, 6);
            verticalButton.Name = "verticalButton";
            verticalButton.NormalBorderColor = System.Drawing.Color.DimGray;
            verticalButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            verticalButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            verticalButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            verticalButton.Size = new System.Drawing.Size(159, 48);
            verticalButton.TabIndex = 3;
            verticalButton.Text = "上下翻转";
            verticalButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            verticalButton.UseVisualStyleBackColor = false;
            verticalButton.Click += verticalButton_Click;
            // 
            // horizontalButton
            // 
            horizontalButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            horizontalButton.BorderRadius = 8;
            horizontalButton.BorderSize = 1;
            horizontalButton.ButtonTextColor = System.Drawing.Color.White;
            horizontalButton.CustomEnabled = true;
            horizontalButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            horizontalButton.FlatStyle = FlatStyle.Flat;
            horizontalButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            horizontalButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            horizontalButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            horizontalButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            horizontalButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            horizontalButton.IconImage = null;
            horizontalButton.IconSize = 60;
            horizontalButton.IconVisible = true;
            horizontalButton.IsPressed = false;
            horizontalButton.Location = new System.Drawing.Point(174, 66);
            horizontalButton.Margin = new Padding(5, 6, 5, 6);
            horizontalButton.Name = "horizontalButton";
            horizontalButton.NormalBorderColor = System.Drawing.Color.DimGray;
            horizontalButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            horizontalButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            horizontalButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            horizontalButton.Size = new System.Drawing.Size(159, 48);
            horizontalButton.TabIndex = 4;
            horizontalButton.Text = "左右翻转";
            horizontalButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            horizontalButton.UseVisualStyleBackColor = false;
            horizontalButton.Click += horizontalButton_Click;
            // 
            // negativeButton
            // 
            negativeButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            negativeButton.BorderRadius = 8;
            negativeButton.BorderSize = 1;
            negativeButton.ButtonTextColor = System.Drawing.Color.White;
            negativeButton.CustomEnabled = true;
            negativeButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            negativeButton.FlatStyle = FlatStyle.Flat;
            negativeButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            negativeButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            negativeButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            negativeButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            negativeButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            negativeButton.IconImage = null;
            negativeButton.IconSize = 60;
            negativeButton.IconVisible = true;
            negativeButton.IsPressed = false;
            negativeButton.Location = new System.Drawing.Point(5, 126);
            negativeButton.Margin = new Padding(5, 6, 5, 6);
            negativeButton.Name = "negativeButton";
            negativeButton.NormalBorderColor = System.Drawing.Color.DimGray;
            negativeButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            negativeButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            negativeButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            negativeButton.Size = new System.Drawing.Size(159, 48);
            negativeButton.TabIndex = 5;
            negativeButton.Text = "图像负像";
            negativeButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            negativeButton.UseVisualStyleBackColor = false;
            negativeButton.Click += negativeButton_Click;
            // 
            // regionButton
            // 
            regionButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            regionButton.BorderRadius = 8;
            regionButton.BorderSize = 1;
            regionButton.ButtonTextColor = System.Drawing.Color.White;
            regionButton.CustomEnabled = true;
            regionButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            regionButton.FlatStyle = FlatStyle.Flat;
            regionButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            regionButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            regionButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            regionButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            regionButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            regionButton.IconImage = null;
            regionButton.IconSize = 60;
            regionButton.IconVisible = true;
            regionButton.IsPressed = false;
            regionButton.Location = new System.Drawing.Point(174, 126);
            regionButton.Margin = new Padding(5, 6, 5, 6);
            regionButton.Name = "regionButton";
            regionButton.NormalBorderColor = System.Drawing.Color.DimGray;
            regionButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            regionButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            regionButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            regionButton.Size = new System.Drawing.Size(159, 48);
            regionButton.TabIndex = 7;
            regionButton.Text = "区域关注";
            regionButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            regionButton.UseVisualStyleBackColor = false;
            regionButton.Click += regionButton_Click;
            // 
            // enhancementTitlePanel
            // 
            enhancementTitlePanel.Controls.Add(pictureBox5);
            enhancementTitlePanel.Controls.Add(label5);
            enhancementTitlePanel.Location = new System.Drawing.Point(13, 604);
            enhancementTitlePanel.Margin = new Padding(3, 4, 3, 4);
            enhancementTitlePanel.Name = "enhancementTitlePanel";
            enhancementTitlePanel.Size = new System.Drawing.Size(357, 40);
            enhancementTitlePanel.TabIndex = 17;
            // 
            // pictureBox5
            // 
            pictureBox5.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            pictureBox5.Image = (System.Drawing.Image)resources.GetObject("pictureBox5.Image");
            pictureBox5.Location = new System.Drawing.Point(472, 12);
            pictureBox5.Margin = new Padding(3, 4, 3, 4);
            pictureBox5.Name = "pictureBox5";
            pictureBox5.Size = new System.Drawing.Size(23, 20);
            pictureBox5.TabIndex = 1;
            pictureBox5.TabStop = false;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.ForeColor = System.Drawing.Color.White;
            label5.Location = new System.Drawing.Point(13, 8);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(69, 20);
            label5.TabIndex = 0;
            label5.Text = "图像增强";
            // 
            // enhancementButtonPanel
            // 
            enhancementButtonPanel.Controls.Add(enhanceBtn1);
            enhancementButtonPanel.Controls.Add(enhanceBtn2);
            enhancementButtonPanel.Controls.Add(enhanceBtn3);
            enhancementButtonPanel.Controls.Add(noiseBtn1);
            enhancementButtonPanel.Controls.Add(noiseBtn2);
            enhancementButtonPanel.Controls.Add(noiseBtn3);
            enhancementButtonPanel.Controls.Add(pseudoColorBtn);
            enhancementButtonPanel.Controls.Add(rilievoBtn);
            enhancementButtonPanel.Location = new System.Drawing.Point(13, 650);
            enhancementButtonPanel.Margin = new Padding(3, 2, 3, 2);
            enhancementButtonPanel.Name = "enhancementButtonPanel";
            enhancementButtonPanel.Size = new System.Drawing.Size(357, 368);
            enhancementButtonPanel.TabIndex = 18;
            // 
            // enhanceBtn1
            // 
            enhanceBtn1.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn1.BorderRadius = 8;
            enhanceBtn1.BorderSize = 1;
            enhanceBtn1.ButtonTextColor = System.Drawing.Color.White;
            enhanceBtn1.CustomEnabled = true;
            enhanceBtn1.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            enhanceBtn1.FlatStyle = FlatStyle.Flat;
            enhanceBtn1.Font = new System.Drawing.Font("微软雅黑", 9F);
            enhanceBtn1.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn1.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn1.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn1.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            enhanceBtn1.IconImage = null;
            enhanceBtn1.IconSize = 60;
            enhanceBtn1.IconVisible = true;
            enhanceBtn1.IsPressed = false;
            enhanceBtn1.Location = new System.Drawing.Point(5, 6);
            enhanceBtn1.Margin = new Padding(5, 6, 5, 6);
            enhanceBtn1.Name = "enhanceBtn1";
            enhanceBtn1.NormalBorderColor = System.Drawing.Color.DimGray;
            enhanceBtn1.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn1.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn1.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn1.Size = new System.Drawing.Size(159, 48);
            enhanceBtn1.TabIndex = 1;
            enhanceBtn1.Text = "普通增强";
            enhanceBtn1.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            enhanceBtn1.UseVisualStyleBackColor = false;
            enhanceBtn1.Click += enhanceBtn1_Click;
            // 
            // enhanceBtn2
            // 
            enhanceBtn2.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn2.BorderRadius = 8;
            enhanceBtn2.BorderSize = 1;
            enhanceBtn2.ButtonTextColor = System.Drawing.Color.White;
            enhanceBtn2.CustomEnabled = true;
            enhanceBtn2.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            enhanceBtn2.FlatStyle = FlatStyle.Flat;
            enhanceBtn2.Font = new System.Drawing.Font("微软雅黑", 9F);
            enhanceBtn2.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn2.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn2.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn2.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            enhanceBtn2.IconImage = null;
            enhanceBtn2.IconSize = 60;
            enhanceBtn2.IconVisible = true;
            enhanceBtn2.IsPressed = false;
            enhanceBtn2.Location = new System.Drawing.Point(174, 6);
            enhanceBtn2.Margin = new Padding(5, 6, 5, 6);
            enhanceBtn2.Name = "enhanceBtn2";
            enhanceBtn2.NormalBorderColor = System.Drawing.Color.DimGray;
            enhanceBtn2.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn2.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn2.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn2.Size = new System.Drawing.Size(159, 48);
            enhanceBtn2.TabIndex = 2;
            enhanceBtn2.Text = "高级增强";
            enhanceBtn2.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            enhanceBtn2.UseVisualStyleBackColor = false;
            enhanceBtn2.Click += enhanceBtn2_Click;
            // 
            // enhanceBtn3
            // 
            enhanceBtn3.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn3.BorderRadius = 8;
            enhanceBtn3.BorderSize = 1;
            enhanceBtn3.ButtonTextColor = System.Drawing.Color.White;
            enhanceBtn3.CustomEnabled = true;
            enhanceBtn3.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            enhanceBtn3.FlatStyle = FlatStyle.Flat;
            enhanceBtn3.Font = new System.Drawing.Font("微软雅黑", 9F);
            enhanceBtn3.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn3.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn3.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn3.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            enhanceBtn3.IconImage = null;
            enhanceBtn3.IconSize = 60;
            enhanceBtn3.IconVisible = true;
            enhanceBtn3.IsPressed = false;
            enhanceBtn3.Location = new System.Drawing.Point(5, 66);
            enhanceBtn3.Margin = new Padding(5, 6, 5, 6);
            enhanceBtn3.Name = "enhanceBtn3";
            enhanceBtn3.NormalBorderColor = System.Drawing.Color.DimGray;
            enhanceBtn3.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            enhanceBtn3.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn3.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            enhanceBtn3.Size = new System.Drawing.Size(159, 48);
            enhanceBtn3.TabIndex = 3;
            enhanceBtn3.Text = "超级增强";
            enhanceBtn3.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            enhanceBtn3.UseVisualStyleBackColor = false;
            enhanceBtn3.Click += enhanceBtn3_Click;
            // 
            // noiseBtn1
            // 
            noiseBtn1.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn1.BorderRadius = 8;
            noiseBtn1.BorderSize = 1;
            noiseBtn1.ButtonTextColor = System.Drawing.Color.White;
            noiseBtn1.CustomEnabled = true;
            noiseBtn1.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            noiseBtn1.FlatStyle = FlatStyle.Flat;
            noiseBtn1.Font = new System.Drawing.Font("微软雅黑", 9F);
            noiseBtn1.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn1.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn1.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn1.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            noiseBtn1.IconImage = null;
            noiseBtn1.IconSize = 60;
            noiseBtn1.IconVisible = true;
            noiseBtn1.IsPressed = false;
            noiseBtn1.Location = new System.Drawing.Point(174, 66);
            noiseBtn1.Margin = new Padding(5, 6, 5, 6);
            noiseBtn1.Name = "noiseBtn1";
            noiseBtn1.NormalBorderColor = System.Drawing.Color.DimGray;
            noiseBtn1.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn1.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn1.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn1.Size = new System.Drawing.Size(159, 48);
            noiseBtn1.TabIndex = 4;
            noiseBtn1.Text = "普通降噪";
            noiseBtn1.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            noiseBtn1.UseVisualStyleBackColor = false;
            noiseBtn1.Click += NoiseBtn1_Click;
            // 
            // noiseBtn2
            // 
            noiseBtn2.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn2.BorderRadius = 8;
            noiseBtn2.BorderSize = 1;
            noiseBtn2.ButtonTextColor = System.Drawing.Color.White;
            noiseBtn2.CustomEnabled = true;
            noiseBtn2.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            noiseBtn2.FlatStyle = FlatStyle.Flat;
            noiseBtn2.Font = new System.Drawing.Font("微软雅黑", 9F);
            noiseBtn2.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn2.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn2.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn2.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            noiseBtn2.IconImage = null;
            noiseBtn2.IconSize = 60;
            noiseBtn2.IconVisible = true;
            noiseBtn2.IsPressed = false;
            noiseBtn2.Location = new System.Drawing.Point(5, 126);
            noiseBtn2.Margin = new Padding(5, 6, 5, 6);
            noiseBtn2.Name = "noiseBtn2";
            noiseBtn2.NormalBorderColor = System.Drawing.Color.DimGray;
            noiseBtn2.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn2.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn2.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn2.Size = new System.Drawing.Size(159, 48);
            noiseBtn2.TabIndex = 5;
            noiseBtn2.Text = "高级降噪";
            noiseBtn2.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            noiseBtn2.UseVisualStyleBackColor = false;
            noiseBtn2.Click += noiseBtn2_Click;
            // 
            // noiseBtn3
            // 
            noiseBtn3.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn3.BorderRadius = 8;
            noiseBtn3.BorderSize = 1;
            noiseBtn3.ButtonTextColor = System.Drawing.Color.White;
            noiseBtn3.CustomEnabled = true;
            noiseBtn3.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            noiseBtn3.FlatStyle = FlatStyle.Flat;
            noiseBtn3.Font = new System.Drawing.Font("微软雅黑", 9F);
            noiseBtn3.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn3.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn3.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn3.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            noiseBtn3.IconImage = null;
            noiseBtn3.IconSize = 60;
            noiseBtn3.IconVisible = true;
            noiseBtn3.IsPressed = false;
            noiseBtn3.Location = new System.Drawing.Point(174, 126);
            noiseBtn3.Margin = new Padding(5, 6, 5, 6);
            noiseBtn3.Name = "noiseBtn3";
            noiseBtn3.NormalBorderColor = System.Drawing.Color.DimGray;
            noiseBtn3.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            noiseBtn3.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn3.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            noiseBtn3.Size = new System.Drawing.Size(159, 48);
            noiseBtn3.TabIndex = 6;
            noiseBtn3.Text = "超级降噪";
            noiseBtn3.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            noiseBtn3.UseVisualStyleBackColor = false;
            noiseBtn3.Click += noiseBtn3_Click;
            // 
            // pseudoColorBtn
            // 
            pseudoColorBtn.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            pseudoColorBtn.BorderRadius = 8;
            pseudoColorBtn.BorderSize = 1;
            pseudoColorBtn.ButtonTextColor = System.Drawing.Color.White;
            pseudoColorBtn.CustomEnabled = true;
            pseudoColorBtn.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            pseudoColorBtn.FlatStyle = FlatStyle.Flat;
            pseudoColorBtn.Font = new System.Drawing.Font("微软雅黑", 9F);
            pseudoColorBtn.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            pseudoColorBtn.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pseudoColorBtn.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pseudoColorBtn.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            pseudoColorBtn.IconImage = null;
            pseudoColorBtn.IconSize = 60;
            pseudoColorBtn.IconVisible = true;
            pseudoColorBtn.IsPressed = false;
            pseudoColorBtn.Location = new System.Drawing.Point(5, 186);
            pseudoColorBtn.Margin = new Padding(5, 6, 5, 6);
            pseudoColorBtn.Name = "pseudoColorBtn";
            pseudoColorBtn.NormalBorderColor = System.Drawing.Color.DimGray;
            pseudoColorBtn.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            pseudoColorBtn.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pseudoColorBtn.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pseudoColorBtn.Size = new System.Drawing.Size(159, 48);
            pseudoColorBtn.TabIndex = 9;
            pseudoColorBtn.Text = "伪彩";
            pseudoColorBtn.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            pseudoColorBtn.UseVisualStyleBackColor = false;
            pseudoColorBtn.Click += pseudoColorBtn_Click;
            // 
            // rilievoBtn
            // 
            rilievoBtn.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rilievoBtn.BorderRadius = 8;
            rilievoBtn.BorderSize = 1;
            rilievoBtn.ButtonTextColor = System.Drawing.Color.White;
            rilievoBtn.CustomEnabled = true;
            rilievoBtn.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            rilievoBtn.FlatStyle = FlatStyle.Flat;
            rilievoBtn.Font = new System.Drawing.Font("微软雅黑", 9F);
            rilievoBtn.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rilievoBtn.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rilievoBtn.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rilievoBtn.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            rilievoBtn.IconImage = null;
            rilievoBtn.IconSize = 60;
            rilievoBtn.IconVisible = true;
            rilievoBtn.IsPressed = false;
            rilievoBtn.Location = new System.Drawing.Point(174, 186);
            rilievoBtn.Margin = new Padding(5, 6, 5, 6);
            rilievoBtn.Name = "rilievoBtn";
            rilievoBtn.NormalBorderColor = System.Drawing.Color.DimGray;
            rilievoBtn.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rilievoBtn.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rilievoBtn.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rilievoBtn.Size = new System.Drawing.Size(159, 48);
            rilievoBtn.TabIndex = 10;
            rilievoBtn.Text = "浮雕";
            rilievoBtn.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            rilievoBtn.UseVisualStyleBackColor = false;
            rilievoBtn.Click += rilievoBtn_Click;
            // 
            // measureTitlePanel
            // 
            measureTitlePanel.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            measureTitlePanel.Controls.Add(label2);
            measureTitlePanel.Location = new System.Drawing.Point(13, 1022);
            measureTitlePanel.Margin = new Padding(3, 2, 3, 2);
            measureTitlePanel.Name = "measureTitlePanel";
            measureTitlePanel.Size = new System.Drawing.Size(357, 40);
            measureTitlePanel.TabIndex = 10;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.ForeColor = System.Drawing.Color.White;
            label2.Location = new System.Drawing.Point(13, 9);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(69, 20);
            label2.TabIndex = 0;
            label2.Text = "图像测量";
            // 
            // measureButtonPanel
            // 
            measureButtonPanel.Controls.Add(scalingButton);
            measureButtonPanel.Controls.Add(magnifierButton);
            measureButtonPanel.Controls.Add(lineMeasureButton);
            measureButtonPanel.Controls.Add(ellipseMeasureButton);
            measureButtonPanel.Controls.Add(linesMeasureButton);
            measureButtonPanel.Controls.Add(angleMeasureButton);
            measureButtonPanel.Controls.Add(nsnrButton);
            measureButtonPanel.Controls.Add(duplexIQIButton);
            measureButtonPanel.Controls.Add(WallThicknessBtn);
            measureButtonPanel.Location = new System.Drawing.Point(13, 1066);
            measureButtonPanel.Margin = new Padding(3, 2, 3, 2);
            measureButtonPanel.Name = "measureButtonPanel";
            measureButtonPanel.Size = new System.Drawing.Size(357, 304);
            measureButtonPanel.TabIndex = 16;
            // 
            // scalingButton
            // 
            scalingButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            scalingButton.BorderRadius = 8;
            scalingButton.BorderSize = 1;
            scalingButton.ButtonTextColor = System.Drawing.Color.White;
            scalingButton.CustomEnabled = true;
            scalingButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            scalingButton.FlatStyle = FlatStyle.Flat;
            scalingButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            scalingButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            scalingButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            scalingButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            scalingButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            scalingButton.IconImage = null;
            scalingButton.IconSize = 60;
            scalingButton.IconVisible = true;
            scalingButton.IsPressed = false;
            scalingButton.Location = new System.Drawing.Point(5, 6);
            scalingButton.Margin = new Padding(5, 6, 5, 6);
            scalingButton.Name = "scalingButton";
            scalingButton.NormalBorderColor = System.Drawing.Color.DimGray;
            scalingButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            scalingButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            scalingButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            scalingButton.Size = new System.Drawing.Size(159, 48);
            scalingButton.TabIndex = 1;
            scalingButton.Text = "定标";
            scalingButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            scalingButton.UseVisualStyleBackColor = false;
            scalingButton.Click += scalingButton_Click;
            // 
            // magnifierButton
            // 
            magnifierButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            magnifierButton.BorderRadius = 8;
            magnifierButton.BorderSize = 1;
            magnifierButton.ButtonTextColor = System.Drawing.Color.White;
            magnifierButton.CustomEnabled = true;
            magnifierButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            magnifierButton.FlatStyle = FlatStyle.Flat;
            magnifierButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            magnifierButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            magnifierButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            magnifierButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            magnifierButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            magnifierButton.IconImage = null;
            magnifierButton.IconSize = 60;
            magnifierButton.IconVisible = true;
            magnifierButton.IsPressed = false;
            magnifierButton.Location = new System.Drawing.Point(174, 6);
            magnifierButton.Margin = new Padding(5, 6, 5, 6);
            magnifierButton.Name = "magnifierButton";
            magnifierButton.NormalBorderColor = System.Drawing.Color.DimGray;
            magnifierButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            magnifierButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            magnifierButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            magnifierButton.Size = new System.Drawing.Size(159, 48);
            magnifierButton.TabIndex = 4;
            magnifierButton.Text = "放大镜";
            magnifierButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            magnifierButton.UseVisualStyleBackColor = false;
            magnifierButton.Click += magnifierButton_Click;
            // 
            // lineMeasureButton
            // 
            lineMeasureButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            lineMeasureButton.BorderRadius = 8;
            lineMeasureButton.BorderSize = 1;
            lineMeasureButton.ButtonTextColor = System.Drawing.Color.White;
            lineMeasureButton.CustomEnabled = true;
            lineMeasureButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            lineMeasureButton.FlatStyle = FlatStyle.Flat;
            lineMeasureButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            lineMeasureButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            lineMeasureButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            lineMeasureButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            lineMeasureButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            lineMeasureButton.IconImage = null;
            lineMeasureButton.IconSize = 60;
            lineMeasureButton.IconVisible = true;
            lineMeasureButton.IsPressed = false;
            lineMeasureButton.Location = new System.Drawing.Point(5, 66);
            lineMeasureButton.Margin = new Padding(5, 6, 5, 6);
            lineMeasureButton.Name = "lineMeasureButton";
            lineMeasureButton.NormalBorderColor = System.Drawing.Color.DimGray;
            lineMeasureButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            lineMeasureButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            lineMeasureButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            lineMeasureButton.Size = new System.Drawing.Size(159, 48);
            lineMeasureButton.TabIndex = 5;
            lineMeasureButton.Text = "直线测量";
            lineMeasureButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            lineMeasureButton.UseVisualStyleBackColor = false;
            lineMeasureButton.Click += lineMeasureButton_Click;
            // 
            // ellipseMeasureButton
            // 
            ellipseMeasureButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            ellipseMeasureButton.BorderRadius = 8;
            ellipseMeasureButton.BorderSize = 1;
            ellipseMeasureButton.ButtonTextColor = System.Drawing.Color.White;
            ellipseMeasureButton.CustomEnabled = true;
            ellipseMeasureButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            ellipseMeasureButton.FlatStyle = FlatStyle.Flat;
            ellipseMeasureButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            ellipseMeasureButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            ellipseMeasureButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseMeasureButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseMeasureButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            ellipseMeasureButton.IconImage = null;
            ellipseMeasureButton.IconSize = 60;
            ellipseMeasureButton.IconVisible = true;
            ellipseMeasureButton.IsPressed = false;
            ellipseMeasureButton.Location = new System.Drawing.Point(174, 66);
            ellipseMeasureButton.Margin = new Padding(5, 6, 5, 6);
            ellipseMeasureButton.Name = "ellipseMeasureButton";
            ellipseMeasureButton.NormalBorderColor = System.Drawing.Color.DimGray;
            ellipseMeasureButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            ellipseMeasureButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseMeasureButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseMeasureButton.Size = new System.Drawing.Size(159, 48);
            ellipseMeasureButton.TabIndex = 6;
            ellipseMeasureButton.Text = "椭圆形测量";
            ellipseMeasureButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            ellipseMeasureButton.UseVisualStyleBackColor = false;
            ellipseMeasureButton.Click += ellipseMeasureButton_Click;
            // 
            // linesMeasureButton
            // 
            linesMeasureButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            linesMeasureButton.BorderRadius = 8;
            linesMeasureButton.BorderSize = 1;
            linesMeasureButton.ButtonTextColor = System.Drawing.Color.White;
            linesMeasureButton.CustomEnabled = true;
            linesMeasureButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            linesMeasureButton.FlatStyle = FlatStyle.Flat;
            linesMeasureButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            linesMeasureButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            linesMeasureButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            linesMeasureButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            linesMeasureButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            linesMeasureButton.IconImage = null;
            linesMeasureButton.IconSize = 60;
            linesMeasureButton.IconVisible = true;
            linesMeasureButton.IsPressed = false;
            linesMeasureButton.Location = new System.Drawing.Point(5, 126);
            linesMeasureButton.Margin = new Padding(5, 6, 5, 6);
            linesMeasureButton.Name = "linesMeasureButton";
            linesMeasureButton.NormalBorderColor = System.Drawing.Color.DimGray;
            linesMeasureButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            linesMeasureButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            linesMeasureButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            linesMeasureButton.Size = new System.Drawing.Size(159, 48);
            linesMeasureButton.TabIndex = 7;
            linesMeasureButton.Text = "线段测量";
            linesMeasureButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            linesMeasureButton.UseVisualStyleBackColor = false;
            linesMeasureButton.Click += linesMeasureButton_Click;
            // 
            // angleMeasureButton
            // 
            angleMeasureButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            angleMeasureButton.BorderRadius = 8;
            angleMeasureButton.BorderSize = 1;
            angleMeasureButton.ButtonTextColor = System.Drawing.Color.White;
            angleMeasureButton.CustomEnabled = true;
            angleMeasureButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            angleMeasureButton.FlatStyle = FlatStyle.Flat;
            angleMeasureButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            angleMeasureButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            angleMeasureButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            angleMeasureButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            angleMeasureButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            angleMeasureButton.IconImage = null;
            angleMeasureButton.IconSize = 60;
            angleMeasureButton.IconVisible = true;
            angleMeasureButton.IsPressed = false;
            angleMeasureButton.Location = new System.Drawing.Point(174, 126);
            angleMeasureButton.Margin = new Padding(5, 6, 5, 6);
            angleMeasureButton.Name = "angleMeasureButton";
            angleMeasureButton.NormalBorderColor = System.Drawing.Color.DimGray;
            angleMeasureButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            angleMeasureButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            angleMeasureButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            angleMeasureButton.Size = new System.Drawing.Size(159, 48);
            angleMeasureButton.TabIndex = 8;
            angleMeasureButton.Text = "角度测量";
            angleMeasureButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            angleMeasureButton.UseVisualStyleBackColor = false;
            angleMeasureButton.Click += angleMeasureButton_Click;
            // 
            // nsnrButton
            // 
            nsnrButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            nsnrButton.BorderRadius = 8;
            nsnrButton.BorderSize = 1;
            nsnrButton.ButtonTextColor = System.Drawing.Color.White;
            nsnrButton.CustomEnabled = true;
            nsnrButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            nsnrButton.FlatStyle = FlatStyle.Flat;
            nsnrButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            nsnrButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            nsnrButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            nsnrButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            nsnrButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            nsnrButton.IconImage = null;
            nsnrButton.IconSize = 60;
            nsnrButton.IconVisible = true;
            nsnrButton.IsPressed = false;
            nsnrButton.Location = new System.Drawing.Point(5, 186);
            nsnrButton.Margin = new Padding(5, 6, 5, 6);
            nsnrButton.Name = "nsnrButton";
            nsnrButton.NormalBorderColor = System.Drawing.Color.DimGray;
            nsnrButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            nsnrButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            nsnrButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            nsnrButton.Size = new System.Drawing.Size(159, 48);
            nsnrButton.TabIndex = 9;
            nsnrButton.Text = "SNR";
            nsnrButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            nsnrButton.UseVisualStyleBackColor = false;
            nsnrButton.Click += nsnrButton_Click;
            // 
            // duplexIQIButton
            // 
            duplexIQIButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            duplexIQIButton.BorderRadius = 8;
            duplexIQIButton.BorderSize = 1;
            duplexIQIButton.ButtonTextColor = System.Drawing.Color.White;
            duplexIQIButton.CustomEnabled = true;
            duplexIQIButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            duplexIQIButton.FlatStyle = FlatStyle.Flat;
            duplexIQIButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            duplexIQIButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            duplexIQIButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            duplexIQIButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            duplexIQIButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            duplexIQIButton.IconImage = null;
            duplexIQIButton.IconSize = 60;
            duplexIQIButton.IconVisible = true;
            duplexIQIButton.IsPressed = false;
            duplexIQIButton.Location = new System.Drawing.Point(174, 186);
            duplexIQIButton.Margin = new Padding(5, 6, 5, 6);
            duplexIQIButton.Name = "duplexIQIButton";
            duplexIQIButton.NormalBorderColor = System.Drawing.Color.DimGray;
            duplexIQIButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            duplexIQIButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            duplexIQIButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            duplexIQIButton.Size = new System.Drawing.Size(159, 48);
            duplexIQIButton.TabIndex = 10;
            duplexIQIButton.Text = "像质计";
            duplexIQIButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            duplexIQIButton.UseVisualStyleBackColor = false;
            duplexIQIButton.Click += duplexIQIButton_Click;
            // 
            // WallThicknessBtn
            // 
            WallThicknessBtn.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            WallThicknessBtn.BorderRadius = 8;
            WallThicknessBtn.BorderSize = 1;
            WallThicknessBtn.ButtonTextColor = System.Drawing.Color.White;
            WallThicknessBtn.CustomEnabled = true;
            WallThicknessBtn.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            WallThicknessBtn.FlatStyle = FlatStyle.Flat;
            WallThicknessBtn.Font = new System.Drawing.Font("微软雅黑", 9F);
            WallThicknessBtn.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            WallThicknessBtn.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            WallThicknessBtn.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            WallThicknessBtn.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            WallThicknessBtn.IconImage = null;
            WallThicknessBtn.IconSize = 60;
            WallThicknessBtn.IconVisible = true;
            WallThicknessBtn.IsPressed = false;
            WallThicknessBtn.Location = new System.Drawing.Point(5, 246);
            WallThicknessBtn.Margin = new Padding(5, 6, 5, 6);
            WallThicknessBtn.Name = "WallThicknessBtn";
            WallThicknessBtn.NormalBorderColor = System.Drawing.Color.DimGray;
            WallThicknessBtn.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            WallThicknessBtn.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            WallThicknessBtn.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            WallThicknessBtn.Size = new System.Drawing.Size(159, 48);
            WallThicknessBtn.TabIndex = 11;
            WallThicknessBtn.Text = "壁厚测量";
            WallThicknessBtn.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            WallThicknessBtn.UseVisualStyleBackColor = false;
            WallThicknessBtn.Click += WallThicknessBtn_Click;
            // 
            // annotationTitlePanel
            // 
            annotationTitlePanel.Controls.Add(label4);
            annotationTitlePanel.Location = new System.Drawing.Point(13, 1376);
            annotationTitlePanel.Margin = new Padding(3, 4, 3, 4);
            annotationTitlePanel.Name = "annotationTitlePanel";
            annotationTitlePanel.Size = new System.Drawing.Size(357, 40);
            annotationTitlePanel.TabIndex = 12;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.ForeColor = System.Drawing.Color.White;
            label4.Location = new System.Drawing.Point(14, 9);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(69, 20);
            label4.TabIndex = 0;
            label4.Text = "图像标注";
            // 
            // annotationButtonPanel
            // 
            annotationButtonPanel.Controls.Add(pointButton);
            annotationButtonPanel.Controls.Add(circleButton);
            annotationButtonPanel.Controls.Add(polylineButton);
            annotationButtonPanel.Controls.Add(interpolatedButton);
            annotationButtonPanel.Controls.Add(rectangleButton);
            annotationButtonPanel.Controls.Add(ellipseButton);
            annotationButtonPanel.Controls.Add(multilineButton);
            annotationButtonPanel.Controls.Add(rangelineButton);
            annotationButtonPanel.Controls.Add(infinitelineButton);
            annotationButtonPanel.Controls.Add(cutlineButton);
            annotationButtonPanel.Controls.Add(arrowButton);
            annotationButtonPanel.Controls.Add(axisButton);
            annotationButtonPanel.Controls.Add(rulerButton);
            annotationButtonPanel.Controls.Add(crosshairButton);
            annotationButtonPanel.Controls.Add(textButton);
            annotationButtonPanel.Location = new System.Drawing.Point(13, 1422);
            annotationButtonPanel.Margin = new Padding(3, 2, 3, 2);
            annotationButtonPanel.Name = "annotationButtonPanel";
            annotationButtonPanel.Size = new System.Drawing.Size(357, 464);
            annotationButtonPanel.TabIndex = 13;
            // 
            // pointButton
            // 
            pointButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            pointButton.BorderRadius = 8;
            pointButton.BorderSize = 1;
            pointButton.ButtonTextColor = System.Drawing.Color.White;
            pointButton.CustomEnabled = true;
            pointButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            pointButton.FlatAppearance.BorderSize = 0;
            pointButton.FlatStyle = FlatStyle.Flat;
            pointButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            pointButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            pointButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pointButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pointButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            pointButton.IconImage = null;
            pointButton.IconSize = 60;
            pointButton.IconVisible = true;
            pointButton.IsPressed = false;
            pointButton.Location = new System.Drawing.Point(5, 6);
            pointButton.Margin = new Padding(5, 6, 5, 6);
            pointButton.Name = "pointButton";
            pointButton.NormalBorderColor = System.Drawing.Color.DimGray;
            pointButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            pointButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pointButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            pointButton.Size = new System.Drawing.Size(159, 46);
            pointButton.TabIndex = 1;
            pointButton.Text = "点";
            pointButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            pointButton.UseVisualStyleBackColor = false;
            pointButton.Click += pointButton_Click;
            // 
            // circleButton
            // 
            circleButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            circleButton.BorderRadius = 8;
            circleButton.BorderSize = 1;
            circleButton.ButtonTextColor = System.Drawing.Color.White;
            circleButton.CustomEnabled = true;
            circleButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            circleButton.FlatAppearance.BorderSize = 0;
            circleButton.FlatStyle = FlatStyle.Flat;
            circleButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            circleButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            circleButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            circleButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            circleButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            circleButton.IconImage = null;
            circleButton.IconSize = 60;
            circleButton.IconVisible = true;
            circleButton.IsPressed = false;
            circleButton.Location = new System.Drawing.Point(174, 6);
            circleButton.Margin = new Padding(5, 6, 5, 6);
            circleButton.Name = "circleButton";
            circleButton.NormalBorderColor = System.Drawing.Color.DimGray;
            circleButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            circleButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            circleButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            circleButton.Size = new System.Drawing.Size(159, 46);
            circleButton.TabIndex = 2;
            circleButton.Text = "圆";
            circleButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            circleButton.UseVisualStyleBackColor = false;
            circleButton.Click += circleButton_Click;
            // 
            // polylineButton
            // 
            polylineButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            polylineButton.BorderRadius = 8;
            polylineButton.BorderSize = 1;
            polylineButton.ButtonTextColor = System.Drawing.Color.White;
            polylineButton.CustomEnabled = true;
            polylineButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            polylineButton.FlatAppearance.BorderSize = 0;
            polylineButton.FlatStyle = FlatStyle.Flat;
            polylineButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            polylineButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            polylineButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            polylineButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            polylineButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            polylineButton.IconImage = null;
            polylineButton.IconSize = 60;
            polylineButton.IconVisible = true;
            polylineButton.IsPressed = false;
            polylineButton.Location = new System.Drawing.Point(5, 64);
            polylineButton.Margin = new Padding(5, 6, 5, 6);
            polylineButton.Name = "polylineButton";
            polylineButton.NormalBorderColor = System.Drawing.Color.DimGray;
            polylineButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            polylineButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            polylineButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            polylineButton.Size = new System.Drawing.Size(159, 46);
            polylineButton.TabIndex = 3;
            polylineButton.Text = "折线";
            polylineButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            polylineButton.UseVisualStyleBackColor = false;
            polylineButton.Click += polylineButton_Click;
            // 
            // interpolatedButton
            // 
            interpolatedButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            interpolatedButton.BorderRadius = 8;
            interpolatedButton.BorderSize = 1;
            interpolatedButton.ButtonTextColor = System.Drawing.Color.White;
            interpolatedButton.CustomEnabled = true;
            interpolatedButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            interpolatedButton.FlatAppearance.BorderSize = 0;
            interpolatedButton.FlatStyle = FlatStyle.Flat;
            interpolatedButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            interpolatedButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            interpolatedButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            interpolatedButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            interpolatedButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            interpolatedButton.IconImage = null;
            interpolatedButton.IconSize = 60;
            interpolatedButton.IconVisible = true;
            interpolatedButton.IsPressed = false;
            interpolatedButton.Location = new System.Drawing.Point(174, 64);
            interpolatedButton.Margin = new Padding(5, 6, 5, 6);
            interpolatedButton.Name = "interpolatedButton";
            interpolatedButton.NormalBorderColor = System.Drawing.Color.DimGray;
            interpolatedButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            interpolatedButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            interpolatedButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            interpolatedButton.Size = new System.Drawing.Size(159, 46);
            interpolatedButton.TabIndex = 4;
            interpolatedButton.Text = "插值";
            interpolatedButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            interpolatedButton.UseVisualStyleBackColor = false;
            interpolatedButton.Click += interpolatedButton_Click;
            // 
            // rectangleButton
            // 
            rectangleButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rectangleButton.BorderRadius = 8;
            rectangleButton.BorderSize = 1;
            rectangleButton.ButtonTextColor = System.Drawing.Color.White;
            rectangleButton.CustomEnabled = true;
            rectangleButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            rectangleButton.FlatAppearance.BorderSize = 0;
            rectangleButton.FlatStyle = FlatStyle.Flat;
            rectangleButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            rectangleButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rectangleButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rectangleButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rectangleButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            rectangleButton.IconImage = null;
            rectangleButton.IconSize = 60;
            rectangleButton.IconVisible = true;
            rectangleButton.IsPressed = false;
            rectangleButton.Location = new System.Drawing.Point(5, 122);
            rectangleButton.Margin = new Padding(5, 6, 5, 6);
            rectangleButton.Name = "rectangleButton";
            rectangleButton.NormalBorderColor = System.Drawing.Color.DimGray;
            rectangleButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rectangleButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rectangleButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rectangleButton.Size = new System.Drawing.Size(159, 46);
            rectangleButton.TabIndex = 5;
            rectangleButton.Text = "矩形";
            rectangleButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            rectangleButton.UseVisualStyleBackColor = false;
            rectangleButton.Click += rectangleButton_Click;
            // 
            // ellipseButton
            // 
            ellipseButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            ellipseButton.BorderRadius = 8;
            ellipseButton.BorderSize = 1;
            ellipseButton.ButtonTextColor = System.Drawing.Color.White;
            ellipseButton.CustomEnabled = true;
            ellipseButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            ellipseButton.FlatAppearance.BorderSize = 0;
            ellipseButton.FlatStyle = FlatStyle.Flat;
            ellipseButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            ellipseButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            ellipseButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            ellipseButton.IconImage = null;
            ellipseButton.IconSize = 60;
            ellipseButton.IconVisible = true;
            ellipseButton.IsPressed = false;
            ellipseButton.Location = new System.Drawing.Point(174, 122);
            ellipseButton.Margin = new Padding(5, 6, 5, 6);
            ellipseButton.Name = "ellipseButton";
            ellipseButton.NormalBorderColor = System.Drawing.Color.DimGray;
            ellipseButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            ellipseButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            ellipseButton.Size = new System.Drawing.Size(159, 46);
            ellipseButton.TabIndex = 6;
            ellipseButton.Text = "椭圆";
            ellipseButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            ellipseButton.UseVisualStyleBackColor = false;
            ellipseButton.Click += ellipseButton_Click;
            // 
            // multilineButton
            // 
            multilineButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            multilineButton.BorderRadius = 8;
            multilineButton.BorderSize = 1;
            multilineButton.ButtonTextColor = System.Drawing.Color.White;
            multilineButton.CustomEnabled = true;
            multilineButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            multilineButton.FlatAppearance.BorderSize = 0;
            multilineButton.FlatStyle = FlatStyle.Flat;
            multilineButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            multilineButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            multilineButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            multilineButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            multilineButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            multilineButton.IconImage = null;
            multilineButton.IconSize = 60;
            multilineButton.IconVisible = true;
            multilineButton.IsPressed = false;
            multilineButton.Location = new System.Drawing.Point(5, 180);
            multilineButton.Margin = new Padding(5, 6, 5, 6);
            multilineButton.Name = "multilineButton";
            multilineButton.NormalBorderColor = System.Drawing.Color.DimGray;
            multilineButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            multilineButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            multilineButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            multilineButton.Size = new System.Drawing.Size(159, 46);
            multilineButton.TabIndex = 7;
            multilineButton.Text = "多行";
            multilineButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            multilineButton.UseVisualStyleBackColor = false;
            multilineButton.Click += multilineButton_Click;
            // 
            // rangelineButton
            // 
            rangelineButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rangelineButton.BorderRadius = 8;
            rangelineButton.BorderSize = 1;
            rangelineButton.ButtonTextColor = System.Drawing.Color.White;
            rangelineButton.CustomEnabled = true;
            rangelineButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            rangelineButton.FlatAppearance.BorderSize = 0;
            rangelineButton.FlatStyle = FlatStyle.Flat;
            rangelineButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            rangelineButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rangelineButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rangelineButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rangelineButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            rangelineButton.IconImage = null;
            rangelineButton.IconSize = 60;
            rangelineButton.IconVisible = true;
            rangelineButton.IsPressed = false;
            rangelineButton.Location = new System.Drawing.Point(174, 180);
            rangelineButton.Margin = new Padding(5, 6, 5, 6);
            rangelineButton.Name = "rangelineButton";
            rangelineButton.NormalBorderColor = System.Drawing.Color.DimGray;
            rangelineButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rangelineButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rangelineButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rangelineButton.Size = new System.Drawing.Size(159, 46);
            rangelineButton.TabIndex = 8;
            rangelineButton.Text = "测量线";
            rangelineButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            rangelineButton.UseVisualStyleBackColor = false;
            rangelineButton.Click += rangelineButton_Click;
            // 
            // infinitelineButton
            // 
            infinitelineButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            infinitelineButton.BorderRadius = 8;
            infinitelineButton.BorderSize = 1;
            infinitelineButton.ButtonTextColor = System.Drawing.Color.White;
            infinitelineButton.CustomEnabled = true;
            infinitelineButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            infinitelineButton.FlatAppearance.BorderSize = 0;
            infinitelineButton.FlatStyle = FlatStyle.Flat;
            infinitelineButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            infinitelineButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            infinitelineButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            infinitelineButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            infinitelineButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            infinitelineButton.IconImage = null;
            infinitelineButton.IconSize = 60;
            infinitelineButton.IconVisible = true;
            infinitelineButton.IsPressed = false;
            infinitelineButton.Location = new System.Drawing.Point(5, 238);
            infinitelineButton.Margin = new Padding(5, 6, 5, 6);
            infinitelineButton.Name = "infinitelineButton";
            infinitelineButton.NormalBorderColor = System.Drawing.Color.DimGray;
            infinitelineButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            infinitelineButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            infinitelineButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            infinitelineButton.Size = new System.Drawing.Size(159, 46);
            infinitelineButton.TabIndex = 9;
            infinitelineButton.Text = "无限线";
            infinitelineButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            infinitelineButton.UseVisualStyleBackColor = false;
            infinitelineButton.Click += infinitelineButton_Click;
            // 
            // cutlineButton
            // 
            cutlineButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            cutlineButton.BorderRadius = 8;
            cutlineButton.BorderSize = 1;
            cutlineButton.ButtonTextColor = System.Drawing.Color.White;
            cutlineButton.CustomEnabled = true;
            cutlineButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            cutlineButton.FlatAppearance.BorderSize = 0;
            cutlineButton.FlatStyle = FlatStyle.Flat;
            cutlineButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            cutlineButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            cutlineButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            cutlineButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            cutlineButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            cutlineButton.IconImage = null;
            cutlineButton.IconSize = 60;
            cutlineButton.IconVisible = true;
            cutlineButton.IsPressed = false;
            cutlineButton.Location = new System.Drawing.Point(174, 238);
            cutlineButton.Margin = new Padding(5, 6, 5, 6);
            cutlineButton.Name = "cutlineButton";
            cutlineButton.NormalBorderColor = System.Drawing.Color.DimGray;
            cutlineButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            cutlineButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            cutlineButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            cutlineButton.Size = new System.Drawing.Size(159, 46);
            cutlineButton.TabIndex = 10;
            cutlineButton.Text = "切割线";
            cutlineButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            cutlineButton.UseVisualStyleBackColor = false;
            cutlineButton.Click += cutlineButton_Click;
            // 
            // arrowButton
            // 
            arrowButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            arrowButton.BorderRadius = 8;
            arrowButton.BorderSize = 1;
            arrowButton.ButtonTextColor = System.Drawing.Color.White;
            arrowButton.CustomEnabled = true;
            arrowButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            arrowButton.FlatAppearance.BorderSize = 0;
            arrowButton.FlatStyle = FlatStyle.Flat;
            arrowButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            arrowButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            arrowButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            arrowButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            arrowButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            arrowButton.IconImage = null;
            arrowButton.IconSize = 60;
            arrowButton.IconVisible = true;
            arrowButton.IsPressed = false;
            arrowButton.Location = new System.Drawing.Point(5, 296);
            arrowButton.Margin = new Padding(5, 6, 5, 6);
            arrowButton.Name = "arrowButton";
            arrowButton.NormalBorderColor = System.Drawing.Color.DimGray;
            arrowButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            arrowButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            arrowButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            arrowButton.Size = new System.Drawing.Size(159, 46);
            arrowButton.TabIndex = 11;
            arrowButton.Text = "箭头";
            arrowButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            arrowButton.UseVisualStyleBackColor = false;
            arrowButton.Click += arrowButton_Click;
            // 
            // axisButton
            // 
            axisButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            axisButton.BorderRadius = 8;
            axisButton.BorderSize = 1;
            axisButton.ButtonTextColor = System.Drawing.Color.White;
            axisButton.CustomEnabled = true;
            axisButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            axisButton.FlatAppearance.BorderSize = 0;
            axisButton.FlatStyle = FlatStyle.Flat;
            axisButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            axisButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            axisButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            axisButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            axisButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            axisButton.IconImage = null;
            axisButton.IconSize = 60;
            axisButton.IconVisible = true;
            axisButton.IsPressed = false;
            axisButton.Location = new System.Drawing.Point(174, 296);
            axisButton.Margin = new Padding(5, 6, 5, 6);
            axisButton.Name = "axisButton";
            axisButton.NormalBorderColor = System.Drawing.Color.DimGray;
            axisButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            axisButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            axisButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            axisButton.Size = new System.Drawing.Size(159, 46);
            axisButton.TabIndex = 12;
            axisButton.Text = "基准线";
            axisButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            axisButton.UseVisualStyleBackColor = false;
            axisButton.Click += axisButton_Click;
            // 
            // rulerButton
            // 
            rulerButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rulerButton.BorderRadius = 8;
            rulerButton.BorderSize = 1;
            rulerButton.ButtonTextColor = System.Drawing.Color.White;
            rulerButton.CustomEnabled = true;
            rulerButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            rulerButton.FlatAppearance.BorderSize = 0;
            rulerButton.FlatStyle = FlatStyle.Flat;
            rulerButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            rulerButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rulerButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rulerButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rulerButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            rulerButton.IconImage = null;
            rulerButton.IconSize = 60;
            rulerButton.IconVisible = true;
            rulerButton.IsPressed = false;
            rulerButton.Location = new System.Drawing.Point(5, 354);
            rulerButton.Margin = new Padding(5, 6, 5, 6);
            rulerButton.Name = "rulerButton";
            rulerButton.NormalBorderColor = System.Drawing.Color.DimGray;
            rulerButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            rulerButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rulerButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            rulerButton.Size = new System.Drawing.Size(159, 46);
            rulerButton.TabIndex = 13;
            rulerButton.Text = "尺子";
            rulerButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            rulerButton.UseVisualStyleBackColor = false;
            rulerButton.Click += rulerButton_Click;
            // 
            // crosshairButton
            // 
            crosshairButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            crosshairButton.BorderRadius = 8;
            crosshairButton.BorderSize = 1;
            crosshairButton.ButtonTextColor = System.Drawing.Color.White;
            crosshairButton.CustomEnabled = true;
            crosshairButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            crosshairButton.FlatAppearance.BorderSize = 0;
            crosshairButton.FlatStyle = FlatStyle.Flat;
            crosshairButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            crosshairButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            crosshairButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            crosshairButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            crosshairButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            crosshairButton.IconImage = null;
            crosshairButton.IconSize = 60;
            crosshairButton.IconVisible = true;
            crosshairButton.IsPressed = false;
            crosshairButton.Location = new System.Drawing.Point(174, 354);
            crosshairButton.Margin = new Padding(5, 6, 5, 6);
            crosshairButton.Name = "crosshairButton";
            crosshairButton.NormalBorderColor = System.Drawing.Color.DimGray;
            crosshairButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            crosshairButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            crosshairButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            crosshairButton.Size = new System.Drawing.Size(159, 46);
            crosshairButton.TabIndex = 14;
            crosshairButton.Text = "十字线";
            crosshairButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            crosshairButton.UseVisualStyleBackColor = false;
            crosshairButton.Click += crosshairButton_Click;
            // 
            // textButton
            // 
            textButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            textButton.BorderRadius = 8;
            textButton.BorderSize = 1;
            textButton.ButtonTextColor = System.Drawing.Color.White;
            textButton.CustomEnabled = true;
            textButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            textButton.FlatAppearance.BorderSize = 0;
            textButton.FlatStyle = FlatStyle.Flat;
            textButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            textButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            textButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            textButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            textButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            textButton.IconImage = null;
            textButton.IconSize = 60;
            textButton.IconVisible = true;
            textButton.IsPressed = false;
            textButton.Location = new System.Drawing.Point(5, 412);
            textButton.Margin = new Padding(5, 6, 5, 6);
            textButton.Name = "textButton";
            textButton.NormalBorderColor = System.Drawing.Color.DimGray;
            textButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            textButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            textButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            textButton.Size = new System.Drawing.Size(159, 46);
            textButton.TabIndex = 15;
            textButton.Text = "文字";
            textButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            textButton.UseVisualStyleBackColor = false;
            textButton.Click += textButton_Click;
            // 
            // roundPanel1
            // 
            roundPanel1.AutoScroll = true;
            roundPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundPanel1.Controls.Add(splitContainer1);
            roundPanel1.CornerRadius = 12;
            roundPanel1.Dock = DockStyle.Fill;
            roundPanel1.Location = new System.Drawing.Point(21, 0);
            roundPanel1.Margin = new Padding(3, 4, 3, 4);
            roundPanel1.Name = "roundPanel1";
            roundPanel1.Size = new System.Drawing.Size(381, 1374);
            roundPanel1.TabIndex = 14;
            // 
            // ToolsPanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(20, 20, 20);
            Controls.Add(roundPanel1);
            Margin = new Padding(3, 4, 3, 4);
            Name = "ToolsPanel";
            Padding = new Padding(21, 0, 21, 38);
            Size = new System.Drawing.Size(423, 1412);
            splitContainer1.Panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer1).EndInit();
            splitContainer1.ResumeLayout(false);
            flowLayoutPanel1.ResumeLayout(false);
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            flowLayoutPanel2.ResumeLayout(false);
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            flowLayoutPanel3.ResumeLayout(false);
            enhancementTitlePanel.ResumeLayout(false);
            enhancementTitlePanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox5).EndInit();
            enhancementButtonPanel.ResumeLayout(false);
            measureTitlePanel.ResumeLayout(false);
            measureTitlePanel.PerformLayout();
            measureButtonPanel.ResumeLayout(false);
            annotationTitlePanel.ResumeLayout(false);
            annotationTitlePanel.PerformLayout();
            annotationButtonPanel.ResumeLayout(false);
            roundPanel1.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        private RoundPanel roundPanel1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        public RoundButton openProjectButton;
        public RoundButton importImageButton;
        public RoundButton screenshotButton;
        public RoundButton panButton;
        public RoundButton zoomButton;
        public RoundButton wwwlButton;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel3;
        public RoundButton regionButton;
        public RoundButton clockwiseButton;
        public RoundButton counterclockwiseButton;
        public RoundButton verticalButton;
        public RoundButton horizontalButton;
        public RoundButton negativeButton;
        public RoundButton scalingButton;
        public RoundButton magnifierButton;
        public RoundButton lineMeasureButton;
        public RoundButton ellipseMeasureButton;
        public RoundButton linesMeasureButton;
        public RoundButton angleMeasureButton;
        public RoundButton nsnrButton;
        public RoundButton pointButton;
        public RoundButton circleButton;
        public RoundButton polylineButton;
        public RoundButton interpolatedButton;
        public RoundButton rectangleButton;
        public RoundButton ellipseButton;
        public RoundButton multilineButton;
        public RoundButton rangelineButton;
        public RoundButton infinitelineButton;
        public RoundButton cutlineButton;
        public RoundButton arrowButton;
        public RoundButton axisButton;
        public RoundButton rulerButton;
        public RoundButton crosshairButton;
        public RoundButton textButton;
        public System.Windows.Forms.SplitContainer splitContainer1;
        public RoundButton duplexIQIButton;
        public RoundButton exportImageButton;
        public Panel measureTitlePanel;
        public Panel annotationTitlePanel;
        public FlowLayoutPanel measureButtonPanel;
        public FlowLayoutPanel annotationButtonPanel;
        public RoundButton resetButton;
        private PictureBox pictureBox5;
        private Label label5;
        public RoundButton enhanceBtn1;
        public RoundButton enhanceBtn2;
        public RoundButton enhanceBtn3;
        public RoundButton noiseBtn1;
        public RoundButton noiseBtn2;
        public RoundButton noiseBtn3;
        public RoundButton pseudoColorBtn;
        public RoundButton rilievoBtn;
        public RoundButton WallThicknessBtn;
        public FlowLayoutPanel enhancementButtonPanel;
        public Panel enhancementTitlePanel;
    }
}
