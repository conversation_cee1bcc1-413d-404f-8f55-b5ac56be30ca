using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Timers;
using System.Management;
using System.Diagnostics;
using System.IO;
using License; // 添加License命名空间的引用

namespace InduVision.LkControls.Utils
{
    // 添加简单的设置类
internal static class AppSettings
{
    public static string LicenseServerUrl => Properties.Settings.Default.LicenseServerUrl;
    public static string LicenseAppKey => Properties.Settings.Default.LicenseAppKey;
}

    public class LicenseReporter
    {
        private readonly string _serverUrl;
        private readonly string _appKey;
        private readonly Timer _reportTimer;
        private readonly HttpClient _httpClient;
        private static LicenseReporter _instance;
        private static readonly string _logFile = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "InduVision", "LicenseReporter.log");
        
        // 授权文件名称常量
        private const string LICENSE_FILE_NAME = "license.license";

        // 单例模式
        public static LicenseReporter Instance
        {
            get
            {
                if (_instance == null)
                {
                    // 验证授权状态
                    LogStaticMessage($"初始化LicenseReporter实例前验证授权状态:");
                    LogStaticMessage($"Licensed = {License.Status.Licensed}");
                    
                    // 检查授权文件是否存在
                    string licenseFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, LICENSE_FILE_NAME);
                    bool fileExists = File.Exists(licenseFilePath);
                    LogStaticMessage($"授权文件 {licenseFilePath} 存在: {fileExists}");
                    
                    // 如果存在授权文件但未授权，这可能是因为未保护应用程序
                    if (fileExists && !License.Status.Licensed)
                    {
                        LogStaticMessage($"警告: 授权文件存在但License.Status.Licensed=False，应用程序可能未被.NET Reactor保护");
                    }
                    
                    _instance = new LicenseReporter(
                        AppSettings.LicenseServerUrl,
                        AppSettings.LicenseAppKey
                    );
                    
                    LogStaticMessage("初始化完成");
                }
                return _instance;
            }
        }

        public LicenseReporter(string serverUrl, string appKey)
        {
            _serverUrl = serverUrl;
            _appKey = appKey;
            _httpClient = new HttpClient();

            // 确保日志目录存在
            var logDir = Path.GetDirectoryName(_logFile);
            if (!Directory.Exists(logDir))
                Directory.CreateDirectory(logDir);

            LogMessage($"LicenseReporter 初始化, 服务器URL: {serverUrl}");

            // 创建一个定时器，每24小时上报一次授权状态
            _reportTimer = new Timer(24 * 60 * 60 * 1000); // 24小时
            _reportTimer.Elapsed += async (s, e) => await ReportLicenseStatusAsync();
        }

        public void Start()
        {
            LogMessage("授权状态上报服务启动中...");

            // 启动时立即上报一次
            Task.Run(async () => await ReportLicenseStatusAsync());

            // 启动定时上报
            _reportTimer.Start();
            LogMessage("授权状态上报服务已启动，每24小时上报一次");
        }

        public void Stop()
        {
            _reportTimer.Stop();
            LogMessage("授权状态上报服务已停止");
        }

        public async Task ReportLicenseStatusAsync()
        {
            try
            {
                LogMessage("开始收集授权状态信息...");

                var licenseStatus = CollectLicenseStatus();

                // 如果没有授权，显示警告但继续上报
                if (!licenseStatus.IsLicensed)
                {
                    LogMessage("警告：授权状态为未授权(Licensed=False)，这可能导致服务器无法识别客户端", isError: true);
                }

                LogMessage($"授权状态已收集: 机器ID={licenseStatus.MachineId}, 机器名={licenseStatus.MachineName}, " +
                          $"已授权={licenseStatus.IsLicensed}, 硬件ID={licenseStatus.HardwareId}");

                LogMessage($"即将上报到服务器: {_serverUrl}/license/report");

                await SendToServerAsync(licenseStatus);
                LogMessage("授权状态上报成功！");
            }
            catch (Exception ex)
            {
                // 记录错误，但不中断正常使用
                LogMessage($"上报授权状态失败: {ex.Message}", isError: true);
                if (ex.InnerException != null)
                {
                    LogMessage($"内部错误: {ex.InnerException.Message}", isError: true);
                }
                LogMessage($"堆栈跟踪: {ex.StackTrace}", isError: true);
            }
        }

        private LicenseStatusReport CollectLicenseStatus()
        {
            LogMessage("正在收集License信息...");

            // 添加检查授权文件位置的日志
            LogMessage("======== 开始检查授权文件 ========");
            
            // 授权文件位置固定为当前进程同级目录下的license.license
            string appDir = AppDomain.CurrentDomain.BaseDirectory;
            string licenseFilePath = Path.Combine(appDir, LICENSE_FILE_NAME);
            
            LogMessage($"授权文件固定位置: {licenseFilePath}");
            
            // 检查固定位置的授权文件是否存在
            bool licenseFileExists = File.Exists(licenseFilePath);
            if (!licenseFileExists)
            {
                LogMessage($"警告: 固定位置的授权文件不存在: {licenseFilePath}", true);
            }
            else
            {
                LogMessage($"找到授权文件: {licenseFilePath}");
                
                // 检查授权状态
                bool isLicensed = License.Status.Licensed;
                string hardwareId = License.Status.HardwareID;
                string licenseHardwareId = License.Status.License_HardwareID;
                
                if (isLicensed)
                {
                    LogMessage($"授权状态检查: Licensed=True, HardwareID={hardwareId}, License_HardwareID={licenseHardwareId}");
                }
                else
                {
                    // 检查是否是因为未保护应用程序
                    if (hardwareId.Contains("To get a hardware ID please protect your assembly with .NET Reactor"))
                    {
                        LogMessage($"警告: 应用程序未被.NET Reactor保护，无法正确验证授权", true);
                    }
                    else
                    {
                        LogMessage($"授权状态检查: Licensed=False, HardwareID={hardwareId}, License_HardwareID={licenseHardwareId}");
                    }
                }
                
                // 检查过期日期
                if (License.Status.Expiration_Date_Lock_Enable)
                {
                    DateTime expDate = License.Status.Expiration_Date;
                    bool isExpired = DateTime.Now > expDate;
                    LogMessage($"过期日期: {expDate.ToString("yyyy-MM-dd")} (已过期: {isExpired})");
                }
                
                // 检查硬件锁状态
                if (License.Status.Hardware_Lock_Enabled)
                {
                    bool hwMatch = License.Status.HardwareID == License.Status.License_HardwareID;
                    LogMessage($"硬件锁状态: 启用, 硬件ID匹配: {hwMatch}");
                    LogMessage($"当前硬件ID: {License.Status.HardwareID}");
                    LogMessage($"授权硬件ID: {License.Status.License_HardwareID}");
                }
                else
                {
                    LogMessage($"硬件锁状态: 未启用");
                }
                
                // 读取KeyValueList (只在授权有效时才有意义)
                if (isLicensed && License.Status.KeyValueList != null)
                {
                    LogMessage($"KeyValueList项数: {License.Status.KeyValueList.Count}");
                    for (int i = 0; i < License.Status.KeyValueList.Count; i++)
                    {
                        try
                        {
                            string key = License.Status.KeyValueList.GetKey(i).ToString();
                            string value = License.Status.KeyValueList.GetByIndex(i).ToString();
                            LogMessage($"  KeyValueList[{i}]: {key} = {value}");
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"  读取KeyValueList[{i}]失败: {ex.Message}", true);
                        }
                    }
                }
                else if (License.Status.KeyValueList == null)
                {
                    LogMessage("KeyValueList不可用");
                }
            }
            
            LogMessage("======== 授权文件检查完成 ========");

            // 简化的授权状态检查
            bool deviceInfoIsLicensed = false;
            string deviceInfoHardwareId = string.Empty;
            try {
                deviceInfoHardwareId = DeviceInfo.GetHardwareId();
                deviceInfoIsLicensed = DeviceInfo.IsValidLicenseAvailable();
                LogMessage($"DeviceInfo授权状态: Licensed={deviceInfoIsLicensed}, HardwareId={deviceInfoHardwareId}");
            }
            catch (Exception ex) {
                LogMessage($"获取DeviceInfo信息失败: {ex.Message}", true);
            }

            // 综合检查授权状态
            bool isReallyLicensed = License.Status.Licensed;
            bool hardwareMatchIfRequired = !License.Status.Hardware_Lock_Enabled || 
                                         (License.Status.GetHardwareID(true, true, true, false) == License.Status.License_HardwareID);
            bool checkExpirationDate = !License.Status.Expiration_Date_Lock_Enable || 
                                     (License.Status.Expiration_Date > DateTime.Now);
            
            // 最终授权状态 = 基本授权 && 硬件匹配(如果启用) && 没有过期(如果启用)
            bool finalLicenseStatus = isReallyLicensed && hardwareMatchIfRequired && checkExpirationDate;
            LogMessage($"最终综合授权状态: {finalLicenseStatus}");
            
            var status = new LicenseStatusReport
            {
                // 客户端标识信息
                MachineId = GetMachineId(),
                MachineName = Environment.MachineName,
                IpAddress = GetIpAddress(),
                WindowsVersion = Environment.OSVersion.ToString(),
                ApplicationVersion = GetApplicationVersion(),
                ReportTime = DateTime.UtcNow,

                // 授权状态信息
                IsLicensed = finalLicenseStatus,
                HardwareId = string.IsNullOrEmpty(deviceInfoHardwareId) ? License.Status.GetHardwareID(true, true, true, false) : deviceInfoHardwareId,
                LicenseHardwareId = License.Status.License_HardwareID,
                
                // 评估锁状态
                EvaluationLockEnabled = License.Status.Evaluation_Lock_Enabled,
                EvaluationType = License.Status.Evaluation_Type.ToString(),
                EvaluationTime = License.Status.Evaluation_Time,
                EvaluationTimeCurrent = License.Status.Evaluation_Time_Current,

                // 到期日期锁定状态
                ExpirationDateLockEnabled = License.Status.Expiration_Date_Lock_Enable,
                ExpirationDate = License.Status.Expiration_Date,

                // 使用次数锁定状态
                NumberOfUsesLockEnabled = License.Status.Number_Of_Uses_Lock_Enable,
                MaxUses = License.Status.Number_Of_Uses,
                CurrentUses = License.Status.Number_Of_Uses_Current,

                // 实例数量锁定状态
                NumberOfInstancesLockEnabled = License.Status.Number_Of_Instances_Lock_Enable,
                MaxInstances = License.Status.Number_Of_Instances,

                // 硬件锁状态
                HardwareLockEnabled = License.Status.Hardware_Lock_Enabled,

                // 附加信息
                AdditionalInfo = GetAdditionalInfo()
            };

            if (License.Status.Licensed != deviceInfoIsLicensed)
            {
                LogMessage($"警告: License.Status.Licensed与DeviceInfo.IsValidLicenseAvailable()返回值不一致!", true);
                LogMessage($"可能原因: 授权文件(license.license)的访问或验证方式不一致，或存在自定义授权验证逻辑", true);
            }
            
            // 检查是否使用了正确的硬件ID
            if (status.IsLicensed && status.HardwareId != status.LicenseHardwareId && License.Status.Hardware_Lock_Enabled)
            {
                LogMessage($"警告：硬件ID ({status.HardwareId}) 与许可证硬件ID ({status.LicenseHardwareId}) 不匹配且硬件锁已启用", true);
            }

            LogMessage("License信息收集完成");
            return status;
        }

        private async Task SendToServerAsync(LicenseStatusReport report)
        {
            var json = JsonSerializer.Serialize(report);
            LogMessage($"已将授权状态序列化为JSON, 长度: {json.Length} 字符");

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // 添加认证头
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("X-App-Key", _appKey);
            LogMessage($"已添加认证头 X-App-Key: {_appKey.Substring(0, 3)}***{_appKey.Substring(_appKey.Length - 3)}");

            // 发送请求
            LogMessage($"正在发送POST请求到 {_serverUrl}/license/report");
            var requestTime = DateTime.Now;
            var response = await _httpClient.PostAsync($"{_serverUrl}/license/report", content);
            var responseTime = DateTime.Now;
            var elapsed = (responseTime - requestTime).TotalMilliseconds;

            // 记录响应
            LogMessage($"收到服务器响应，状态码: {(int)response.StatusCode} {response.StatusCode}, 耗时: {elapsed}ms");

            // 读取响应内容
            var responseBody = await response.Content.ReadAsStringAsync();
            LogMessage($"响应内容: {responseBody}");

            // 确保请求成功
            response.EnsureSuccessStatusCode();
            LogMessage("请求成功，服务器已接收授权信息");
        }

        private string GetMachineId()
        {
            // 简化的获取硬件ID逻辑
            LogMessage("获取机器ID...");
            
            string finalHardwareId;
            
            try 
            {
                // 优先使用DeviceInfo.GetHardwareId()
                finalHardwareId = DeviceInfo.GetHardwareId();
                if (!string.IsNullOrEmpty(finalHardwareId) && 
                    !finalHardwareId.Contains("To get a hardware ID please protect your assembly with .NET Reactor"))
                {
                    LogMessage($"使用DeviceInfo.GetHardwareId()作为机器ID: '{finalHardwareId}'");
                    return finalHardwareId;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"DeviceInfo.GetHardwareId()失败: {ex.Message}", true);
            }
            
            try
            {
                // 尝试使用License.Status API
                finalHardwareId = License.Status.GetHardwareID(true, true, true, false);
                if (!string.IsNullOrEmpty(finalHardwareId) && 
                    !finalHardwareId.Contains("To get a hardware ID please protect your assembly with .NET Reactor"))
                {
                    LogMessage($"使用License.Status.GetHardwareID作为机器ID: '{finalHardwareId}'");
                    return finalHardwareId;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"License.Status.GetHardwareID失败: {ex.Message}", true);
            }
            
            // 如果上述方法都失败，则使用机器名
            finalHardwareId = Environment.MachineName;
            LogMessage($"使用机器名作为机器ID: '{finalHardwareId}'");
            return finalHardwareId;
        }

        private string GetIpAddress()
        {
            // 获取IP地址
            try
            {
                var ip = System.Net.Dns.GetHostAddresses(System.Net.Dns.GetHostName())
                    .FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)?.ToString() ?? "未知";
                LogMessage($"获取到IP地址: {ip}");
                return ip;
            }
            catch (Exception ex)
            {
                LogMessage($"无法获取IP地址: {ex.Message}", isError: true);
                return "未知";
            }
        }

        private string GetApplicationVersion()
        {
            // 获取应用程序版本
            try
            {
                var version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
                LogMessage($"获取到应用程序版本: {version}");
                return version;
            }
            catch (Exception ex)
            {
                LogMessage($"无法获取应用程序版本: {ex.Message}", isError: true);
                return "未知";
            }
        }

        private Dictionary<string, string> GetAdditionalInfo()
        {
            var info = new Dictionary<string, string>();
            
            // 只有在成功授权情况下才尝试读取KeyValueList
            if (License.Status.Licensed && License.Status.KeyValueList != null)
            {
                LogMessage("许可证文件有效，开始读取附加信息");
                for (int i = 0; i < License.Status.KeyValueList.Count; i++)
                {
                    try
                    {
                        string key = License.Status.KeyValueList.GetKey(i).ToString();
                        string value = License.Status.KeyValueList.GetByIndex(i).ToString();
                        info[key] = value;
                        LogMessage($"附加信息 #{i}: {key} = {value}");
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"获取附加信息项#{i}时出错: {ex.Message}", isError: true);
                    }
                }
            }
            else if (!License.Status.Licensed)
            {
                LogMessage("许可证文件无效，无法获取附加信息");
            }

            // 添加基本信息，以便于服务器端诊断
            try {
                info["_IsLicensed"] = License.Status.Licensed.ToString();
                info["_HardwareID"] = License.Status.GetHardwareID(true, true, true, false);
                info["_License_HardwareID"] = License.Status.License_HardwareID;
                info["_Hardware_Lock_Enabled"] = License.Status.Hardware_Lock_Enabled.ToString();
                info["_LicenseFileExists"] = File.Exists(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, LICENSE_FILE_NAME)).ToString();
                
                // 如果启用了过期日期锁定，添加过期日期
                if (License.Status.Expiration_Date_Lock_Enable)
                {
                    info["_Expiration_Date"] = License.Status.Expiration_Date.ToString("yyyy-MM-dd");
                }
            }
            catch (Exception ex) {
                LogMessage($"添加基本信息失败: {ex.Message}", isError: true);
            }

            return info;
        }

        // 统一日志记录方法
        private void LogMessage(string message, bool isError = false)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logLevel = isError ? "ERROR" : "INFO";
            var logMessage = $"[{timestamp}] [{logLevel}] {message}";

            // 输出到调试窗口
            Debug.WriteLine(logMessage);

            // 写入日志文件
            try
            {
                File.AppendAllText(_logFile, logMessage + Environment.NewLine);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"写入日志文件失败: {ex.Message}");
            }
        }

        // 静态日志方法，用于在实例创建前记录日志
        private static void LogStaticMessage(string message)
        {
            var logFile = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "InduVision", "LicenseReporter.log");

            var logDir = Path.GetDirectoryName(logFile);
            if (!Directory.Exists(logDir))
                Directory.CreateDirectory(logDir);

            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var logMessage = $"[{timestamp}] [STATIC] {message}";
                File.AppendAllText(logFile, logMessage + Environment.NewLine);
                Debug.WriteLine(logMessage);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"静态日志记录失败: {ex.Message}");
            }
        }

        // 设置HTTP请求超时时间
        public void SetHttpTimeout(TimeSpan timeout)
        {
            if (_httpClient != null)
            {
                _httpClient.Timeout = timeout;
                LogMessage($"已设置HTTP请求超时时间为: {timeout.TotalSeconds}秒");
            }
        }

        // 公共静态方法，详细检查授权状态
        public static void CheckLicenseStatus()
        {
            LogStaticMessage("======== 开始授权状态检查 ========");
            
            try
            {
                // 检查授权文件
                string licenseFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "license.license");
                bool fileExists = File.Exists(licenseFilePath);
                LogStaticMessage($"授权文件 {licenseFilePath} 存在: {fileExists}");
                
                // 授权状态
                bool isLicensed = License.Status.Licensed;
                LogStaticMessage($"Licensed = {isLicensed}");
                
                string hardwareId = License.Status.GetHardwareID(true, true, true, false);
                LogStaticMessage($"当前硬件ID (HardwareID) = {hardwareId}");
                
                // 检查硬件ID是否为未保护应用程序的默认消息
                bool isUnprotected = hardwareId.Contains("To get a hardware ID please protect your assembly with .NET Reactor");
                if (isUnprotected)
                {
                    LogStaticMessage($"警告: 应用程序未被.NET Reactor保护，无法正确获取硬件ID");
                }
                
                if (!isUnprotected)
                {
                    // 硬件锁状态
                    bool hardwareLockEnabled = License.Status.Hardware_Lock_Enabled;
                    LogStaticMessage($"Hardware_Lock_Enabled = {hardwareLockEnabled}");
                    
                    string licenseHardwareId = License.Status.License_HardwareID;
                    LogStaticMessage($"授权硬件ID (License_HardwareID) = {licenseHardwareId}");
                    
                    bool hardwareIdMatch = hardwareId == licenseHardwareId;
                    LogStaticMessage($"硬件ID匹配 = {hardwareIdMatch}");
                    
                    // 到期日期检查
                    bool expirationLockEnabled = License.Status.Expiration_Date_Lock_Enable;
                    LogStaticMessage($"Expiration_Date_Lock_Enable = {expirationLockEnabled}");
                    
                    if (expirationLockEnabled)
                    {
                        DateTime expirationDate = License.Status.Expiration_Date;
                        DateTime now = DateTime.Now;
                        bool isExpired = now > expirationDate;
                        
                        LogStaticMessage($"当前系统日期 = {now.ToString("yyyy-MM-dd HH:mm:ss")}");
                        LogStaticMessage($"授权到期日期 = {expirationDate.ToString("yyyy-MM-dd HH:mm:ss")}");
                        LogStaticMessage($"是否已过期 = {isExpired}");
                    }
                }
                
                try
                {
                    LogStaticMessage($"DeviceInfo.IsValidLicenseAvailable() = {DeviceInfo.IsValidLicenseAvailable()}");
                }
                catch (Exception ex)
                {
                    LogStaticMessage($"获取DeviceInfo.IsValidLicenseAvailable()失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                LogStaticMessage($"检查授权状态时出错: {ex.Message}");
            }
            
            LogStaticMessage("======== 授权状态检查完成 ========");
        }
    }

    public class LicenseStatusReport
    {
        // 客户端标识信息
        public string MachineId { get; set; }
        public string MachineName { get; set; }
        public string IpAddress { get; set; }
        public string WindowsVersion { get; set; }
        public string ApplicationVersion { get; set; }
        public DateTime ReportTime { get; set; }

        // 授权状态
        public bool IsLicensed { get; set; }
        public string HardwareId { get; set; }
        public string LicenseHardwareId { get; set; }

        // 评估锁状态
        public bool EvaluationLockEnabled { get; set; }
        public string EvaluationType { get; set; }
        public int EvaluationTime { get; set; }
        public int EvaluationTimeCurrent { get; set; }

        // 到期日期锁定状态
        public bool ExpirationDateLockEnabled { get; set; }
        public DateTime ExpirationDate { get; set; }

        // 使用次数锁定状态
        public bool NumberOfUsesLockEnabled { get; set; }
        public int MaxUses { get; set; }
        public int CurrentUses { get; set; }

        // 实例数量锁定状态
        public bool NumberOfInstancesLockEnabled { get; set; }
        public int MaxInstances { get; set; }

        // 硬件锁状态
        public bool HardwareLockEnabled { get; set; }

        // 附加信息
        public Dictionary<string, string> AdditionalInfo { get; set; } = new Dictionary<string, string>();
    }
}