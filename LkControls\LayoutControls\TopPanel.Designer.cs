﻿using LkControls.common;

namespace LkControls.LayoutControls
{
    partial class TopPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TopPanel));
            pictureBox1 = new System.Windows.Forms.PictureBox();
            label1 = new System.Windows.Forms.Label();
            fpdInfo = new System.Windows.Forms.Label();
            line1 = new System.Windows.Forms.Panel();
            pictureBox2 = new System.Windows.Forms.PictureBox();
            label3 = new System.Windows.Forms.Label();
            captureInfo = new System.Windows.Forms.Label();
            line2 = new System.Windows.Forms.Panel();
            pictureBox3 = new System.Windows.Forms.PictureBox();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            resolutionLabel = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            pixdataLabel = new System.Windows.Forms.Label();
            roundPanel1 = new RoundPanel();
            flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            panel4 = new System.Windows.Forms.Panel();
            workDirectoryLabel = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            pictureBox4 = new System.Windows.Forms.PictureBox();
            panel5 = new System.Windows.Forms.Panel();
            panel1 = new System.Windows.Forms.Panel();
            panel2 = new System.Windows.Forms.Panel();
            panel3 = new System.Windows.Forms.Panel();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox3).BeginInit();
            roundPanel1.SuspendLayout();
            flowLayoutPanel1.SuspendLayout();
            panel4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox4).BeginInit();
            panel1.SuspendLayout();
            panel2.SuspendLayout();
            panel3.SuspendLayout();
            SuspendLayout();
            // 
            // pictureBox1
            // 
            pictureBox1.Image = (System.Drawing.Image)resources.GetObject("pictureBox1.Image");
            pictureBox1.Location = new System.Drawing.Point(15, 13);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new System.Drawing.Size(57, 62);
            pictureBox1.TabIndex = 0;
            pictureBox1.TabStop = false;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            label1.ForeColor = System.Drawing.Color.FromArgb(250, 102, 38);
            label1.Location = new System.Drawing.Point(91, 13);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(52, 27);
            label1.TabIndex = 1;
            label1.Text = "平板";
            // 
            // fpdInfo
            // 
            fpdInfo.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            fpdInfo.ForeColor = System.Drawing.Color.White;
            fpdInfo.Location = new System.Drawing.Point(94, 44);
            fpdInfo.MaximumSize = new System.Drawing.Size(150, 40);
            fpdInfo.MinimumSize = new System.Drawing.Size(60, 20);
            fpdInfo.Name = "fpdInfo";
            fpdInfo.Size = new System.Drawing.Size(60, 20);
            fpdInfo.TabIndex = 2;
            fpdInfo.Text = "未连接";
            // 
            // line1
            // 
            line1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left;
            line1.BackColor = System.Drawing.Color.FromArgb(70, 70, 70);
            line1.Location = new System.Drawing.Point(500, 3);
            line1.Name = "line1";
            line1.Size = new System.Drawing.Size(1, 100);
            line1.TabIndex = 3;
            // 
            // pictureBox2
            // 
            pictureBox2.Image = (System.Drawing.Image)resources.GetObject("pictureBox2.Image");
            pictureBox2.Location = new System.Drawing.Point(20, 13);
            pictureBox2.Name = "pictureBox2";
            pictureBox2.Size = new System.Drawing.Size(69, 62);
            pictureBox2.TabIndex = 4;
            pictureBox2.TabStop = false;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            label3.ForeColor = System.Drawing.Color.FromArgb(250, 102, 38);
            label3.Location = new System.Drawing.Point(105, 14);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(92, 27);
            label3.TabIndex = 5;
            label3.Text = "图像采集";
            // 
            // captureInfo
            // 
            captureInfo.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            captureInfo.ForeColor = System.Drawing.Color.White;
            captureInfo.Location = new System.Drawing.Point(109, 45);
            captureInfo.Name = "captureInfo";
            captureInfo.Size = new System.Drawing.Size(146, 44);
            captureInfo.TabIndex = 6;
            captureInfo.Text = "未开始";
            // 
            // line2
            // 
            line2.BackColor = System.Drawing.Color.FromArgb(70, 70, 70);
            line2.Location = new System.Drawing.Point(783, 3);
            line2.Name = "line2";
            line2.Size = new System.Drawing.Size(1, 100);
            line2.TabIndex = 7;
            // 
            // pictureBox3
            // 
            pictureBox3.Image = (System.Drawing.Image)resources.GetObject("pictureBox3.Image");
            pictureBox3.Location = new System.Drawing.Point(21, 15);
            pictureBox3.Name = "pictureBox3";
            pictureBox3.Size = new System.Drawing.Size(66, 58);
            pictureBox3.TabIndex = 8;
            pictureBox3.TabStop = false;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label5.ForeColor = System.Drawing.Color.White;
            label5.Location = new System.Drawing.Point(111, 50);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(58, 19);
            label5.TabIndex = 9;
            label5.Text = "分辨率:";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            label6.ForeColor = System.Drawing.Color.FromArgb(250, 102, 38);
            label6.Location = new System.Drawing.Point(109, 16);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(92, 27);
            label6.TabIndex = 10;
            label6.Text = "图像信息";
            // 
            // resolutionLabel
            // 
            resolutionLabel.AutoSize = true;
            resolutionLabel.ForeColor = System.Drawing.Color.White;
            resolutionLabel.Location = new System.Drawing.Point(171, 52);
            resolutionLabel.Name = "resolutionLabel";
            resolutionLabel.Size = new System.Drawing.Size(39, 20);
            resolutionLabel.TabIndex = 11;
            resolutionLabel.Text = "未知";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label8.ForeColor = System.Drawing.Color.White;
            label8.Location = new System.Drawing.Point(261, 50);
            label8.Name = "label8";
            label8.Size = new System.Drawing.Size(73, 19);
            label8.TabIndex = 12;
            label8.Text = "图像叠加:";
            // 
            // pixdataLabel
            // 
            pixdataLabel.AutoSize = true;
            pixdataLabel.ForeColor = System.Drawing.Color.White;
            pixdataLabel.Location = new System.Drawing.Point(336, 52);
            pixdataLabel.Name = "pixdataLabel";
            pixdataLabel.Size = new System.Drawing.Size(39, 20);
            pixdataLabel.TabIndex = 13;
            pixdataLabel.Text = "未启用";
            // 
            // roundPanel1
            // 
            roundPanel1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            roundPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundPanel1.Controls.Add(flowLayoutPanel1);
            roundPanel1.CornerRadius = 18;
            roundPanel1.Location = new System.Drawing.Point(23, 22);
            roundPanel1.Name = "roundPanel1";
            roundPanel1.Padding = new System.Windows.Forms.Padding(5);
            roundPanel1.Size = new System.Drawing.Size(1375, 94);
            roundPanel1.TabIndex = 18;
            // 
            // flowLayoutPanel1
            // 
            flowLayoutPanel1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            flowLayoutPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            flowLayoutPanel1.Controls.Add(panel4);
            flowLayoutPanel1.Controls.Add(panel5);
            flowLayoutPanel1.Controls.Add(panel1);
            flowLayoutPanel1.Controls.Add(line1);
            flowLayoutPanel1.Controls.Add(panel2);
            flowLayoutPanel1.Controls.Add(line2);
            flowLayoutPanel1.Controls.Add(panel3);
            flowLayoutPanel1.Location = new System.Drawing.Point(27, 0);
            flowLayoutPanel1.Name = "flowLayoutPanel1";
            flowLayoutPanel1.Size = new System.Drawing.Size(1330, 92);
            flowLayoutPanel1.TabIndex = 14;
            // 
            // panel4
            // 
            panel4.Controls.Add(workDirectoryLabel);
            panel4.Controls.Add(label2);
            panel4.Controls.Add(pictureBox4);
            panel4.Location = new System.Drawing.Point(3, 3);
            panel4.Name = "panel4";
            panel4.Size = new System.Drawing.Size(278, 91);
            panel4.TabIndex = 9;
            // 
            // workDirectoryLabel
            // 
            workDirectoryLabel.Cursor = System.Windows.Forms.Cursors.Hand;
            workDirectoryLabel.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            workDirectoryLabel.ForeColor = System.Drawing.Color.White;
            workDirectoryLabel.Location = new System.Drawing.Point(89, 44);
            workDirectoryLabel.MaximumSize = new System.Drawing.Size(170, 50);
            workDirectoryLabel.Name = "workDirectoryLabel";
            workDirectoryLabel.Size = new System.Drawing.Size(170, 50);
            workDirectoryLabel.TabIndex = 2;
            workDirectoryLabel.Text = "未选择";
            workDirectoryLabel.Click += workDirectoryLabel_Click;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Bold);
            label2.ForeColor = System.Drawing.Color.FromArgb(250, 102, 38);
            label2.Location = new System.Drawing.Point(88, 12);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(92, 27);
            label2.TabIndex = 1;
            label2.Text = "工作目录";
            // 
            // pictureBox4
            // 
            pictureBox4.Image = (System.Drawing.Image)resources.GetObject("pictureBox4.Image");
            pictureBox4.Location = new System.Drawing.Point(12, 14);
            pictureBox4.Name = "pictureBox4";
            pictureBox4.Size = new System.Drawing.Size(60, 57);
            pictureBox4.TabIndex = 0;
            pictureBox4.TabStop = false;
            // 
            // panel5
            // 
            panel5.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left;
            panel5.BackColor = System.Drawing.Color.FromArgb(70, 70, 70);
            panel5.Location = new System.Drawing.Point(287, 3);
            panel5.Name = "panel5";
            panel5.Size = new System.Drawing.Size(1, 100);
            panel5.TabIndex = 4;
            // 
            // panel1
            // 
            panel1.Controls.Add(pictureBox1);
            panel1.Controls.Add(fpdInfo);
            panel1.Controls.Add(label1);
            panel1.Location = new System.Drawing.Point(294, 3);
            panel1.MinimumSize = new System.Drawing.Size(200, 90);
            panel1.Name = "panel1";
            panel1.Size = new System.Drawing.Size(200, 90);
            panel1.TabIndex = 0;
            // 
            // panel2
            // 
            panel2.Controls.Add(pictureBox2);
            panel2.Controls.Add(label3);
            panel2.Controls.Add(captureInfo);
            panel2.Location = new System.Drawing.Point(507, 3);
            panel2.MinimumSize = new System.Drawing.Size(200, 90);
            panel2.Name = "panel2";
            panel2.Size = new System.Drawing.Size(270, 90);
            panel2.TabIndex = 4;
            // 
            // panel3
            // 
            panel3.Controls.Add(pictureBox3);
            panel3.Controls.Add(pixdataLabel);
            panel3.Controls.Add(label6);
            panel3.Controls.Add(label8);
            panel3.Controls.Add(label5);
            panel3.Controls.Add(resolutionLabel);
            panel3.Location = new System.Drawing.Point(790, 3);
            panel3.MinimumSize = new System.Drawing.Size(370, 90);
            panel3.Name = "panel3";
            panel3.Size = new System.Drawing.Size(434, 90);
            panel3.TabIndex = 8;
            // 
            // TopPanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(20, 20, 20);
            Controls.Add(roundPanel1);
            Name = "TopPanel";
            Padding = new System.Windows.Forms.Padding(20, 0, 20, 0);
            Size = new System.Drawing.Size(1398, 138);
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox2).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox3).EndInit();
            roundPanel1.ResumeLayout(false);
            flowLayoutPanel1.ResumeLayout(false);
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox4).EndInit();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            panel3.ResumeLayout(false);
            panel3.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Panel line1;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label captureInfo;
        private System.Windows.Forms.Panel line2;
        private System.Windows.Forms.PictureBox pictureBox3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label8;
        private RoundPanel roundPanel1;
        public System.Windows.Forms.Label fpdInfo;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        public System.Windows.Forms.Panel panel1;
        public System.Windows.Forms.Panel panel2;
        public System.Windows.Forms.Panel panel3;
        public System.Windows.Forms.Label resolutionLabel;
        public System.Windows.Forms.Label pixdataLabel;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.PictureBox pictureBox4;
        private System.Windows.Forms.Panel panel5;
        public System.Windows.Forms.Label workDirectoryLabel;
    }
}
