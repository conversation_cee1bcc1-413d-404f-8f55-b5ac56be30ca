﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Vintasoft.Imaging;
using Vintasoft.Imaging.Codecs.Encoders;
using Vintasoft.Imaging.Codecs.ImageFiles.Dicom;
using Vintasoft.Imaging.Dicom.UI.VisualTools;
using Vintasoft.Imaging.UI;
using OpenCvSharp;
using OpenCvSharp.Extensions;
using FellowOakDicom.IO.Buffer;
using FellowOakDicom;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using FellowOakDicom.Imaging;
using System.Drawing.Imaging;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Vintasoft.Imaging.Annotation.Dicom.UI.VisualTools;
using DemosCommonCode.Imaging;
using Vintasoft.Imaging.UI.VisualTools;
using Vintasoft.Imaging.Annotation.Measurements;
using Vintasoft.Imaging.Annotation.UI;
using LkControls.common;
using DicomViewerDemo;
using LkControls.Utils;
using LkControls.SetPanel;
using System.Diagnostics;
using InduVision.LkControls.SetPanel;
using static DemosCommonCode.Imaging.ImageMeasureToolAction;
using static DemosCommonCode.Imaging.VisualToolsToolStrip;
using System.Xml.Linq;
using InduVision;
using DicomViewerDemo.RecordDialogs;
using InduVision.LkControls.LayoutControls;
using InduVision.LkControls.Utils;
using Microsoft.VisualBasic.Devices;
using System.Threading;
using OpenCvSharp;
using OxyPlot.Annotations;
using Vintasoft.Imaging.Annotation;

namespace LkControls.LayoutControls
{
    public partial class ToolsPanel : UserControl
    {

        MainForm mainForm;

        [DllImport("user32.dll")]
        private static extern int ShowScrollBar(IntPtr hWnd, int wBar, int bShow);

        private const int SB_BOTH = 3; // 0=水平, 1=垂直, 3=全部

        public RoundButton activedButton;

        private MouseEventHandler regionMouseUpHandler;  // 改为 MouseEventHandler

        private System.Windows.Forms.ToolTip toolTip;

        private static Mat toolsMat = null;

        private double lastWindowWidth = 0;
        private double lastWindowCenter = 0;

        public ToolsPanel()
        {
            InitializeComponent();

            splitContainer1.Panel1.AutoScroll = false;
            splitContainer1.Panel1.HorizontalScroll.Enabled = false;
            splitContainer1.Panel1.HorizontalScroll.Visible = false;
            splitContainer1.Panel2.AutoScroll = false;
            splitContainer1.FixedPanel = FixedPanel.Panel2;

            HideScrollBar(splitContainer1.Panel1);
            // 调整所有按钮大小
            this.Load += (s, e) => AdjustAllButtonsSize();

            toolTip = new System.Windows.Forms.ToolTip();
            toolTip.AutoPopDelay = 5000;
            toolTip.InitialDelay = 500;
            toolTip.ReshowDelay = 200;
            toolTip.ShowAlways = true;
        }

        private void HideScrollBar(Control control)
        {
            ShowScrollBar(control.Handle, 0, 0);  // 隐藏水平和垂直滚动条
        }

        public MainForm MainForm
        {
            set
            {
                mainForm = value;
            }
        }

        private void panButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(panButton))
            {
                return;
            }

            panButton.IsPressed = true;

            DicomViewerToolInteractionMode interactionMode = DicomViewerToolInteractionMode.Pan;
            DicomViewerToolInteractionButtonToolStrip dicomViewerToolInteractionButtonToolStrip1 = mainForm.getDicomViewerToolInteractionButtonToolStrip1();
            dicomViewerToolInteractionButtonToolStrip1.Tool.SetInteractionMode(MouseButtons.Left, interactionMode);
        }

        private void zoomButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(zoomButton))
            {
                return;
            }

            zoomButton.IsPressed = true;

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.None;


            DicomViewerToolInteractionMode interactionMode = DicomViewerToolInteractionMode.Zoom;
            DicomViewerToolInteractionButtonToolStrip dicomViewerToolInteractionButtonToolStrip1 = mainForm.getDicomViewerToolInteractionButtonToolStrip1();
            dicomViewerToolInteractionButtonToolStrip1.Tool.SetInteractionMode(MouseButtons.Left, interactionMode);
        }

        private void wwwlButton_Click(object sender, EventArgs e)
        {
            if (activedButton == wwwlButton)
            {
                reset();
            }
            else
            {
                if (!checkClick(wwwlButton))
                {
                    return;
                }

                wwwlButton.IsPressed = true;

                DicomViewerToolInteractionMode interactionMode = DicomViewerToolInteractionMode.WindowLevel;
                DicomViewerToolInteractionButtonToolStrip dicomViewerToolInteractionButtonToolStrip1 = mainForm.getDicomViewerToolInteractionButtonToolStrip1();
                dicomViewerToolInteractionButtonToolStrip1.Tool.SetInteractionMode(MouseButtons.Left, interactionMode);

                if (mainForm.leftPanel.fpdPanel1.deviceType != 1 || mainForm.leftPanel.fpdPanel1.DynamicIsCapturing() == false)
                {
                    if (splitContainer1.Panel2Collapsed)
                    {
                        WwwlPanel wwwlPanel = new WwwlPanel(mainForm);

                        splitContainer1.Panel2.Controls.Add(wwwlPanel);

                        splitContainer1.Panel2Collapsed = false;

                        splitContainer1.SplitterDistance = this.ClientSize.Height - wwwlPanel.Height;
                    }
                    else
                    {
                        splitContainer1.Panel2Collapsed = true;

                        splitContainer1.SplitterDistance = splitContainer1.Height;

                        splitContainer1.Panel2.Controls.Clear();
                    }
                }
            }

        }

        private void clockwiseButton_Click(object sender, EventArgs e)
        {
            bool isPseudoColorActivated = false;

            if (activedButton == pseudoColorBtn) {
                isPseudoColorActivated = true;
            }

            if (!checkClick(clockwiseButton))
            {
                return;
            }

            if (mainForm.leftPanel.fpdPanel1.deviceType == 0 || mainForm.leftPanel.customTabControl1.SelectedIndex == 1 || MainForm.canRefreshImage)
            {
                MainForm.canRefreshImage = false;

                if (mainForm.leftPanel.fpdPanel1.deviceType == 1 && mainForm.leftPanel.customTabControl1.SelectedIndex == 0)
                    Thread.Sleep(200);

                ImageViewer imageViewer1 = mainForm.getImageViewer1();
                VintasoftImage image = imageViewer1.Image;

                Vintasoft.Imaging.PixelManipulator pixelManipulator = image.OpenPixelManipulator();
                // unlock pixels
                pixelManipulator.UnlockPixels();
                // close PixelManipulator and generate the Vintasoft.Imaging.VintasoftImage.Changed event
                image.ClosePixelManipulator(true);

                Mat rawMat = MainForm.ChangedRawMat;

                image.Rotate(90);
                MatProcesser.Rotate90Clockwise(rawMat);

                InduVision.LkControls.Utils.Utils.RenderImage(image, rawMat);

                mainForm.getDicomViewerTool().UpdateImage();

                MainForm.ChangedRawMat = rawMat;

                MainForm.canRefreshImage = true;
            }

            if (isPseudoColorActivated) {
                pseudoColorBtn_Click(sender, e);
            }
        }

        private void counterclockwiseButton_Click(object sender, EventArgs e)
        {
            bool isPseudoColorActivated = false;

            if (activedButton == pseudoColorBtn)
            {
                isPseudoColorActivated = true;
            }

            if (!checkClick(counterclockwiseButton))
            {
                return;
            }

            if (mainForm.leftPanel.fpdPanel1.deviceType == 0 || mainForm.leftPanel.customTabControl1.SelectedIndex == 1 || MainForm.canRefreshImage)
            {
                MainForm.canRefreshImage = false;

                if (mainForm.leftPanel.fpdPanel1.deviceType == 1 && mainForm.leftPanel.customTabControl1.SelectedIndex == 0)
                    Thread.Sleep(200);

                ImageViewer imageViewer1 = mainForm.getImageViewer1();
                VintasoftImage image = imageViewer1.Image;

                Mat rawMat = MainForm.ChangedRawMat;

                imageViewer1.Image.Rotate(-90);
                MatProcesser.Rotate90Counterclockwise(rawMat);

                InduVision.LkControls.Utils.Utils.RenderImage(image, rawMat);

                mainForm.getDicomViewerTool().UpdateImage();

                MainForm.ChangedRawMat = rawMat;

                MainForm.canRefreshImage = true;
            }

            if (isPseudoColorActivated)
            {
                pseudoColorBtn_Click(sender, e);
            }
        }

        private void verticalButton_Click(object sender, EventArgs e)
        {
            bool isPseudoColorActivated = false;

            if (activedButton == pseudoColorBtn)
            {
                isPseudoColorActivated = true;
            }

            if (!checkClick(verticalButton))
            {
                return;
            }

            if (mainForm.leftPanel.fpdPanel1.deviceType == 0 || mainForm.leftPanel.customTabControl1.SelectedIndex == 1 || MainForm.canRefreshImage)
            {
                MainForm.canRefreshImage = false;

                if (mainForm.leftPanel.fpdPanel1.deviceType == 1 && mainForm.leftPanel.customTabControl1.SelectedIndex == 0)
                    Thread.Sleep(200);

                ImageViewer imageViewer1 = mainForm.getImageViewer1();
                VintasoftImage image = imageViewer1.Image;

                Mat rawMat = MainForm.ChangedRawMat;

                MatProcesser.FlipVertical(rawMat);

                InduVision.LkControls.Utils.Utils.RenderImage(image, rawMat);

                mainForm.getDicomViewerTool().UpdateImage();

                MainForm.ChangedRawMat = rawMat;
                MainForm.canRefreshImage = true;
            }

            if (isPseudoColorActivated)
            {
                pseudoColorBtn_Click(sender, e);
            }
        }

        private void horizontalButton_Click(object sender, EventArgs e)
        {
            bool isPseudoColorActivated = false;

            if (activedButton == pseudoColorBtn)
            {
                isPseudoColorActivated = true;
            }

            if (!checkClick(horizontalButton))
            {
                return;
            }

            if (mainForm.leftPanel.fpdPanel1.deviceType == 0 || mainForm.leftPanel.customTabControl1.SelectedIndex == 1 || MainForm.canRefreshImage)
            {
                MainForm.canRefreshImage = false;

                if (mainForm.leftPanel.fpdPanel1.deviceType == 1 && mainForm.leftPanel.customTabControl1.SelectedIndex == 0)
                    Thread.Sleep(200);

                ImageViewer imageViewer1 = mainForm.getImageViewer1();

                VintasoftImage image = imageViewer1.Image;

                Mat rawMat = MainForm.ChangedRawMat;

                MatProcesser.FlipHorizontal(rawMat);

                InduVision.LkControls.Utils.Utils.RenderImage(image, rawMat);

                mainForm.getDicomViewerTool().UpdateImage();

                MainForm.ChangedRawMat = rawMat;

                MainForm.canRefreshImage = true;
            }

            if (isPseudoColorActivated)
            {
                pseudoColorBtn_Click(sender, e);
            }
        }

        private void negativeButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(negativeButton))
            {
                return;
            }

            MainForm.canRefreshImage = false;

            if (mainForm.leftPanel.fpdPanel1.deviceType == 1 && mainForm.leftPanel.customTabControl1.SelectedIndex == 0)
                Thread.Sleep(200);

            DemosCommonCode.Imaging.DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();
            dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool.DicomViewerTool.IsImageNegative = !dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool.DicomViewerTool.IsImageNegative;

            negativeButton.IsPressed = mainForm.getDicomAnnotatedViewerToolStrip1().DicomAnnotatedViewerTool.DicomViewerTool.IsImageNegative;

            MainForm.canRefreshImage = true;
        }

        private void WallThicknessBtn_Click(object sender, EventArgs e)
        {
            //DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            //ImageMeasureToolAction imageMeasureToolAction = dicomAnnotatedViewerToolStrip1.FindAction<ImageMeasureToolAction>();
            //imageMeasureToolAction.BeginWallMeasurement(mainForm);

            if (!checkClick(WallThicknessBtn))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("WallThickness");
        }

        private void exportImageButton_Click(object sender, EventArgs e)
        {
            if (mainForm.leftPanel.customTabControl1.SelectedIndex == 0)
            {
                return;
            }

            if (splitContainer1.Panel2Collapsed)
            {
                ExportImagePanel exportImagePanel = new ExportImagePanel(mainForm);

                splitContainer1.Panel2.Controls.Add(exportImagePanel);

                splitContainer1.Panel2Collapsed = false;

                exportImagePanel.Dock = DockStyle.Top;

                splitContainer1.SplitterDistance = this.ClientSize.Height - exportImagePanel.Height;
            }
            else
            {
                splitContainer1.Panel2Collapsed = true;

                splitContainer1.SplitterDistance = splitContainer1.Height;

                splitContainer1.Panel2.Controls.Clear();
            }
        }

        private void magnifierButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(magnifierButton))
            {
                return;
            }

            VisualToolActionButtonsDictionary visualToolActionButtonsDictionary = mainForm.getDicomAnnotatedViewerToolStrip1()._toolStripDictionary;

            VisualToolAction action = visualToolActionButtonsDictionary.getAction("Magnifier Tool");

            action.Activate();

            action.Click();

            if (splitContainer1.Panel2Collapsed)
            {
                MagnifierToolSettingsPanel magnifierToolSettingsPanel = new MagnifierToolSettingsPanel(splitContainer1);

                magnifierToolSettingsPanel.Magnifier = (MagnifierTool)mainForm.getDicomAnnotatedViewerToolStrip1().MainVisualTool.FindVisualTool<MagnifierTool>();

                splitContainer1.Panel2.Controls.Add(magnifierToolSettingsPanel);

                splitContainer1.Panel2Collapsed = false;

                magnifierToolSettingsPanel.Dock = DockStyle.Top;

                splitContainer1.SplitterDistance = this.ClientSize.Height - magnifierToolSettingsPanel.Height;
            }
            else
            {
                splitContainer1.Panel2Collapsed = true;

                splitContainer1.SplitterDistance = splitContainer1.Height;

                splitContainer1.Panel2.Controls.Clear();
            }
        }

        private void duplexIQIButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(duplexIQIButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();

            if (annotationsToolStrip1.duplexIQIForm != null)
            {
                return;
            }

            annotationsToolStrip1.BuildAnnotation("DuplexIQI");

            if (splitContainer1.Panel2Collapsed)
            {
                DuplexIQIPanel duplexIQIPanel = new DuplexIQIPanel(mainForm);

                splitContainer1.Panel2.Controls.Add(duplexIQIPanel);

                splitContainer1.Panel2Collapsed = false;

                duplexIQIPanel.Dock = DockStyle.Top;

                splitContainer1.SplitterDistance = this.ClientSize.Height - duplexIQIPanel.Height;
            }
            else
            {
                splitContainer1.Panel2Collapsed = true;

                splitContainer1.SplitterDistance = splitContainer1.Height;

                splitContainer1.Panel2.Controls.Clear();
            }
        }

        private void scalingButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(lineMeasureButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Scaling");
        }

        private void lineMeasureButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(lineMeasureButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();
            ImageMeasureToolAction imageMeasureToolAction = dicomAnnotatedViewerToolStrip1.FindAction<ImageMeasureToolAction>();

            imageMeasureToolAction.BeginMeasurement(MeasurementType.Line);
        }

        private void ellipseMeasureButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(ellipseButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();
            ImageMeasureToolAction imageMeasureToolAction = dicomAnnotatedViewerToolStrip1.FindAction<ImageMeasureToolAction>();

            imageMeasureToolAction.BeginMeasurement(MeasurementType.Ellipse);
        }

        private void linesMeasureButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(linesMeasureButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();
            ImageMeasureToolAction imageMeasureToolAction = dicomAnnotatedViewerToolStrip1.FindAction<ImageMeasureToolAction>();

            imageMeasureToolAction.BeginMeasurement(MeasurementType.Lines);
        }

        private void angleMeasureButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(angleMeasureButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();
            ImageMeasureToolAction imageMeasureToolAction = dicomAnnotatedViewerToolStrip1.FindAction<ImageMeasureToolAction>();

            imageMeasureToolAction.BeginMeasurement(MeasurementType.Angle);
        }

        private void pointButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(pointButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Point");
        }

        private void circleButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(circleButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Circle");
        }

        private void polylineButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(polylineButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Polyline");
        }

        private void interpolatedButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(interpolatedButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Interpolated");
        }

        private void rectangleButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(rectangleButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Rectangle");
        }

        private void ellipseButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(ellipseButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Ellipse");
        }

        private void multilineButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(multilineButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Multiline");
        }

        private void rangelineButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(rangelineButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Rangeline");
        }

        private void infinitelineButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(infinitelineButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Infiniteline");
        }

        private void cutlineButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(cutlineButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Cutline");
        }

        private void arrowButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(arrowButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Arrow");
        }

        private void axisButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(axisButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Axis");
        }

        private void rulerButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(rulerButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Ruler");
        }

        private void crosshairButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(crosshairButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Crosshair");
        }

        private void textButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(textButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Text");
        }

        private void regionButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(regionButton) || regionButton.isPressed == true)
            {
                return;
            }

            regionButton.isPressed = true;

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.isCancel = false;
            annotationsToolStrip1.BuildAnnotation("Region");
        }

        private void nsnrButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(nsnrButton))
            {
                return;
            }

            DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
            dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationInteractionMode = (AnnotationInteractionMode)DicomAnnotatedViewerToolInteractionMode.Annotation;

            AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
            annotationsToolStrip1.BuildAnnotation("Nsnr");
        }

        private void openProjectButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(openProjectButton))
            {
                return;
            }

            mainForm.OpenDirectory();
        }

        private void importImageButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(importImageButton))
            {
                return;
            }

            mainForm.AddDicomFiles();
        }

        private void screenshotButton_Click(object sender, EventArgs e)
        {
            System.Windows.Forms.SaveFileDialog saveFileDialog = new System.Windows.Forms.SaveFileDialog();

            saveFileDialog.DefaultExt = "jpg";
            saveFileDialog.Filter = "JPEG files|*.jpg";

            if (mainForm.leftPanel.customTabControl1.SelectedIndex == 0)
            {

                if (mainForm.leftPanel.fpdPanel1.StaticIsPressed())
                {
                    if (!string.IsNullOrEmpty(mainForm.leftPanel.fpdPanel1.StaticNameTextBox()))
                    {
                        saveFileDialog.FileName = mainForm.leftPanel.fpdPanel1.StaticNameTextBox();
                    }
                    else
                    {
                        saveFileDialog.FileName = DateTime.Now.ToString("yyyyMMddHHmmss");
                    }
                }
                else
                {
                    saveFileDialog.FileName = MainForm.currentFileName;
                }
            }
            else
            {
                saveFileDialog.FileName = MainForm.currentFileName;
            }

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                string saveFilename = Path.GetFullPath(saveFileDialog.FileName);

                VintasoftImage image = mainForm.imageViewer1.RenderViewerImage();
                image.Save(saveFilename);
            }

        }

        private bool checkClick(RoundButton roundButton)
        {
            // 获取按钮的原始文本 (现在存储在标签中)
            string buttonText = "";
            if (roundButton.Parent is Panel panel)
            {
                foreach (Control c in panel.Controls)
                {
                    if (c is Label label)
                    {
                        buttonText = label.Text;
                        break;
                    }
                }
            }

            // 检查项目目录
            if (buttonText != "打开项目" && buttonText != "截图" && mainForm.WorkDirectory == null)
            {
                MessageBox.Show(
                    "请打开项目后再进行该操作",
                    "操作异常",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                return false;
            }

            VintasoftImage vintasoftImage = mainForm.imageViewer1.Image;

            // 检查图像是否存在
            if (buttonText != "打开项目" && buttonText != "导入图像" && vintasoftImage == null)
            {
                MessageBox.Show(
                    "请选择有效的图像后再进行该操作",
                    "操作异常",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                return false;
            }

            // 检查图像尺寸
            if (buttonText != "打开项目" && buttonText != "截图" && buttonText != "导入图像" &&
                vintasoftImage != null && (vintasoftImage.Width < 100 || vintasoftImage.Height < 100))
            {
                MessageBox.Show(
                   "请选择有效的图像后再进行该操作",
                   "操作异常",
                   MessageBoxButtons.OK,
                   MessageBoxIcon.Information);
                return false;
            }

            if (activedButton != null)
            {
                if (roundButton != activedButton)
                {
                    reset();
                    activedButton = roundButton;
                }
            }
            else
            {
                activedButton = roundButton;
            }

            return true;
        }

        private void resetButton_Click(object sender, EventArgs e)
        {
            if (!checkClick(resetButton))
            {
                return;
            }

            MainForm.ChangedRawMat = MainForm.RawMat.Clone();

            Vintasoft.Imaging.VintasoftImage image = mainForm.imageViewer1.Image;

            InduVision.LkControls.Utils.Utils.RenderImage(image, MainForm.ChangedRawMat);

            try
            {
                var (windowLevel, windowWidth, maxGrayValue, minGrayValue) = Histogram.CalculateWindowLevelAndWidth(MainForm.ChangedRawMat);
                mainForm.getDicomViewerTool().DicomImageVoiLut = new Vintasoft.Imaging.Codecs.ImageFiles.Dicom.DicomImageVoiLookupTable(windowLevel, windowWidth);

                mainForm.getDicomViewerTool().UpdateImage();

                if (mainForm.imageViewer1.Image != null && mainForm.imageViewer1.Image.Width < 100)
                {
                    mainForm.imageViewer1.VisualTool = mainForm.getDicomAnnotatedViewerTool();
                    mainForm.imageViewer1.Images.Remove(mainForm.imageViewer1.Image);
                }

                mainForm.nsnrStripStatusLabel1.Text = "";

                mainForm.toolsPanel.reset();
                mainForm.toolsPanel.activedButton = null;

                DicomAnnotatedViewerTool dicomAnnotatedViewerTool = mainForm.getDicomAnnotatedViewerTool();
                DicomAnnotationTool dicomAnnotationTool = dicomAnnotatedViewerTool.DicomAnnotationTool;
                if (dicomAnnotationTool.AnnotationViewCollection.Count > 0)
                {
                    dicomAnnotationTool.AnnotationViewCollection.Clear();
                    dicomAnnotationTool.AnnotationDataCollection.Clear();
                }
                ImageMeasureTool imageMeasureTool = dicomAnnotatedViewerTool.ImageMeasureTool;
                if (imageMeasureTool.AnnotationViewCollection.Count > 0)
                {
                    imageMeasureTool.AnnotationViewCollection.Clear();
                }
            }
            catch (Exception ex)
            {

            }

        }

        public void reset()
        {
            if (activedButton != null)
            {
                activedButton.IsPressed = false;

                if (activedButton == magnifierButton)
                {
                    mainForm.imageViewer1.VisualTool = mainForm.getDicomAnnotatedViewerTool();
                }
                else if (activedButton == regionButton)
                {
                    regionButton.IsPressed = false;
                    AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
                    annotationsToolStrip1.cancelBuilding();
                }
                else if (activedButton == duplexIQIButton)
                {
                    duplexIQIButton.IsPressed = false;
                    AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
                    annotationsToolStrip1.cancelBuilding();
                    annotationsToolStrip1.isCancel = false;
                    if (annotationsToolStrip1.duplexIQIForm != null)
                    {
                        annotationsToolStrip1.duplexIQIForm.Close();
                        annotationsToolStrip1.duplexIQIForm = null;
                    }
                }
                else if (activedButton == pseudoColorBtn)
                {
                    mainForm.imageViewer1.Image.Dispose();
                    mainForm.imageViewer1.Image = null;

                    byte[] imageData1;
                    Cv2.ImEncode(".tiff", MainForm.ChangedRawMat, out imageData1, new int[] { (int)ImwriteFlags.TiffCompression, 1 });
                    MemoryStream memoryStream1 = new MemoryStream(imageData1);
                    VintasoftImage vintasoftImage = new VintasoftImage(memoryStream1, true);
                    vintasoftImage.Resolution = new Vintasoft.Imaging.Resolution(250f, 250f);

                    mainForm.imageViewer1.Image = vintasoftImage;

                    mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(lastWindowCenter, lastWindowWidth);

                    lastWindowCenter = 0;
                    lastWindowWidth = 0;
                }
                else if (activedButton == rilievoBtn)
                {
                    InduVision.LkControls.Utils.Utils.RenderImage(mainForm.imageViewer1.Image, MainForm.RawMat);
                    mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(lastWindowCenter, lastWindowWidth);
                }

                DicomViewerToolInteractionMode interactionMode = DicomViewerToolInteractionMode.WindowLevel;
                DicomViewerToolInteractionButtonToolStrip dicomViewerToolInteractionButtonToolStrip1 = mainForm.getDicomViewerToolInteractionButtonToolStrip1();
                dicomViewerToolInteractionButtonToolStrip1.Tool.SetInteractionMode(MouseButtons.Left, interactionMode);

                activedButton = null;
            }
            splitContainer1.Panel2Collapsed = true;
            foreach (Control ctrl in splitContainer1.Panel2.Controls.Cast<Control>().ToList())
            {
                splitContainer1.Panel2.Controls.Remove(ctrl);
                ctrl.Dispose();
            }

            negativeButton.IsPressed = mainForm.getDicomAnnotatedViewerToolStrip1().DicomAnnotatedViewerTool.DicomViewerTool.IsImageNegative;

            mainForm.nsnrStripStatusLabel1.Text = "";
            mainForm.separator1.Visible = false;
        }
        private void AdjustAllButtonsSize()
        {
            // 修改按钮设置
            ApplySquareSizeToButtons(flowLayoutPanel2.Controls);
            ApplySquareSizeToButtons(flowLayoutPanel3.Controls);
            ApplySquareSizeToButtons(enhancementButtonPanel.Controls);
            ApplySquareSizeToButtons(measureButtonPanel.Controls);
            ApplySquareSizeToButtons(annotationButtonPanel.Controls);


            // 调整面板高度以容纳所有按钮
            AdjustPanelHeightsBasedOnContent();
        }

        private void ApplySquareSizeToButtons(Control.ControlCollection controls)
        {
            // 创建控件副本，因为我们会修改集合
            var controlsList = new List<Control>();
            foreach (Control c in controls)
            {
                controlsList.Add(c);
            }

            foreach (Control control in controlsList)
            {
                if (control is RoundButton btn)
                {
                    // 保存原始文本
                    string btnText = btn.Text;

                    // 加载SVG图标 - 先使用原始文本加载图标
                    btn.LoadSvgIcon(btnText);

                    // 按钮不显示文本 - 然后再清空文本
                    btn.Text = "";

                    // 创建一个容器来包含按钮和标签
                    Panel buttonPanel = new Panel
                    {
                        Size = new System.Drawing.Size(70, 85), // 给按钮和标签留足空间
                        BackColor = Color.Transparent,
                        Margin = new Padding(5, 8, 5, 8)
                    };

                    // 设置按钮大小和位置
                    btn.Size = new System.Drawing.Size(60, 60);
                    btn.Location = new System.Drawing.Point(5, 0);
                    btn.Margin = new Padding(0);

                    // 添加工具提示
                    toolTip.SetToolTip(btn, btnText);

                    // 从原始父容器移除按钮
                    FlowLayoutPanel parent = btn.Parent as FlowLayoutPanel;
                    if (parent != null)
                    {
                        parent.Controls.Remove(btn);

                        // 创建标签
                        Label textLabel = new Label
                        {
                            Text = btnText,
                            AutoSize = false,
                            Size = new System.Drawing.Size(70, 20),
                            TextAlign = ContentAlignment.TopCenter,
                            Font = new System.Drawing.Font("Microsoft YaHei", 10f),
                            ForeColor = Color.White,
                            BackColor = Color.Transparent,
                            Location = new System.Drawing.Point(0, 65)
                        };

                        // 将按钮和标签添加到面板
                        buttonPanel.Controls.Add(btn);
                        buttonPanel.Controls.Add(textLabel);

                        // 将面板添加到FlowLayoutPanel
                        parent.Controls.Add(buttonPanel);
                    }
                }
            }
        }



        private void AdjustPanelHeightsBasedOnContent()
        {
            // 为每个FlowLayoutPanel自动调整高度
            AdjustFlowPanelHeight(flowLayoutPanel2);
            AdjustFlowPanelHeight(flowLayoutPanel3);
            AdjustFlowPanelHeight(measureButtonPanel);
            AdjustFlowPanelHeight(annotationButtonPanel);

            // 调整整个控件的高度以容纳所有面板
            this.Height = CalculateTotalHeight();
        }

        private void AdjustFlowPanelHeight(FlowLayoutPanel panel)
        {
            // 计算面板所需的高度
            int requiredHeight = 0;
            foreach (Control control in panel.Controls)
            {
                // 考虑每个控件的高度和边距
                requiredHeight = Math.Max(requiredHeight,
                    control.Bottom + control.Margin.Bottom);
            }

            // 设置新高度（添加一些额外空间）
            panel.Height = requiredHeight + 10;
        }

        private int CalculateTotalHeight()
        {
            // 计算所有面板加上标签的总高度
            int totalHeight = 0;

            // 遍历所有控件计算总高度（包括标签和面板）
            foreach (Control c in splitContainer1.Panel1.Controls)
            {
                totalHeight += c.Height + c.Margin.Top + c.Margin.Bottom;
            }

            return totalHeight + 20; // 添加一些额外空间
        }

        private void enhanceBtn1_Click(object sender, EventArgs e)
        {
            if (!checkClick(enhanceBtn1))
            {
                return;
            }

            if (splitContainer1.Panel2Collapsed == false)
            {
                splitContainer1.Panel2Collapsed = true;
                splitContainer1.SplitterDistance = splitContainer1.Height;
                splitContainer1.Panel2.Controls.Clear();
            }

            if (splitContainer1.Panel2Collapsed)
            {
                EnhancePanel enhancePanel = new EnhancePanel(splitContainer1, mainForm, 7, 9, 0, 4, 8, 8);

                splitContainer1.Panel2.Controls.Add(enhancePanel);
                splitContainer1.Panel2Collapsed = false;
                enhancePanel.Dock = DockStyle.Top;

                enhancePanel.ApplyEnhance(enhancePanel.gammaCheckBox.Checked, enhancePanel.autoWindowCheckBox.Checked);

                splitContainer1.SplitterDistance = this.ClientSize.Height - enhancePanel.Height;
            }

        }

        private void enhanceBtn2_Click(object sender, EventArgs e)
        {
            if (!checkClick(enhanceBtn2))
            {
                return;
            }

            if (splitContainer1.Panel2Collapsed == false)
            {
                splitContainer1.Panel2Collapsed = true;
                splitContainer1.SplitterDistance = splitContainer1.Height;
                splitContainer1.Panel2.Controls.Clear();
            }

            if (splitContainer1.Panel2Collapsed)
            {
                EnhancePanel enhancePanel = new EnhancePanel(splitContainer1, mainForm, 5, 8, 0, 13, 12, 12);

                splitContainer1.Panel2.Controls.Add(enhancePanel);
                splitContainer1.Panel2Collapsed = false;
                enhancePanel.Dock = DockStyle.Top;

                enhancePanel.ApplyEnhance(enhancePanel.gammaCheckBox.Checked, enhancePanel.autoWindowCheckBox.Checked);

                splitContainer1.SplitterDistance = this.ClientSize.Height - enhancePanel.Height;
            }
        }

        private void enhanceBtn3_Click(object sender, EventArgs e)
        {
            if (!checkClick(enhanceBtn3))
            {
                return;
            }

            if (splitContainer1.Panel2Collapsed == false)
            {
                splitContainer1.Panel2Collapsed = true;
                splitContainer1.SplitterDistance = splitContainer1.Height;
                splitContainer1.Panel2.Controls.Clear();
            }

            if (splitContainer1.Panel2Collapsed)
            {
                EnhancePanel enhancePanel = new EnhancePanel(splitContainer1, mainForm, 3, 15, -1, 20, 13, 13);

                splitContainer1.Panel2.Controls.Add(enhancePanel);
                splitContainer1.Panel2Collapsed = false;
                enhancePanel.Dock = DockStyle.Top;

                enhancePanel.ApplyEnhance(enhancePanel.gammaCheckBox.Checked, enhancePanel.autoWindowCheckBox.Checked);

                splitContainer1.SplitterDistance = this.ClientSize.Height - enhancePanel.Height;
            }
        }

        private void NoiseBtn1_Click(object sender, EventArgs e)
        {
            if (!checkClick(noiseBtn1))
            {
                return;
            }

            if (lastWindowCenter != 0)
            {
                mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(lastWindowCenter, lastWindowWidth);
            }

            denoise(1);
        }

        private void noiseBtn2_Click(object sender, EventArgs e)
        {
            if (!checkClick(noiseBtn2))
            {
                return;
            }

            if (lastWindowCenter != 0)
            {
                mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(lastWindowCenter, lastWindowWidth);
            }

            denoise(2);
        }

        private void noiseBtn3_Click(object sender, EventArgs e)
        {
            if (!checkClick(noiseBtn3))
            {
                return;
            }

            if (lastWindowCenter != 0)
            {
                mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(lastWindowCenter, lastWindowWidth);
            }

            denoise(3);
        }

        private void pseudoColorBtn_Click(object sender, EventArgs e)
        {
            if (activedButton == pseudoColorBtn)
            {
                reset();
            }
            else
            {
                if (!checkClick(pseudoColorBtn))
                {
                    return;
                }

                pseudoColorBtn.isPressed = true;

                if (lastWindowCenter != 0)
                {
                    InduVision.LkControls.Utils.Utils.RenderImage(mainForm.imageViewer1.Image, MainForm.RawMat);
                    mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(lastWindowCenter, lastWindowWidth);
                }

                lastWindowWidth = mainForm.getDicomViewerTool().DicomImageVoiLut.WindowWidth;
                lastWindowCenter = mainForm.getDicomViewerTool().DicomImageVoiLut.WindowCenter;

                VintasoftImage image = mainForm.getDicomViewerTool().DisplayedImage;
                ushort[,] pixelData = VintasoftImageToPixelData(image);

                mainForm.imageViewer1.Image.Dispose();
                mainForm.imageViewer1.Image = null;

                VintasoftImage image1 = new VintasoftImage(MainForm.ChangedRawMat.Cols, MainForm.ChangedRawMat.Rows, Vintasoft.Imaging.PixelFormat.Bgr24);
                image1.Resolution = new Vintasoft.Imaging.Resolution(250f, 250f);
                mainForm.imageViewer1.Image = image1;

                InduVision.LkControls.Utils.Utils.RenderImagePseudoColor(image1, pixelData);
                mainForm.getDicomViewerTool().UpdateImage();
            }
        }

        private void rilievoBtn_Click(object sender, EventArgs e)
        {
            if (activedButton == rilievoBtn)
            {
                reset();
            }
            else
            {
                if (!checkClick(rilievoBtn))
                {
                    return;
                }

                if (toolsMat != null)
                {
                    toolsMat.Dispose();
                    toolsMat = null;
                }

                toolsMat = MainForm.ChangedRawMat.Clone();


                toolsMat = ToSculpture(toolsMat);

                lastWindowWidth = mainForm.getDicomViewerTool().DicomImageVoiLut.WindowWidth;
                lastWindowCenter = mainForm.getDicomViewerTool().DicomImageVoiLut.WindowCenter;

                VintasoftImage image = mainForm.imageViewer1.Image;
                InduVision.LkControls.Utils.Utils.RenderImage(image, toolsMat);

                var (windowLevel, windowWidth) = Histogram.cal_win_level(toolsMat);
                mainForm.getDicomViewerTool().DicomImageVoiLut = new DicomImageVoiLookupTable(windowLevel, windowWidth);
            }

        }

        private void denoise(int denoiseStrength)
        {
            if (toolsMat != null)
            {
                toolsMat.Dispose();
                toolsMat = null;
            }

            toolsMat = MainForm.ChangedRawMat.Clone();

            switch (denoiseStrength)
            {
                case 1:
                    // 轻度：高斯模糊
                    Cv2.GaussianBlur(toolsMat, toolsMat, new OpenCvSharp.Size(3, 3), 0);
                    break;
                case 2:
                    // 中度：中值滤波
                    Cv2.MedianBlur(toolsMat, toolsMat, 3);
                    break;
                case 3:
                    // 强度：双边滤波
                    // 注意：OpenCV 的 BilateralFilter 不支持 16 位，需要先转换到 32F 或 8U，再返回16U
                    Mat mat32F = new Mat();
                    toolsMat.ConvertTo(mat32F, MatType.CV_32F);

                    Mat bilateral = new Mat();
                    Cv2.BilateralFilter(mat32F, bilateral, 9, 75, 75);

                    bilateral.ConvertTo(toolsMat, MatType.CV_16U);
                    break;
                default:
                    throw new ArgumentException("无效的 denoiseStrength 值");
            }

            MainForm.ChangedRawMat = toolsMat.Clone();

            VintasoftImage image = mainForm.imageViewer1.Image;
            InduVision.LkControls.Utils.Utils.RenderImage(image, toolsMat);
            mainForm.getDicomViewerTool().UpdateImage();
        }

        public Mat ToSculpture(Mat src16u)
        {
            if (src16u.Type() != MatType.CV_16UC1)
                throw new ArgumentException("输入图像必须是16位单通道");

            int width = src16u.Cols;
            int height = src16u.Rows;

            Mat dst16u = new Mat(src16u.Size(), MatType.CV_16UC1);

            unsafe
            {
                byte* srcPtr = (byte*)src16u.DataPointer;
                byte* dstPtr = (byte*)dst16u.DataPointer;

                int srcStep = (int)src16u.Step();
                int dstStep = (int)dst16u.Step();

                // 并行执行（跳过 i=0）
                Parallel.For(1, height, i =>
                {
                    ushort* srcRow = (ushort*)(srcPtr + i * srcStep);
                    ushort* srcPrevRow = (ushort*)(srcPtr + (i - 1) * srcStep);
                    ushort* dstRow = (ushort*)(dstPtr + i * dstStep);

                    // 第一列直接复制
                    dstRow[0] = srcRow[0];

                    for (int j = 1; j < width; j++)
                    {
                        ushort prevPixel = srcPrevRow[j - 1];
                        ushort currPixel = srcRow[j];

                        int val = Math.Abs(currPixel - prevPixel + 2048);
                        val = Math.Min(65535, Math.Max(1024, val));

                        dstRow[j] = (ushort)val;
                    }
                });

                // 处理第一行
                ushort* srcFirstRow = (ushort*)srcPtr;
                ushort* dstFirstRow = (ushort*)dstPtr;
                for (int j = 0; j < width; j++)
                {
                    dstFirstRow[j] = srcFirstRow[j];
                }
            }

            return dst16u;
        }

        // 限定值到[min, max]
        private static byte ClampToByte(int value, int min, int max)
        {
            if (value < min) return (byte)min;
            if (value > max) return (byte)max;
            return (byte)value;
        }

        public ushort[,] VintasoftImageToPixelData(VintasoftImage image)
        {
            // get the PixelManipulator object
            Vintasoft.Imaging.PixelManipulator pixelManipulator = image.OpenPixelManipulator();
            // set the lock area to full image size
            System.Drawing.Rectangle lockRectangle =
                new System.Drawing.Rectangle(0, 0, image.Width, image.Height);
            // lock pixels for read and write
            pixelManipulator.LockPixels(lockRectangle, Vintasoft.Imaging.BitmapLockMode.ReadWrite);
            // remebmer the stride for performance purposes
            int width = image.Width;
            int height = image.Height;

            ushort[,] pixelData = new ushort[height, width];

            // Determine pixel format
            var pixelFormat = image.PixelFormat;

            // 计算每像素字节数
            int bytesPerPixel = image.BitsPerPixel / 8;

            for (int y = 0; y < height; y++)
            {
                byte[] row = pixelManipulator.ReadRowData(y);

                for (int x = 0; x < width; x++)
                {
                    int byteIndex = x * bytesPerPixel;

                    if (bytesPerPixel == 1)
                    {
                        // 8位图像
                        byte gray = row[byteIndex];
                        pixelData[y, x] = gray; // 直接转为 ushort
                    }
                    else if (bytesPerPixel == 2)
                    {
                        // 16位图像
                        ushort gray = BitConverter.ToUInt16(row, byteIndex);
                        pixelData[y, x] = gray;
                    }
                    else
                    {
                        throw new NotSupportedException($"Unsupported pixel format with {bytesPerPixel * 8} bits per pixel.");
                    }
                }
            }

            // unlock pixels
            pixelManipulator.UnlockPixels();
            // close PixelManipulator
            image.ClosePixelManipulator(true);

            return pixelData;
        }

        
    }
}
