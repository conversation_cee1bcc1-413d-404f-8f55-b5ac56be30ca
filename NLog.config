﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <targets>
    <!-- 文件日志，自动创建 logs 目录，按日期每天创建新文件 -->
    <target name="logfile" xsi:type="File" 
            fileName="${basedir}/logs/${date:format=yyyy-MM-dd}.log" 
            layout="${longdate}|${level:uppercase=true}|${logger}|${message} ${exception:format=toString,Data:maxInnerExceptionLevel=10}" 
            encoding="utf-8"
            createDirs="true"
            archiveFileName="${basedir}/logs/archives/{#}.log"
            archiveNumbering="Date"
            archiveEvery="Day"
            archiveDateFormat="yyyy-MM-dd"
            maxArchiveFiles="30" />
    <!-- 彩色控制台日志 -->
    <target name="logconsole" xsi:type="ColoredConsole" 
            layout="${longdate}|${level:uppercase=true}|${logger}|${message} ${exception:format=toString,Data:maxInnerExceptionLevel=10}" />
  </targets>

  <rules>
    <!-- 同时输出到文件和控制台，日志等级为 Debug 及以上 -->
    <logger name="*" minlevel="Debug" writeTo="logfile,logconsole" />
  </rules>
</nlog>