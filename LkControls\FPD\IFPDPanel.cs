﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace InduVision.LkControls.FPD
{
    public abstract class IFPDPanel : UserControl
    {
        public int deviceType = 0; //设备类型
        public MainForm mainForm;

        public abstract void setMainForm(MainForm mainForm);

        public abstract void DynamicStop();

        public abstract void StaticStop();

        public abstract bool DynamicIsStart();

        public abstract bool DynamicIsCapturing();

        public abstract bool StaticIsCapturing();

        public abstract bool StaticIsPressed();

        public abstract string StaticNameTextBox();
    }
}
