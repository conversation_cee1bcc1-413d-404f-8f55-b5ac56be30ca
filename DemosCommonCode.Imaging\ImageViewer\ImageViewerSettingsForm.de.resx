﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="$this.Text" xml:space="preserve">
    <value>Bildbetrachter-Einstellungen</value>
  </data>
  <data name="keyboardNavigationCheckBox.Text" xml:space="preserve">
    <value>Tastaturnavigation</value>
  </data>
  <data name="label22.Text" xml:space="preserve">
    <value>Zoomschritt der Tastaturnavigation</value>
  </data>
  <data name="label21.Text" xml:space="preserve">
    <value>Scrollschritt der Tastaturnavigation (px)</value>
  </data>
  <data name="label17.Text" xml:space="preserve">
    <value>Bilder in Zeile/Spalte</value>
  </data>
  <data name="label18.Text" xml:space="preserve">
    <value>Bilder auffüllen</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>Layoutrichtung</value>
  </data>
  <data name="label19.Text" xml:space="preserve">
    <value>Mehrseitiger Anzeigemodus</value>
  </data>
  <data name="useImageAppearancesInSinglepageModeCheckBox.Text" xml:space="preserve">
    <value>Bilddarstellungen im Einzelseitenmodus verwenden</value>
  </data>
  <data name="focusedImageAppearanceButton.Text" xml:space="preserve">
    <value>Fokussierte Bilddarstellung...</value>
  </data>
  <data name="imageAppearanceButton.Text" xml:space="preserve">
    <value>Bilddarstellung...</value>
  </data>
  <data name="imagesDisplayModeGroupBox.Text" xml:space="preserve">
    <value>Bildanzeigemodus</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>Vorschauintervall für das Laden von Bildern
(bei aktiviertem Rendern)</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>Rendering-Qualitätsfaktor von Vektorbildern
(wenn Rendering aktiviert ist)</value>
  </data>
  <data name="label20.Text" xml:space="preserve">
    <value>Max. Threads</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>Millisekunden</value>
  </data>
  <data name="label16.Text" xml:space="preserve">
    <value>Höchste</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="renderOnlyVisibleImagesCheckBox.Text" xml:space="preserve">
    <value>Nur sichtbare Bilder rendern</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Rendering</value>
  </data>
  <data name="renderingRequirementsButton.Text" xml:space="preserve">
    <value>Teilweise/progressive Rendering-Anforderungen...</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Hintergrundfarbe</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="renderingSettingsButton.Text" xml:space="preserve">
    <value>Bild-Rendering-Einstellungen...</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Rendering-Qualität</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Bildanker</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Mindestbildgröße bei Verwendung des Zoompuffers</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Viewer-Puffergröße</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>Megabyte</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Megapixel</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Megapixel</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Renderer-Cache-Größe</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>Pufferung</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Anker</value>
  </data>
  <data name="focusPointIsFixedCheckBox.Text" xml:space="preserve">
    <value>Behoben</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Fokuspunkt</value>
  </data>
</root>