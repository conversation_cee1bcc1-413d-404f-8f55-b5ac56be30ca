﻿using DicomViewerDemo;
using FellowOakDicom.IO.Buffer;
using FellowOakDicom;
using InduVision;
using InduVision.LkControls.FPD.IRay;
using LkControls.LayoutControls;
using Newtonsoft.Json.Linq;
using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.Threading;

namespace InduVision.LkControls.FPD.IRay
{
    public partial class DynamicOperatePanel : UserControl
    {
        public bool isCapturing = false;
        public bool isStarted = false;
        MainForm mainForm;
        public event Action<JObject> DisconnectedDevice;

        public DynamicOperatePanel()
        {
            InitializeComponent();
            overlayNumber.ValueChanged += OverlayNumber_ValueChanged;
            isOverlay.CheckedChanged += IsOverlay_CheckedChanged;
        }

        private void IsOverlay_CheckedChanged(object sender, EventArgs e)
        {
            MainForm.isOverlay = isOverlay.Checked;
        }

        private void OverlayNumber_ValueChanged(object sender, EventArgs e)
        {
            MainForm.overlayNumber = (int)overlayNumber.Value;
        }

        public MainForm MainForm
        {
            set
            {
                mainForm = value;
            }
        }

        private void disconnectButton_Click(object sender, EventArgs e)
        {
            if (isCapturing) {
                MessageBox.Show(
                        "图像正在采集中，请先停止后再断开连接",                // 错误信息
                        "操作异常",                  // 弹窗标题
                        MessageBoxButtons.OK,    // 按钮类型：只有一个“确定”按钮
                        MessageBoxIcon.Information    // 弹窗图标：错误图标
                    );
                return;
            }

            DynamicFPD.disconnect();

            JObject jObject = new JObject
            { };

            DisconnectedDevice?.Invoke(jObject);
        }

        private void startButton_Click(object sender, EventArgs e)
        {
            start();
        }

        private void stopButton_Click(object sender, EventArgs e)
        {
            isStarted = false;
            stop();
        }

        private void saveButton_Click(object sender, EventArgs e)
        {
            string name;

            if (!string.IsNullOrEmpty(nameTextBox.Text))
            {
                name = nameTextBox.Text;  // 如果有值，使用 textBox 的内容
            }
            else
            {
                // 如果没有值，使用当前时间（年月日时分秒）
                name = DateTime.Now.ToString("yyyyMMddHHmmss");
            }

            if (mainForm.WorkDirectory != null)
            {
                mainForm.topPanel1.updateCaptureInfo("图像保存中...");

                string rawName = name + ".dcm";
                string thumbnailName = name + ".jpg";
                string projectPath = Path.Combine(mainForm.WorkDirectory, ".visionnova");
                string thumbnailPath = Path.Combine(projectPath, "thumbnail");
                string thumbnailFile = Path.Combine(thumbnailPath, thumbnailName);
                string destFile = Path.Combine(mainForm.WorkDirectory, rawName);

                if (mainForm.leftPanel.fpdPanel1.deviceType == 0)
                {
                    //TODO File.WriteAllBytes(destFile, StaticFPD.imageData);
                }
                else {
                    byte[] byteData = new byte[DynamicFPD.imageData.Length * 2];
                    Buffer.BlockCopy(DynamicFPD.imageData, 0, byteData, 0, byteData.Length);

                    var dataset = new DicomDataset(FellowOakDicom.DicomTransferSyntax.ImplicitVRLittleEndian)
                {
                    //{ DicomTag.PatientID, "20240919" },
                    { DicomTag.StudyDate, DateTime.Now.ToString("yyyyMMdd") },
                    { DicomTag.StudyTime, DateTime.Now.ToString("HHmmss") },
                    { DicomTag.SpecificCharacterSet,  "GB18030"},
                    { DicomTag.StudyInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                    { DicomTag.SeriesInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                    { DicomTag.SOPInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                    { DicomTag.SOPClassUID, DicomUID.SecondaryCaptureImageStorage },
                    { DicomTag.PhotometricInterpretation, FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2.Value },
                    { DicomTag.Rows, (ushort)MainForm.IMAGE_HEIGHT },
                    { DicomTag.Columns, (ushort)MainForm.IMAGE_WIDTH },
                    { DicomTag.BitsAllocated, (ushort)16 },
                    { DicomTag.BitsStored, (ushort)16 },
                    { DicomTag.HighBit, (ushort)15 },
                    { DicomTag.PixelRepresentation, (ushort)0 }, // 0 = unsigned, 1 = signed
                    { DicomTag.SamplesPerPixel, (ushort)1 }
                };

                    // Add pixel data
                    var pixelData = FellowOakDicom.Imaging.DicomPixelData.Create(dataset, true);
                    pixelData.BitsStored = 16;
                    pixelData.HighBit = 15;
                    pixelData.PixelRepresentation = FellowOakDicom.Imaging.PixelRepresentation.Unsigned;
                    pixelData.SamplesPerPixel = 1;
                    pixelData.PlanarConfiguration = FellowOakDicom.Imaging.PlanarConfiguration.Interleaved;
                    pixelData.Width = (ushort)MainForm.IMAGE_WIDTH;
                    pixelData.Height = (ushort)MainForm.IMAGE_HEIGHT;
                    pixelData.PhotometricInterpretation = FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2;

                    // Assign image data to DICOM pixel data
                    pixelData.AddFrame(new MemoryByteBuffer(byteData));

                    // Save the DICOM file
                    FellowOakDicom.DicomFile dicomFile = new FellowOakDicom.DicomFile(dataset);

                    dicomFile.Save(destFile);

                }
                

                //生成缩略图
                Mat rawMat = new Mat(MainForm.IMAGE_HEIGHT, MainForm.IMAGE_WIDTH, MainForm.IMAGE_MATTYPE);

                int length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH;

                if (MainForm.IMAGE_MATTYPE == MatType.CV_16U)
                {
                    length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH;
                }

                if (mainForm.leftPanel.fpdPanel1.deviceType == 0)
                {
                    Marshal.Copy(StaticFPD.imageData, 0, rawMat.Data, length);
                }
                else
                {
                    Marshal.Copy(DynamicFPD.imageData, 0, rawMat.Data, length);
                }
                
                // 计算新的宽度和高度
                int newWidth = (int)(MainForm.IMAGE_WIDTH * 0.1);
                int newHeight = (int)(MainForm.IMAGE_HEIGHT * 0.1);

                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                Cv2.ImWrite(thumbnailFile, rawMat);

                byte[] jpegData;
                // 将16位灰度图转换为8位灰度图
                rawMat.ConvertTo(rawMat, MatType.CV_8U, 255.0 / 65535.0); // 缩放因子：65535 -> 255

                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                Cv2.ImEncode(".jpg", rawMat, out jpegData);

                // 保存为文件或其他处理
                File.WriteAllBytes(thumbnailFile, jpegData);

                saveButton.IsPressed = false;
                nameTextBox.Text = "";

                //mainForm.leftPanel.thumbnailViewer1.RefreshThumbnails();

                //mainForm.leftPanel.thumbnailViewer1.SelectThumbnailByIndex(0);

                mainForm.topPanel1.updateCaptureInfo("图像已保存");
            }
            else {
                MessageBox.Show(
                    "请打开项目后再进行该操作",                // 错误信息
                    "操作异常",                  // 弹窗标题
                    MessageBoxButtons.OK,    // 按钮类型：只有一个“确定”按钮
                    MessageBoxIcon.Information    // 弹窗图标：错误图标
                );
            }

        }

        public void start() {
            bool result = DynamicFPD.start();
            if (result)
            {
                isCapturing = true;
                isStarted = true;
                mainForm.toolsPanel.measureTitlePanel.Visible = false;
                mainForm.toolsPanel.measureButtonPanel.Visible = false;
                mainForm.toolsPanel.annotationTitlePanel.Visible = false;
                mainForm.toolsPanel.annotationButtonPanel.Visible = false;
                mainForm.toolsPanel.WallThicknessBtn.Visible = false;
                mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = false;
                mainForm.toolsPanel.enhancementTitlePanel.Visible = false;
                mainForm.toolsPanel.enhancementButtonPanel.Visible = false;
                mainForm.topPanel1.updateCaptureInfo("图像开始采集");
            }
        }

        public void stop() {
            bool result = DynamicFPD.stop();
            if (result)
            {
                isCapturing = false;
                mainForm.toolsPanel.measureTitlePanel.Visible = true;
                mainForm.toolsPanel.measureButtonPanel.Visible = true;
                mainForm.toolsPanel.annotationTitlePanel.Visible = true;
                mainForm.toolsPanel.annotationButtonPanel.Visible = true;
                mainForm.toolsPanel.WallThicknessBtn.Visible = true;
                mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = true;
                mainForm.toolsPanel.enhancementTitlePanel.Visible = true;
                mainForm.toolsPanel.enhancementButtonPanel.Visible = true;
                mainForm.topPanel1.updateCaptureInfo("图像停止采集");
            }
        }

    }
}
