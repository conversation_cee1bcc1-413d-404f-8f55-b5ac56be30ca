﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Management;
using System.Security.Cryptography;
using InduVision.LkControls.Utils;
using InduVision.LicenseRegistration;

namespace InduVision
{
    public partial class Welcome : Form
    {
        public bool isExit = false;

        MainForm mainForm;
        public Welcome(MainForm _mainForm)
        {
            InitializeComponent();
            mainForm = _mainForm;
            versionLabel.Text = MainForm.VERSION;
        }

        private void Panel1_Click(object sender, System.EventArgs e)
        {
            mainForm.OpenDirectory();
            DialogResult = DialogResult.OK;
            Close();
        }

        private void Panel2_Click(object sender, System.EventArgs e)
        {
            isExit = true;
            DialogResult = DialogResult.OK;
            Close();
        }

        private void Panel3_Click(object sender, System.EventArgs e)
        {
            try
            {
                // 显示授权信息窗体
                using (var aboutLicenseForm = new AboutLicenseForm())
                {
                    aboutLicenseForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示授权信息失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void deviceCodeLabel_Click(object sender, EventArgs e)
        {
            Clipboard.SetText(DeviceInfo.GetHardwareId());
        }
    }
}
