﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Vintasoft.Imaging.Media;
using Vintasoft.Imaging;
using Vintasoft.Imaging.UI;

namespace InduVision.LkControls.CommonControls
{
    public class DynamicImageViewer : ImageViewer
    {
        #region Fields

        /// <summary>
        /// Image capture source.
        /// </summary>
        ImageCaptureSource _imageCaptureSource;

        #endregion



        #region Constructors

        /// <summary>
        /// Initializes a new instance of the <see cref="WebcamViewer"/> class.
        /// </summary>
        public DynamicImageViewer()
        {
            ShortcutDelete = System.Windows.Forms.Shortcut.None;
            ShortcutCut = System.Windows.Forms.Shortcut.None;
            ShortcutInsert = System.Windows.Forms.Shortcut.None;
        }

        #endregion



        #region Properties

        ImageCaptureDevice _captureDevice;
        /// <summary>
        /// Image capture device.
        /// </summary>
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public ImageCaptureDevice CaptureDevice
        {
            get
            {
                return _captureDevice;
            }
            set
            {
                if (_captureDevice != value)
                {
                    Stop();

                    if (_captureDevice != null)
                    {
                        _imageCaptureSource.CaptureCompleted -= new EventHandler<ImageCaptureCompletedEventArgs>(CaptureSource_CaptureCompleted);
                    }
                    _captureDevice = value;

                    if (_captureDevice != null)
                    {
                        _imageCaptureSource = new ImageCaptureSource();
                        _imageCaptureSource.CaptureCompleted += new EventHandler<ImageCaptureCompletedEventArgs>(CaptureSource_CaptureCompleted);
                        _imageCaptureSource.CaptureDevice = _captureDevice;
                    }
                    else
                    {
                        this.Image = null;
                    }
                }
            }
        }

        int _captureTimeout = 0;
        /// <summary>
        /// Image capture timeout.
        /// </summary>
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public int CaptureTimeout
        {
            get
            {
                return _captureTimeout;
            }
            set
            {
                _captureTimeout = value;
            }
        }

        #endregion



        #region Methods

        /// <summary>
        /// Starts image capturing.
        /// </summary>
        public void Start()
        {
            if (_imageCaptureSource != null && _imageCaptureSource.State != ImageCaptureState.Started)
            {
                _imageCaptureSource.Start();
                _imageCaptureSource.CaptureAsync();
            }
        }

        /// <summary>
        /// Stops image capturing.
        /// </summary>
        public void Stop()
        {
            if (_imageCaptureSource != null)
            {
                _imageCaptureSource.Stop();
            }
        }

        /// <summary>
        /// Gets a captured image.
        /// </summary>
        public VintasoftImage GetCapturedImage()
        {
            while (this.Image != null)
            {
                VintasoftImage image = this.Image;
                if (image == null)
                    return null;

                lock (image)
                {
                    if (!image.IsDisposed)
                        return (VintasoftImage)image.Clone();
                }
            }
            return null;
        }

        /// <summary>
        /// Image is captured.
        /// </summary>
        private void CaptureSource_CaptureCompleted(object sender, ImageCaptureCompletedEventArgs e)
        {
            if (_imageCaptureSource.State == ImageCaptureState.Stopped)
                return;

            // save reference to the previously captured image
            VintasoftImage oldImage = this.Image;
            // show captured image in the preview viewer
            this.Image = e.GetCapturedImage();
            // if previously captured image is exist
            if (oldImage != null)
            {
                lock (oldImage)
                {
                    // dispose previously captured image
                    oldImage.Dispose();
                }
            }

            // if capture source is started
            if (_imageCaptureSource.State == ImageCaptureState.Started)
            {
                // sleep...
                if (_captureTimeout > 0)
                    Thread.Sleep(_captureTimeout);
                // if capture source is started
                if (_imageCaptureSource.State == ImageCaptureState.Started)
                    // initialize new image capture request
                    _imageCaptureSource.CaptureAsync();
            }
        }

        #endregion
    }
}
