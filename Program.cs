﻿using Dynamic_Initialization;
using InduVision;
using System;
using System.Diagnostics;
using System.Drawing.Imaging;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using Dicom;
using Dicom.Imaging;
using InduVision.LicenseRegistration;
using License;
using InduVision.LkControls.Utils;
using System.Threading.Tasks;

namespace InduVision
{
    static class Program
    {

        [DllImport("kernel32.dll")]
        private static extern bool AllocConsole();
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 在创建任何窗体之前设置应用程序样式
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            //AllocConsole(); // 分配一个控制台窗口
            //new Initialization().Run();
            //Console.ReadKey();

            //正式代码

            DemosCommonCode.DemosTools.EnableLicenseExceptionDisplaying();

            // 获取当前进程名
            string processName = Process.GetCurrentProcess().ProcessName;

            // 检查是否已有其他相同名称的进程在运行
            if (Process.GetProcessesByName(processName).Length > 1)
            {
                MessageBox.Show("程序已在运行中！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 启动授权状态上报服务
            StartLicenseReporter();

            try
            {
                // 确保Config目录存在
                string configDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                if (!Directory.Exists(configDirectory))
                {
                    Directory.CreateDirectory(configDirectory);
                }

#if DEBUG
                // Debug模式下直接启动应用程序，跳过授权验证
                Application.Run(new MainForm());
                return;
#endif

                // 授权验证
                bool isLicensed = false;
                try
                {
                    // 尝试加载授权文件
                    string licensePath = Path.Combine(configDirectory, "license.license");
                    if (File.Exists(licensePath))
                    {
                        Status.LoadLicense(licensePath);
                        isLicensed = Status.Licensed && !IsLicenseExpired();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载授权文件时发生错误：{ex.Message}", "授权错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    isLicensed = false;
                }

                if (!isLicensed)
                {
                    // 显示授权窗口
                    LicenseRegisterForm registerForm = new LicenseRegisterForm();
                    if (registerForm.ShowDialog() != DialogResult.OK)
                    {
                        // 用户取消授权，退出程序
                        return;
                    }

                    // 重新检查授权状态
                    isLicensed = Status.Licensed && !IsLicenseExpired();
                    if (!isLicensed)
                    {
                        MessageBox.Show("软件未授权，请获取有效的授权后再使用！", "授权提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }

                // 启动应用程序主窗体
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"程序启动时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 启动授权状态上报服务
        /// </summary>
        private static void StartLicenseReporter()
        {
            // 使用Task.Run完全异步启动上报服务，避免影响主程序启动
            Task.Run(() => {
                try
                {
                    // 延迟2秒启动，确保主界面已加载
                    Task.Delay(2000).Wait();
                    Debug.WriteLine("开始初始化授权状态上报服务...");
                    
                    // 初始化上报服务单例（可能会有网络操作）
                    LicenseReporter reporter = LicenseReporter.Instance;
                    
                    // 设置更短的超时时间，避免长时间阻塞
                    reporter.SetHttpTimeout(TimeSpan.FromSeconds(5));
                    
                    // 启动授权状态上报服务
                    reporter.Start();
                    
                    // 在应用程序退出时停止服务
                    Application.ApplicationExit += (s, e) => 
                    {
                        try
                        {
                            reporter.Stop();
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"停止授权状态上报服务时出错: {ex.Message}");
                        }
                    };
                    
                    Debug.WriteLine("授权状态上报服务已初始化并启动");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"初始化授权状态上报服务时出错: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Debug.WriteLine($"内部错误: {ex.InnerException.Message}");
                    }
                    // 不中断主程序流程
                }
            });
            
            // 立即返回，不等待上报服务初始化完成
            Debug.WriteLine("主程序继续初始化，不等待授权上报服务");
        }

        /// <summary>
        /// 检查授权是否已过期
        /// </summary>
        private static bool IsLicenseExpired()
        {
            return Status.Expiration_Date_Lock_Enable && Status.Expiration_Date < DateTime.Now;
        }
       
    }
}
