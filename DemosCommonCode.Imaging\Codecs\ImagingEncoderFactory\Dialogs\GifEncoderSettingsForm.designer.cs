namespace DemosCommonCode.Imaging.Codecs.Dialogs
{
    partial class GifEncoderSettingsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GifEncoderSettingsForm));
            this.addImagesToExistingFileCheckBox = new System.Windows.Forms.CheckBox();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonOk = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rightPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.rightTopPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.letfBottomPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.bottomPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.rightBottomPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.topPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.leftPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.leftTopPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.centerPositionRadioButton = new System.Windows.Forms.RadioButton();
            this.createPageMethodComboBox = new System.Windows.Forms.ComboBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.animationDelayNumericUpDown = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.animationCyclesNumericUpDown = new System.Windows.Forms.NumericUpDown();
            this.infiniteAnimationCheckBox = new System.Windows.Forms.CheckBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.logicalScreenHeightNumericUpDown = new System.Windows.Forms.NumericUpDown();
            this.logicalScreenWidthNumericUpDown = new System.Windows.Forms.NumericUpDown();
            this.autoSizeCheckBox = new System.Windows.Forms.CheckBox();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.animationDelayNumericUpDown)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.animationCyclesNumericUpDown)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.logicalScreenHeightNumericUpDown)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.logicalScreenWidthNumericUpDown)).BeginInit();
            this.SuspendLayout();
            // 
            // addImagesToExistingFileCheckBox
            // 
            this.addImagesToExistingFileCheckBox.AutoSize = true;
            this.addImagesToExistingFileCheckBox.Location = new System.Drawing.Point(8, 12);
            this.addImagesToExistingFileCheckBox.Name = "addImagesToExistingFileCheckBox";
            this.addImagesToExistingFileCheckBox.Size = new System.Drawing.Size(129, 17);
            this.addImagesToExistingFileCheckBox.TabIndex = 10;
            resources.ApplyResources(this.addImagesToExistingFileCheckBox, "addImagesToExistingFileCheckBox");
            this.addImagesToExistingFileCheckBox.UseVisualStyleBackColor = true;
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonCancel.Location = new System.Drawing.Point(129, 262);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(75, 23);
            this.buttonCancel.TabIndex = 13;
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.UseVisualStyleBackColor = true;
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonOk
            // 
            this.buttonOk.Location = new System.Drawing.Point(48, 262);
            this.buttonOk.Name = "buttonOk";
            this.buttonOk.Size = new System.Drawing.Size(75, 23);
            this.buttonOk.TabIndex = 12;
            this.buttonOk.Text = "OK";
            this.buttonOk.UseVisualStyleBackColor = true;
            this.buttonOk.Click += new System.EventHandler(this.buttonOk_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rightPositionRadioButton);
            this.groupBox1.Controls.Add(this.rightTopPositionRadioButton);
            this.groupBox1.Controls.Add(this.letfBottomPositionRadioButton);
            this.groupBox1.Controls.Add(this.bottomPositionRadioButton);
            this.groupBox1.Controls.Add(this.rightBottomPositionRadioButton);
            this.groupBox1.Controls.Add(this.topPositionRadioButton);
            this.groupBox1.Controls.Add(this.leftPositionRadioButton);
            this.groupBox1.Controls.Add(this.leftTopPositionRadioButton);
            this.groupBox1.Controls.Add(this.centerPositionRadioButton);
            this.groupBox1.Location = new System.Drawing.Point(173, 41);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(88, 79);
            this.groupBox1.TabIndex = 14;
            this.groupBox1.TabStop = false;
            resources.ApplyResources(this.groupBox1, "groupBox1");
            // 
            // rightPositionRadioButton
            // 
            this.rightPositionRadioButton.AutoSize = true;
            this.rightPositionRadioButton.Location = new System.Drawing.Point(54, 37);
            this.rightPositionRadioButton.Name = "rightPositionRadioButton";
            this.rightPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.rightPositionRadioButton.TabIndex = 8;
            this.rightPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // rightTopPositionRadioButton
            // 
            this.rightTopPositionRadioButton.AutoSize = true;
            this.rightTopPositionRadioButton.Location = new System.Drawing.Point(54, 18);
            this.rightTopPositionRadioButton.Name = "rightTopPositionRadioButton";
            this.rightTopPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.rightTopPositionRadioButton.TabIndex = 7;
            this.rightTopPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // letfBottomPositionRadioButton
            // 
            this.letfBottomPositionRadioButton.AutoSize = true;
            this.letfBottomPositionRadioButton.Location = new System.Drawing.Point(14, 56);
            this.letfBottomPositionRadioButton.Name = "letfBottomPositionRadioButton";
            this.letfBottomPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.letfBottomPositionRadioButton.TabIndex = 6;
            this.letfBottomPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // bottomPositionRadioButton
            // 
            this.bottomPositionRadioButton.AutoSize = true;
            this.bottomPositionRadioButton.Location = new System.Drawing.Point(34, 56);
            this.bottomPositionRadioButton.Name = "bottomPositionRadioButton";
            this.bottomPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.bottomPositionRadioButton.TabIndex = 5;
            this.bottomPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // rightBottomPositionRadioButton
            // 
            this.rightBottomPositionRadioButton.AutoSize = true;
            this.rightBottomPositionRadioButton.Location = new System.Drawing.Point(54, 56);
            this.rightBottomPositionRadioButton.Name = "rightBottomPositionRadioButton";
            this.rightBottomPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.rightBottomPositionRadioButton.TabIndex = 4;
            this.rightBottomPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // topPositionRadioButton
            // 
            this.topPositionRadioButton.AutoSize = true;
            this.topPositionRadioButton.Location = new System.Drawing.Point(34, 18);
            this.topPositionRadioButton.Name = "topPositionRadioButton";
            this.topPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.topPositionRadioButton.TabIndex = 3;
            this.topPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // leftPositionRadioButton
            // 
            this.leftPositionRadioButton.AutoSize = true;
            this.leftPositionRadioButton.Location = new System.Drawing.Point(14, 37);
            this.leftPositionRadioButton.Name = "leftPositionRadioButton";
            this.leftPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.leftPositionRadioButton.TabIndex = 2;
            this.leftPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // leftTopPositionRadioButton
            // 
            this.leftTopPositionRadioButton.AutoSize = true;
            this.leftTopPositionRadioButton.Location = new System.Drawing.Point(14, 18);
            this.leftTopPositionRadioButton.Name = "leftTopPositionRadioButton";
            this.leftTopPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.leftTopPositionRadioButton.TabIndex = 1;
            this.leftTopPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // centerPositionRadioButton
            // 
            this.centerPositionRadioButton.AutoSize = true;
            this.centerPositionRadioButton.Checked = true;
            this.centerPositionRadioButton.Location = new System.Drawing.Point(34, 37);
            this.centerPositionRadioButton.Name = "centerPositionRadioButton";
            this.centerPositionRadioButton.Size = new System.Drawing.Size(14, 13);
            this.centerPositionRadioButton.TabIndex = 0;
            this.centerPositionRadioButton.TabStop = true;
            this.centerPositionRadioButton.UseVisualStyleBackColor = true;
            // 
            // createPageMethodComboBox
            // 
            this.createPageMethodComboBox.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.createPageMethodComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.createPageMethodComboBox.FormattingEnabled = true;
            this.createPageMethodComboBox.Location = new System.Drawing.Point(6, 15);
            this.createPageMethodComboBox.Name = "createPageMethodComboBox";
            this.createPageMethodComboBox.Size = new System.Drawing.Size(241, 21);
            this.createPageMethodComboBox.TabIndex = 15;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.createPageMethodComboBox);
            this.groupBox2.Location = new System.Drawing.Point(8, 203);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(253, 46);
            this.groupBox2.TabIndex = 16;
            this.groupBox2.TabStop = false;
            resources.ApplyResources(this.groupBox2, "groupBox2");
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.animationDelayNumericUpDown);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.animationCyclesNumericUpDown);
            this.groupBox3.Controls.Add(this.infiniteAnimationCheckBox);
            this.groupBox3.Location = new System.Drawing.Point(8, 126);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(253, 71);
            this.groupBox3.TabIndex = 17;
            this.groupBox3.TabStop = false;
            resources.ApplyResources(this.groupBox3, "groupBox3");
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(145, 18);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(56, 13);
            this.label4.TabIndex = 5;
            resources.ApplyResources(this.label4, "label4");
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(6, 18);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(34, 13);
            this.label3.TabIndex = 4;
            resources.ApplyResources(this.label3, "label3");
            // 
            // animationDelayNumericUpDown
            // 
            this.animationDelayNumericUpDown.Location = new System.Drawing.Point(79, 16);
            this.animationDelayNumericUpDown.Name = "animationDelayNumericUpDown";
            this.animationDelayNumericUpDown.Size = new System.Drawing.Size(55, 20);
            this.animationDelayNumericUpDown.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(6, 44);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(38, 13);
            this.label2.TabIndex = 2;
            resources.ApplyResources(this.label2, "label2");
            // 
            // animationCyclesNumericUpDown
            // 
            this.animationCyclesNumericUpDown.Location = new System.Drawing.Point(79, 42);
            this.animationCyclesNumericUpDown.Name = "animationCyclesNumericUpDown";
            this.animationCyclesNumericUpDown.Size = new System.Drawing.Size(55, 20);
            this.animationCyclesNumericUpDown.TabIndex = 1;
            // 
            // infiniteAnimationCheckBox
            // 
            this.infiniteAnimationCheckBox.AutoSize = true;
            this.infiniteAnimationCheckBox.Location = new System.Drawing.Point(148, 45);
            this.infiniteAnimationCheckBox.Name = "infiniteAnimationCheckBox";
            this.infiniteAnimationCheckBox.Size = new System.Drawing.Size(57, 17);
            this.infiniteAnimationCheckBox.TabIndex = 0;
            resources.ApplyResources(this.infiniteAnimationCheckBox, "infiniteAnimationCheckBox");
            this.infiniteAnimationCheckBox.UseVisualStyleBackColor = true;
            this.infiniteAnimationCheckBox.CheckedChanged += new System.EventHandler(this.infiniteAnimationCheckBox_CheckedChanged);
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label1);
            this.groupBox4.Controls.Add(this.logicalScreenHeightNumericUpDown);
            this.groupBox4.Controls.Add(this.logicalScreenWidthNumericUpDown);
            this.groupBox4.Controls.Add(this.autoSizeCheckBox);
            this.groupBox4.Location = new System.Drawing.Point(8, 41);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(159, 79);
            this.groupBox4.TabIndex = 18;
            this.groupBox4.TabStop = false;
            resources.ApplyResources(this.groupBox4, "groupBox4");
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(63, 52);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(14, 13);
            this.label1.TabIndex = 4;
            this.label1.Text = "X";
            // 
            // logicalScreenHeightNumericUpDown
            // 
            this.logicalScreenHeightNumericUpDown.Location = new System.Drawing.Point(79, 49);
            this.logicalScreenHeightNumericUpDown.Maximum = new decimal(new int[] {
            32768,
            0,
            0,
            0});
            this.logicalScreenHeightNumericUpDown.Name = "logicalScreenHeightNumericUpDown";
            this.logicalScreenHeightNumericUpDown.Size = new System.Drawing.Size(57, 20);
            this.logicalScreenHeightNumericUpDown.TabIndex = 3;
            // 
            // logicalScreenWidthNumericUpDown
            // 
            this.logicalScreenWidthNumericUpDown.Location = new System.Drawing.Point(6, 49);
            this.logicalScreenWidthNumericUpDown.Maximum = new decimal(new int[] {
            32768,
            0,
            0,
            0});
            this.logicalScreenWidthNumericUpDown.Name = "logicalScreenWidthNumericUpDown";
            this.logicalScreenWidthNumericUpDown.Size = new System.Drawing.Size(55, 20);
            this.logicalScreenWidthNumericUpDown.TabIndex = 2;
            // 
            // autoSizeCheckBox
            // 
            this.autoSizeCheckBox.AutoSize = true;
            this.autoSizeCheckBox.Location = new System.Drawing.Point(6, 26);
            this.autoSizeCheckBox.Name = "autoSizeCheckBox";
            this.autoSizeCheckBox.Size = new System.Drawing.Size(124, 17);
            this.autoSizeCheckBox.TabIndex = 0;
            resources.ApplyResources(this.autoSizeCheckBox, "autoSizeCheckBox");
            this.autoSizeCheckBox.UseVisualStyleBackColor = true;
            this.autoSizeCheckBox.CheckedChanged += new System.EventHandler(this.autoSizeCheckBox_CheckedChanged);
            // 
            // GifEncoderSettingsForm
            // 
            this.AcceptButton = this.buttonOk;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.buttonCancel;
            this.ClientSize = new System.Drawing.Size(273, 292);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonOk);
            this.Controls.Add(this.addImagesToExistingFileCheckBox);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "GifEncoderSettingsForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            resources.ApplyResources(this, "$this");
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.animationDelayNumericUpDown)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.animationCyclesNumericUpDown)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.logicalScreenHeightNumericUpDown)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.logicalScreenWidthNumericUpDown)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.CheckBox addImagesToExistingFileCheckBox;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Button buttonOk;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rightPositionRadioButton;
        private System.Windows.Forms.RadioButton rightTopPositionRadioButton;
        private System.Windows.Forms.RadioButton letfBottomPositionRadioButton;
        private System.Windows.Forms.RadioButton bottomPositionRadioButton;
        private System.Windows.Forms.RadioButton rightBottomPositionRadioButton;
        private System.Windows.Forms.RadioButton topPositionRadioButton;
        private System.Windows.Forms.RadioButton leftPositionRadioButton;
        private System.Windows.Forms.RadioButton leftTopPositionRadioButton;
        private System.Windows.Forms.RadioButton centerPositionRadioButton;
        private System.Windows.Forms.ComboBox createPageMethodComboBox;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox infiniteAnimationCheckBox;
        private System.Windows.Forms.NumericUpDown animationCyclesNumericUpDown;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.CheckBox autoSizeCheckBox;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown logicalScreenHeightNumericUpDown;
        private System.Windows.Forms.NumericUpDown logicalScreenWidthNumericUpDown;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown animationDelayNumericUpDown;
        private System.Windows.Forms.Label label4;
    }
}