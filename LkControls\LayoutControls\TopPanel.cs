﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace LkControls.LayoutControls
{
    public partial class TopPanel : UserControl
    {
        System.Windows.Forms.ToolTip toolTip1 = new System.Windows.Forms.ToolTip();
        System.Windows.Forms.ToolTip toolTip2 = new System.Windows.Forms.ToolTip();

        public TopPanel()
        {
            InitializeComponent();

            
            toolTip1.SetToolTip(workDirectoryLabel, workDirectoryLabel.Text);

            workDirectoryLabel.MouseHover += (s, e) =>
            {
                toolTip1.SetToolTip(workDirectoryLabel, workDirectoryLabel.Text);
            };
        }

        public void updateFPDInfo(string phase, string message)
        {
            fpdInfo.Text = message;
            fpdInfo.Width = fpdInfo.PreferredSize.Width;
            panel1.Width = panel1.MinimumSize.Width + fpdInfo.PreferredSize.Width - fpdInfo.MinimumSize.Width;
            toolTip2.SetToolTip(fpdInfo, phase);

            fpdInfo.MouseHover += (s, e) =>
            {
                toolTip2.SetToolTip(fpdInfo, phase);
            };
        }

        public void updateCaptureInfo(string text)
        {
            captureInfo.Text = text;
            //captureInfo.Width = captureInfo.PreferredSize.Width;
            //panel2.Width = panel2.MinimumSize.Width + captureInfo.PreferredSize.Width - captureInfo.MinimumSize.Width;
        }

        public void updateImageInfo(int width, int height, string overlay)
        {
            if (width == 0)
            {
                resolutionLabel.Text = "未知";
                pixdataLabel.Text = "未启用";
            }
            else
            {
                resolutionLabel.Text = string.Concat(width, " x ", height);
                pixdataLabel.Text = overlay;
            }
        }

        private void workDirectoryLabel_Click(object sender, EventArgs e)
        {
            string folderPath = workDirectoryLabel.Text;
            // 打开资源管理器并导航到指定文件夹
            Process.Start("explorer.exe", folderPath);
        }
    }
}
