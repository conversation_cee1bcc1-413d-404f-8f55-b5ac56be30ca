﻿using FellowOakDicom.IO.Buffer;
using FellowOakDicom;
using OpenCvSharp;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Vintasoft.Imaging;
using DemosCommonCode;
using Newtonsoft.Json.Linq;

namespace InduVision.LkControls.Utils
{
    public class Utils
    {
        //返回一条线上的所有坐标点
        public static List<(int, int)> BresenhamLine(int x1, int y1, int x2, int y2)
        {
            var points = new List<(int, int)>();
            int dx = Math.Abs(x2 - x1), dy = Math.Abs(y2 - y1);
            int sx = x1 < x2 ? 1 : -1, sy = y1 < y2 ? 1 : -1;
            int err = dx - dy;

            while (true)
            {
                points.Add((x1, y1));
                if (x1 == x2 && y1 == y2) break;
                int e2 = 2 * err;
                if (e2 > -dy)
                {
                    err -= dy;
                    x1 += sx;
                }
                if (e2 < dx)
                {
                    err += dx;
                    y1 += sy;
                }
            }

            return points;
        }

        //获取当前指定坐标两侧指定宽度的坐标值
        public static List<(int, int)> GetPerpendicularPoints(int x, int y, int dx, int dy, int distance)
        {
            var points = new List<(int, int)>();

            points.Add((x, y));

            double magnitude = Math.Sqrt(dx * dx + dy * dy);
            double perpDx = dy / magnitude;
            double perpDy = -dx / magnitude;


            int lastOffsetX = 0, lastOffsetY = 0;
            for (int d = 1, count = 0; d <= 1000; d++)
            {
                int offsetX = (int)Math.Round(perpDx * d);
                int offsetY = (int)Math.Round(perpDy * d);

                if (offsetX != lastOffsetX || offsetY != lastOffsetY)
                {
                    points.Add((x + offsetX, y + offsetY));
                    points.Add((x - offsetX, y - offsetY));

                    lastOffsetX = offsetX;
                    lastOffsetY = offsetY;
                    count++;
                }

                if (count >= distance)
                {
                    break;
                }
            }

            return points;
        }

        //获取当前指定坐标两侧指定宽度的坐标值
        public static double[] getDuplexIQIGrayArray(Mat rawMat, (int, int) start, (int, int) end, int distance)
        {
            // 获取直线的点
            var linePoints = Utils.BresenhamLine(start.Item1, start.Item2, end.Item1, end.Item2);

            // 方向向量
            int dx = end.Item1 - start.Item1;
            int dy = end.Item2 - start.Item2;

            // 结果存储
            var results = new List<(int, int, List<ushort>)>();

            List<long> grays = new List<long>();
            foreach (var (x, y) in linePoints)
            {
                var perpPoints = Utils.GetPerpendicularPoints(x, y, dx, dy, 10);
                var values = new List<ushort>();

                var total = 0;
                var count = 0;

                foreach (var (px, py) in perpPoints)
                {
                    // 检查是否在数组范围内
                    if (px >= 0 && px < rawMat.Cols && py >= 0 && py < rawMat.Rows)
                    {
                        values.Add(rawMat.At<ushort>(py, px));
                        total = total + rawMat.At<ushort>(py, px);
                        count++;
                    }
                }

                if (count > 0)
                {
                    grays.Add(total / count);

                    results.Add((x, y, values));
                }

            }

            double[] grayArray = new double[grays.Count];
            for (int i = 0; i < grays.Count; i++)
            {
                grayArray[i] = (double)grays[i];
            }

            return grayArray;
        }

        public static double[] getLineGrayArray(Mat rawMat, List<(int, int)> linePoints)
        {
            List<long> grays = new List<long>();
            foreach (var (px, py) in linePoints)
            {
                // 检查是否在数组范围内
                if (px >= 0 && px < rawMat.Cols && py >= 0 && py < rawMat.Rows)
                {
                    grays.Add(rawMat.At<ushort>(py, px));
                }
            }

            double[] grayArray = new double[grays.Count];
            for (int i = 0; i < grays.Count; i++)
            {
                grayArray[i] = (double)grays[i];
            }

            return grayArray;
        }

        public static void RenderImage(VintasoftImage image, Mat mat) {
            if (image.Width != mat.Width) {
                return;
            }

            try {
                if (image.IsImageDataLocked) {
                    image.ClosePixelManipulator(true);
                }

                Vintasoft.Imaging.PixelManipulator pixelManipulator = image.OpenPixelManipulator();
                System.Drawing.Rectangle lockRectangle =
                    new System.Drawing.Rectangle(0, 0, image.Width, image.Height);
                pixelManipulator.LockPixels(lockRectangle, Vintasoft.Imaging.BitmapLockMode.ReadWrite);
                int stride = pixelManipulator.Stride;
                int height = mat.Rows;
                int width = mat.Cols;

                if (mat.Type() == MatType.CV_16UC1)
                {
                    short[] matArray = new short[width];


                    for (int y = 0; y < mat.Rows; y++)
                    {
                        // 读取 Mat 的一行数据到 ushort 数组
                        Marshal.Copy(mat.Data + (y * width * sizeof(ushort)), matArray, 0, width);

                        // 分配一个 byte 数组（每个像素2字节）
                        byte[] rowData = pixelManipulator.ReadRowData(y);

                        // **优化关键点**: 使用 `Buffer.BlockCopy` 一次性转换
                        Buffer.BlockCopy(matArray, 0, rowData, 0, width * 2);
                        // write the modified scan line
                        pixelManipulator.WriteRowData(y, rowData);
                    }
                }
                else if (mat.Type() == MatType.CV_8U)
                {
                    byte[] matArray = new byte[width];
                    ushort[] matArray16 = new ushort[width];
                    for (int y = 0; y < height; y++)
                    {
                        Marshal.Copy(mat.Data + (y * width), matArray, 0, width);

                        // **扩展 8 位到 16 位**
                        for (int x = 0; x < width; x++)
                        {
                            matArray16[x] = (ushort)(matArray[x] * 256);  // 8位灰度扩展到16位
                        }

                        byte[] rowData = pixelManipulator.ReadRowData(y);
                        Buffer.BlockCopy(matArray16, 0, rowData, 0, width * 2); // 8位数据复制
                        pixelManipulator.WriteRowData(y, rowData);
                    }
                }



                // unlock pixels
                pixelManipulator.UnlockPixels();
                // close PixelManipulator and generate the Vintasoft.Imaging.VintasoftImage.Changed event
                image.ClosePixelManipulator(true);
            } catch (Exception e)
            {
            }
        }

        public static void RenderImagePseudoColor(VintasoftImage image, ushort[,] pixelData) {
            try
            {
                if (image.IsImageDataLocked)
                    image.ClosePixelManipulator(true);

                var pixelManipulator = image.OpenPixelManipulator();
                var lockRectangle = new System.Drawing.Rectangle(0, 0, image.Width, image.Height);
                pixelManipulator.LockPixels(lockRectangle, Vintasoft.Imaging.BitmapLockMode.ReadWrite);

                int width = pixelData.GetLength(1); 
                int height = pixelData.GetLength(0);

                for (int y = 0; y < height; y++)
                {
                    byte[] rowData = pixelManipulator.ReadRowData(y);


                    for (int x = 0; x < width; x++)
                    {
                        ushort v = pixelData[y, x];

                        rowData[x * 3 + 0] = GrayToPseudoColor((int)v, 2, 2); // B
                        rowData[x * 3 + 1] = GrayToPseudoColor((int)v, 2, 1); // G
                        rowData[x * 3 + 2] = GrayToPseudoColor((int)v, 2, 0); // R
                    }

                    pixelManipulator.WriteRowData(y, rowData);
                }

                pixelManipulator.UnlockPixels();
                image.ClosePixelManipulator(true);
            }
            catch (Exception e)
            {
                DemosTools.ShowErrorMessage(e);
            }
        }

        public static byte[,] ironTable = new byte[,]
        {
            {
                0,
                0,
                0
            },
            {
                0,
                0,
                0
            },
            {
                0,
                0,
                36
            },
            {
                0,
                0,
                51
            },
            {
                0,
                0,
                66
            },
            {
                0,
                0,
                81
            },
            {
                2,
                0,
                90
            },
            {
                4,
                0,
                99
            },
            {
                7,
                0,
                106
            },
            {
                11,
                0,
                115
            },
            {
                14,
                0,
                119
            },
            {
                20,
                0,
                123
            },
            {
                27,
                0,
                128
            },
            {
                33,
                0,
                133
            },
            {
                41,
                0,
                137
            },
            {
                48,
                0,
                140
            },
            {
                55,
                0,
                143
            },
            {
                61,
                0,
                146
            },
            {
                66,
                0,
                149
            },
            {
                72,
                0,
                150
            },
            {
                78,
                0,
                151
            },
            {
                84,
                0,
                152
            },
            {
                91,
                0,
                153
            },
            {
                97,
                0,
                155
            },
            {
                104,
                0,
                155
            },
            {
                110,
                0,
                156
            },
            {
                115,
                0,
                157
            },
            {
                122,
                0,
                157
            },
            {
                128,
                0,
                157
            },
            {
                134,
                0,
                157
            },
            {
                139,
                0,
                157
            },
            {
                146,
                0,
                156
            },
            {
                152,
                0,
                155
            },
            {
                157,
                0,
                155
            },
            {
                162,
                0,
                155
            },
            {
                167,
                0,
                154
            },
            {
                171,
                0,
                153
            },
            {
                175,
                1,
                152
            },
            {
                178,
                1,
                151
            },
            {
                182,
                2,
                149
            },
            {
                185,
                4,
                149
            },
            {
                188,
                5,
                147
            },
            {
                191,
                6,
                146
            },
            {
                193,
                8,
                144
            },
            {
                195,
                11,
                142
            },
            {
                198,
                13,
                139
            },
            {
                201,
                17,
                135
            },
            {
                203,
                20,
                132
            },
            {
                206,
                23,
                127
            },
            {
                208,
                26,
                121
            },
            {
                210,
                29,
                116
            },
            {
                212,
                33,
                111
            },
            {
                214,
                37,
                103
            },
            {
                217,
                41,
                97
            },
            {
                219,
                46,
                89
            },
            {
                221,
                49,
                78
            },
            {
                223,
                53,
                66
            },
            {
                224,
                56,
                54
            },
            {
                226,
                60,
                42
            },
            {
                228,
                64,
                30
            },
            {
                229,
                68,
                25
            },
            {
                231,
                72,
                20
            },
            {
                232,
                76,
                16
            },
            {
                234,
                78,
                12
            },
            {
                235,
                82,
                10
            },
            {
                236,
                86,
                8
            },
            {
                237,
                90,
                7
            },
            {
                238,
                93,
                5
            },
            {
                239,
                96,
                4
            },
            {
                240,
                100,
                3
            },
            {
                241,
                103,
                3
            },
            {
                241,
                106,
                2
            },
            {
                242,
                109,
                1
            },
            {
                243,
                113,
                1
            },
            {
                244,
                116,
                0
            },
            {
                244,
                120,
                0
            },
            {
                245,
                125,
                0
            },
            {
                246,
                129,
                0
            },
            {
                247,
                133,
                0
            },
            {
                248,
                136,
                0
            },
            {
                248,
                139,
                0
            },
            {
                249,
                142,
                0
            },
            {
                249,
                145,
                0
            },
            {
                250,
                149,
                0
            },
            {
                251,
                154,
                0
            },
            {
                252,
                159,
                0
            },
            {
                253,
                163,
                0
            },
            {
                253,
                168,
                0
            },
            {
                253,
                172,
                0
            },
            {
                254,
                176,
                0
            },
            {
                254,
                179,
                0
            },
            {
                254,
                184,
                0
            },
            {
                254,
                187,
                0
            },
            {
                254,
                191,
                0
            },
            {
                254,
                195,
                0
            },
            {
                254,
                199,
                0
            },
            {
                254,
                202,
                1
            },
            {
                254,
                205,
                2
            },
            {
                254,
                208,
                5
            },
            {
                254,
                212,
                9
            },
            {
                254,
                216,
                12
            },
            {
                byte.MaxValue,
                219,
                15
            },
            {
                byte.MaxValue,
                221,
                23
            },
            {
                byte.MaxValue,
                224,
                32
            },
            {
                byte.MaxValue,
                227,
                39
            },
            {
                byte.MaxValue,
                229,
                50
            },
            {
                byte.MaxValue,
                232,
                63
            },
            {
                byte.MaxValue,
                235,
                75
            },
            {
                byte.MaxValue,
                238,
                88
            },
            {
                byte.MaxValue,
                239,
                102
            },
            {
                byte.MaxValue,
                241,
                116
            },
            {
                byte.MaxValue,
                242,
                134
            },
            {
                byte.MaxValue,
                244,
                149
            },
            {
                byte.MaxValue,
                245,
                164
            },
            {
                byte.MaxValue,
                247,
                179
            },
            {
                byte.MaxValue,
                248,
                192
            },
            {
                byte.MaxValue,
                249,
                203
            },
            {
                byte.MaxValue,
                251,
                216
            },
            {
                byte.MaxValue,
                253,
                228
            },
            {
                byte.MaxValue,
                254,
                239
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                249
            }
        };

        // Token: 0x040003F2 RID: 1010
        public static byte[,] rainTable = new byte[,]
        {
            {
                0,
                0,
                0
            },
            {
                0,
                0,
                0
            },
            {
                15,
                0,
                15
            },
            {
                31,
                0,
                31
            },
            {
                47,
                0,
                47
            },
            {
                63,
                0,
                63
            },
            {
                79,
                0,
                79
            },
            {
                95,
                0,
                95
            },
            {
                111,
                0,
                111
            },
            {
                127,
                0,
                127
            },
            {
                143,
                0,
                143
            },
            {
                159,
                0,
                159
            },
            {
                175,
                0,
                175
            },
            {
                191,
                0,
                191
            },
            {
                207,
                0,
                207
            },
            {
                223,
                0,
                223
            },
            {
                239,
                0,
                239
            },
            {
                byte.MaxValue,
                0,
                byte.MaxValue
            },
            {
                239,
                0,
                250
            },
            {
                223,
                0,
                245
            },
            {
                207,
                0,
                240
            },
            {
                191,
                0,
                236
            },
            {
                175,
                0,
                231
            },
            {
                159,
                0,
                226
            },
            {
                143,
                0,
                222
            },
            {
                127,
                0,
                217
            },
            {
                111,
                0,
                212
            },
            {
                95,
                0,
                208
            },
            {
                79,
                0,
                203
            },
            {
                63,
                0,
                198
            },
            {
                47,
                0,
                194
            },
            {
                31,
                0,
                189
            },
            {
                15,
                0,
                184
            },
            {
                0,
                0,
                180
            },
            {
                0,
                15,
                184
            },
            {
                0,
                31,
                189
            },
            {
                0,
                47,
                194
            },
            {
                0,
                63,
                198
            },
            {
                0,
                79,
                203
            },
            {
                0,
                95,
                208
            },
            {
                0,
                111,
                212
            },
            {
                0,
                127,
                217
            },
            {
                0,
                143,
                222
            },
            {
                0,
                159,
                226
            },
            {
                0,
                175,
                231
            },
            {
                0,
                191,
                236
            },
            {
                0,
                207,
                240
            },
            {
                0,
                223,
                245
            },
            {
                0,
                239,
                250
            },
            {
                0,
                byte.MaxValue,
                byte.MaxValue
            },
            {
                0,
                245,
                239
            },
            {
                0,
                236,
                223
            },
            {
                0,
                227,
                207
            },
            {
                0,
                218,
                191
            },
            {
                0,
                209,
                175
            },
            {
                0,
                200,
                159
            },
            {
                0,
                191,
                143
            },
            {
                0,
                182,
                127
            },
            {
                0,
                173,
                111
            },
            {
                0,
                164,
                95
            },
            {
                0,
                155,
                79
            },
            {
                0,
                146,
                63
            },
            {
                0,
                137,
                47
            },
            {
                0,
                128,
                31
            },
            {
                0,
                119,
                15
            },
            {
                0,
                110,
                0
            },
            {
                15,
                118,
                0
            },
            {
                30,
                127,
                0
            },
            {
                45,
                135,
                0
            },
            {
                60,
                144,
                0
            },
            {
                75,
                152,
                0
            },
            {
                90,
                161,
                0
            },
            {
                105,
                169,
                0
            },
            {
                120,
                178,
                0
            },
            {
                135,
                186,
                0
            },
            {
                150,
                195,
                0
            },
            {
                165,
                203,
                0
            },
            {
                180,
                212,
                0
            },
            {
                195,
                220,
                0
            },
            {
                210,
                229,
                0
            },
            {
                225,
                237,
                0
            },
            {
                240,
                246,
                0
            },
            {
                byte.MaxValue,
                byte.MaxValue,
                0
            },
            {
                251,
                240,
                0
            },
            {
                248,
                225,
                0
            },
            {
                245,
                210,
                0
            },
            {
                242,
                195,
                0
            },
            {
                238,
                180,
                0
            },
            {
                235,
                165,
                0
            },
            {
                232,
                150,
                0
            },
            {
                229,
                135,
                0
            },
            {
                225,
                120,
                0
            },
            {
                222,
                105,
                0
            },
            {
                219,
                90,
                0
            },
            {
                216,
                75,
                0
            },
            {
                212,
                60,
                0
            },
            {
                209,
                45,
                0
            },
            {
                206,
                30,
                0
            },
            {
                203,
                15,
                0
            },
            {
                200,
                0,
                0
            },
            {
                202,
                11,
                11
            },
            {
                205,
                23,
                23
            },
            {
                207,
                34,
                34
            },
            {
                210,
                46,
                46
            },
            {
                212,
                57,
                57
            },
            {
                215,
                69,
                69
            },
            {
                217,
                81,
                81
            },
            {
                220,
                92,
                92
            },
            {
                222,
                104,
                104
            },
            {
                225,
                115,
                115
            },
            {
                227,
                127,
                127
            },
            {
                230,
                139,
                139
            },
            {
                232,
                150,
                150
            },
            {
                235,
                162,
                162
            },
            {
                237,
                173,
                173
            },
            {
                240,
                185,
                185
            },
            {
                242,
                197,
                197
            },
            {
                245,
                208,
                208
            },
            {
                247,
                220,
                220
            },
            {
                250,
                231,
                231
            },
            {
                252,
                243,
                243
            },
            {
                252,
                243,
                243
            },
            {
                252,
                243,
                243
            },
            {
                252,
                243,
                243
            },
            {
                252,
                243,
                243
            },
            {
                252,
                243,
                243
            },
            {
                252,
                243,
                243
            },
            {
                252,
                243,
                243
            }
        };

        public static byte GrayToPseudoColor(int gray, int type, int channel)
        {
            if (type == 1)
            {
                int num = gray / 2;
                return ironTable[num, channel];
            }
            if (type == 2)
            {
                int num2 = gray / 2;
                return rainTable[num2, channel];
            }
            channel = 2 - channel;
            if (gray >= 0 && gray <= 63)
            {
                if (channel == 2)
                {
                    return 0;
                }
                if (channel == 1)
                {
                    return (byte)(254 - 4 * gray);
                }
                return byte.MaxValue;
            }
            else if (gray >= 64 && gray <= 127)
            {
                if (channel == 2)
                {
                    return 0;
                }
                if (channel == 1)
                {
                    return (byte)(4 * gray - 254);
                }
                return (byte)(510 - 4 * gray);
            }
            else if (gray >= 128 && gray <= 191)
            {
                if (channel == 2)
                {
                    return (byte)(4 * gray - 510);
                }
                if (channel == 1)
                {
                    return byte.MaxValue;
                }
                return 0;
            }
            else
            {
                if (gray < 192 || gray > 255)
                {
                    return 0;
                }
                if (channel == 2)
                {
                    return byte.MaxValue;
                }
                if (channel == 1)
                {
                    return (byte)(1022 - 4 * gray);
                }
                return 0;
            }
        }

    }
}
