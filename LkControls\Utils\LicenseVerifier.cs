﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
namespace InduVision.LkControls.Utils
{
    class LicenseVerifier
    {
        // ✅ 确保密钥长度为 16、24 或 32 字节（与 Python 版相同）
        private static readonly byte[] SECRET_KEY = Encoding.UTF8.GetBytes("MySecretKey12345");

        public static string errorMessage = null;

        public static bool VerifyLicense(string deviceId)
        {
            if (!File.Exists("license.dat"))
            {
                errorMessage = "未找到授权文件 license.dat！";
                return false;
            }

            try
            {
                // 读取授权文件
                string encryptedData = File.ReadAllText("license.dat");
                string decryptedJson = Decrypt(encryptedData);

                // 解析授权数据
                var license = JsonConvert.DeserializeObject<LicenseData>(decryptedJson);
                Debug.WriteLine("license: " + license);
                if (license == null)
                {
                    errorMessage = "解析授权文件失败！";
                    return false;
                }

                // 检查设备ID
                if (license.device_id != deviceId)
                {
                    errorMessage = $"设备ID 不匹配！当前设备: {deviceId}，授权设备: {license.device_id}";
                    return false;
                }

                // 检查有效期
                DateTime expiryDate = DateTimeOffset.FromUnixTimeSeconds(license.expiry_timestamp).DateTime;
                if (expiryDate < DateTime.Now)
                {
                    errorMessage = $"授权已过期！有效期至 {expiryDate:yyyy-MM-dd}";
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"授权验证异常: {ex.Message}";
                return false;
            }
        }

        private static string Decrypt(string encryptedData)
        {
            byte[] encryptedBytes = Convert.FromBase64String(encryptedData);

            using (Aes aes = Aes.Create())
            {
                aes.Key = SECRET_KEY;
                aes.Mode = CipherMode.ECB;
                aes.Padding = PaddingMode.PKCS7;

                using (ICryptoTransform decryptor = aes.CreateDecryptor())
                {
                    byte[] decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
                    return Encoding.UTF8.GetString(decryptedBytes);
                }
            }
        }
    }

    // 授权数据结构
    class LicenseData
    {
        public string device_id { get; set; }
        public long expiry_timestamp { get; set; }
    }
}
