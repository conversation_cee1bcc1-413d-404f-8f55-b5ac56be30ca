﻿using DicomViewerDemo;
using FellowOakDicom.IO.Buffer;
using FellowOakDicom;
using InduVision;
using InduVision.LkControls.FPD.Nice;
using LkControls.LayoutControls;
using Newtonsoft.Json.Linq;
using OpenCvSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Vintasoft.Imaging.Annotation.Dicom.UI.VisualTools;
using Vintasoft.Imaging.Annotation.Measurements;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace InduVision.LkControls.FPD.IRay
{
    public partial class StaticOperatePanel : UserControl
    {
        MainForm mainForm;
        public event Action<JObject> DisconnectedDevice;
        Timer timer;
        int countdownSeconds;
        public bool isCapturing = false;
        public StaticOperatePanel()
        {
            InitializeComponent();
            overlayNumber.ValueChanged += OverlayNumber_ValueChanged;
            isOverlay.CheckedChanged += IsOverlay_CheckedChanged;
            captureMode.SelectedIndexChanged += CaptureMode_SelectedIndexChanged;
            overlayMode.SelectedIndexChanged += OverlayMode_SelectedIndexChanged;
            overlayMode.SelectedIndex = 0;
            captureMode.SelectedIndex = 0;
        }

        private void OverlayMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (overlayMode.SelectedIndex == 0)
            {
                FPDPanel.overlayMode = 0;
            }
            else if (overlayMode.SelectedIndex == 1)
            {
                FPDPanel.overlayMode = 1;
            }
        }

        private void CaptureMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (captureMode.SelectedIndex == 0)
            {
                openButton.Text = "准备";
                captureButton.Text = "采集";
            }
            else if (captureMode.SelectedIndex == 1)
            {
                openButton.Text = "开始";
                captureButton.Text = "停止";
            }
        }

        private void IsOverlay_CheckedChanged(object sender, EventArgs e)
        {
            MainForm.isOverlay = isOverlay.Checked;

            if (MainForm.isOverlay)
            {
                mainForm.topPanel1.updateImageInfo(MainForm.IMAGE_WIDTH, MainForm.IMAGE_HEIGHT, "启用");
            }
            else {
                MainForm.matCache.Clear();
                mainForm.topPanel1.updateImageInfo(MainForm.IMAGE_WIDTH, MainForm.IMAGE_HEIGHT, "未启用");
            }
        }

        private void OverlayNumber_ValueChanged(object sender, EventArgs e)
        {
            MainForm.overlayNumber = (int)overlayNumber.Value;
        }

        public MainForm MainForm
        {
            set
            {
                mainForm = value;
            }
        }

        public void manualMode()
        {
            namePanel.Visible = true;
            openButton.Visible = true;
            captureButton.Visible = true;
            saveButton.Visible = true;
        }

        public void halfAutoMode()
        {
            namePanel.Visible = true;
            openButton.Visible = false;
            captureButton.Visible = true;
            saveButton.Visible = true;
        }

        private void disconnectButton_Click(object sender, EventArgs e)
        {
            JObject jObject = new JObject
            { };

            StaticFPD.disconnect();

            DisconnectedDevice?.Invoke(jObject);
        }

        private void openButton_Click(object sender, EventArgs e)
        {
            if (captureMode.SelectedIndex == 1)
            {
                isCapturing = true;

                mainForm.toolsPanel.measureTitlePanel.Visible = false;
                mainForm.toolsPanel.measureButtonPanel.Visible = false;
                mainForm.toolsPanel.annotationTitlePanel.Visible = false;
                mainForm.toolsPanel.annotationButtonPanel.Visible = false;
                mainForm.toolsPanel.WallThicknessBtn.Visible = false;
                mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = false;
                mainForm.toolsPanel.enhancementTitlePanel.Visible = false;
                mainForm.toolsPanel.enhancementButtonPanel.Visible = false;
            }

            openWindow();
        }

        private void captureButton_Click(object sender, EventArgs e)
        {
            if (captureMode.SelectedIndex == 1)
            {
                isCapturing = false;

                mainForm.toolsPanel.measureTitlePanel.Visible = true;
                mainForm.toolsPanel.measureButtonPanel.Visible = true;
                mainForm.toolsPanel.annotationTitlePanel.Visible = true;
                mainForm.toolsPanel.annotationButtonPanel.Visible = true;
                mainForm.toolsPanel.WallThicknessBtn.Visible = true;
                mainForm.toolsPanel.WallThicknessBtn.Parent.Visible = true;
                mainForm.toolsPanel.enhancementTitlePanel.Visible = true;
                mainForm.toolsPanel.enhancementButtonPanel.Visible = true;
            }

            capture();
        }

        public void capture()
        {
            if (timer != null)
            {
                timer.Stop();
            }

            mainForm.toolsPanel.reset();

            DicomAnnotatedViewerTool dicomAnnotatedViewerTool = mainForm.getDicomAnnotatedViewerTool();
            DicomAnnotationTool dicomAnnotationTool = dicomAnnotatedViewerTool.DicomAnnotationTool;
            if (dicomAnnotationTool.AnnotationViewCollection.Count > 0)
            {
                dicomAnnotationTool.AnnotationViewCollection.Clear();
                dicomAnnotationTool.AnnotationDataCollection.Clear();
            }
            ImageMeasureTool imageMeasureTool = dicomAnnotatedViewerTool.ImageMeasureTool;
            if (imageMeasureTool.AnnotationViewCollection.Count > 0)
            {
                imageMeasureTool.AnnotationViewCollection.Clear();
            }

            bool result = StaticFPD.capture();
            if (result)
            {
                //saveButton.Enabled = false;
                mainForm.topPanel1.updateCaptureInfo("图像开始采集");
                mainForm.toolsPanel.reset();
            }
        }

        private void saveButton_Click(object sender, EventArgs e)
        {
            string name;

            if (!string.IsNullOrEmpty(nameTextBox.Text))
            {
                name = nameTextBox.Text;  // 如果有值，使用 textBox 的内容
            }
            else
            {
                // 如果没有值，使用当前时间（年月日时分秒）
                name = DateTime.Now.ToString("yyyyMMddHHmmss");
            }

            if (mainForm.WorkDirectory != null)
            {
                mainForm.topPanel1.updateCaptureInfo("图像保存中...");

                string rawName = name + ".dcm";
                string thumbnailName = name + ".jpg";
                string projectPath = Path.Combine(mainForm.WorkDirectory, ".visionnova");
                string thumbnailPath = Path.Combine(projectPath, "thumbnail");
                string thumbnailFile = Path.Combine(thumbnailPath, thumbnailName);
                string destFile = Path.Combine(mainForm.WorkDirectory, rawName);

                var dataset = new DicomDataset(FellowOakDicom.DicomTransferSyntax.ImplicitVRLittleEndian)
                {
                    //{ DicomTag.PatientID, "20240919" },
                    { DicomTag.StudyDate, DateTime.Now.ToString("yyyyMMdd") },
                    { DicomTag.StudyTime, DateTime.Now.ToString("HHmmss") },
                    { DicomTag.SpecificCharacterSet,  "GB18030"},
                    { DicomTag.StudyInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                    { DicomTag.SeriesInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                    { DicomTag.SOPInstanceUID, DicomUIDGenerator.GenerateDerivedFromUUID() },
                    { DicomTag.SOPClassUID, DicomUID.SecondaryCaptureImageStorage },
                    { DicomTag.PhotometricInterpretation, FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2.Value },
                    { DicomTag.Rows, (ushort)MainForm.IMAGE_HEIGHT },
                    { DicomTag.Columns, (ushort)MainForm.IMAGE_WIDTH },
                    { DicomTag.BitsAllocated, (ushort)16 },
                    { DicomTag.BitsStored, (ushort)16 },
                    { DicomTag.HighBit, (ushort)15 },
                    { DicomTag.PixelRepresentation, (ushort)0 }, // 0 = unsigned, 1 = signed
                    { DicomTag.SamplesPerPixel, (ushort)1 }
                };

                // Add pixel data
                var pixelData = FellowOakDicom.Imaging.DicomPixelData.Create(dataset, true);
                pixelData.BitsStored = 16;
                pixelData.HighBit = 15;
                pixelData.PixelRepresentation = FellowOakDicom.Imaging.PixelRepresentation.Unsigned;
                pixelData.SamplesPerPixel = 1;
                pixelData.PlanarConfiguration = FellowOakDicom.Imaging.PlanarConfiguration.Interleaved;
                pixelData.Width = (ushort)MainForm.IMAGE_WIDTH;
                pixelData.Height = (ushort)MainForm.IMAGE_HEIGHT;
                pixelData.PhotometricInterpretation = FellowOakDicom.Imaging.PhotometricInterpretation.Monochrome2;

                byte[] byteData = new byte[StaticFPD.imageData.Length * 2];
                Buffer.BlockCopy(StaticFPD.imageData, 0, byteData, 0, byteData.Length);

                // Assign image data to DICOM pixel data
                pixelData.AddFrame(new MemoryByteBuffer(byteData));

                // Save the DICOM file
                FellowOakDicom.DicomFile dicomFile = new FellowOakDicom.DicomFile(dataset);

                dicomFile.Save(destFile);

                //生成缩略图
                Mat rawMat = new Mat(MainForm.IMAGE_HEIGHT, MainForm.IMAGE_WIDTH, MainForm.IMAGE_MATTYPE);

                int length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH;

                if (MainForm.IMAGE_MATTYPE == MatType.CV_16U)
                {
                    length = MainForm.IMAGE_HEIGHT * MainForm.IMAGE_WIDTH;
                }

                Marshal.Copy(StaticFPD.imageData, 0, rawMat.Data, length);

                // 计算新的宽度和高度
                int newWidth = (int)(MainForm.IMAGE_WIDTH * 0.1);
                int newHeight = (int)(MainForm.IMAGE_HEIGHT * 0.1);

                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                byte[] jpegData;
                // 将16位灰度图转换为8位灰度图
                rawMat.ConvertTo(rawMat, MatType.CV_8U, 255.0 / 65535.0); // 缩放因子：65535 -> 255

                Cv2.Resize(rawMat, rawMat, new OpenCvSharp.Size(newWidth, newHeight));

                Cv2.ImEncode(".jpg", rawMat, out jpegData);

                // 保存为文件或其他处理
                File.WriteAllBytes(thumbnailFile, jpegData);

                saveButton.IsPressed = false;
                nameTextBox.Text = "";

                mainForm.leftPanel.thumbnailViewer1.RefreshThumbnails();

                MainForm.currentFileName = name;

                //mainForm.leftPanel.thumbnailViewer1.SelectThumbnailByIndex(0);

                mainForm.topPanel1.updateCaptureInfo("图像已保存");
            }
            else {
                MessageBox.Show(
                    "请打开项目后再进行该操作",                // 错误信息
                    "操作异常",                  // 弹窗标题
                    MessageBoxButtons.OK,    // 按钮类型：只有一个“确定”按钮
                    MessageBoxIcon.Information    // 弹窗图标：错误图标
                );
            }

        }

        public void openWindow()
        {

            bool result = StaticFPD.prepare();
            if (result)
            {
                countdownSeconds = (int)captureTime.Value;
                if (timer == null)
                {
                    timer = new Timer();
                    timer.Interval = 1000;
                    timer.Tick += Timer_Tick;
                    timer.Start();
                }
                else
                {
                    timer.Start();
                }
            }
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            countdownSeconds--;

            if (countdownSeconds <= 0)
            {
                timer.Stop();
                capture();
            }
            else
            {
                mainForm.topPanel1.updateCaptureInfo("采集倒计时: " + countdownSeconds + " 秒");
            }
        }

    }
}
