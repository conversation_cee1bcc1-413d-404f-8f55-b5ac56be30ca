﻿using DemosCommonCode.Imaging;
using DicomViewerDemo;
using InduVision.LkControls.CommonControls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Vintasoft.Imaging.Annotation;
using Vintasoft.Imaging.Annotation.Dicom.UI.VisualTools;
using Vintasoft.Imaging.Annotation.UI;
using Vintasoft.Imaging.Dicom.UI.VisualTools;
using Vintasoft.Imaging.UI;

namespace InduVision.LkControls.LayoutControls
{
    public partial class DuplexIQIPanel : UserControl
    {
        MainForm mainForm;
        public DuplexIQIPanel(MainForm _mainForm)
        {
            InitializeComponent();
            mainForm = _mainForm;
            textBox1.Text = MainForm.DUPLEXIQI_WIDTH.ToString();
        }

        private void applyButton_Click(object sender, EventArgs e)
        {

            if (int.TryParse(textBox1.Text, out MainForm.DUPLEXIQI_WIDTH))
            {
                if (MainForm.DUPLEXIQI_WIDTH <= 0) {
                    MainForm.DUPLEXIQI_WIDTH = 1;
                    textBox1.Text = "1";
                } else if (MainForm.DUPLEXIQI_WIDTH > 100) {
                    MainForm.DUPLEXIQI_WIDTH = 100;
                    textBox1.Text = "100";
                }

                DicomAnnotatedViewerToolStrip dicomAnnotatedViewerToolStrip1 = mainForm.getDicomAnnotatedViewerToolStrip1();
                DicomAnnotatedViewerTool dicomAnnotatedViewerTool = dicomAnnotatedViewerToolStrip1.DicomAnnotatedViewerTool;
                AnnotationData[] annotationDatas = dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationDataCollection.ToArray<AnnotationData>();

                for (int i = 0; i < annotationDatas.Length; i++) {
                    AnnotationData annotationData = annotationDatas[i];
                    if (annotationData.GetType() == typeof(DuplexIQIAnnotationData)) {
                        dicomAnnotatedViewerTool.DicomAnnotationTool.AnnotationDataCollection.Remove(annotationData);
                        break;
                    }
                }

                AnnotationsToolStrip annotationsToolStrip1 = mainForm.getAnnotationsToolStrip1();
                annotationsToolStrip1.applyDuplexIQI = true;
                if (annotationsToolStrip1.duplexIQIForm != null)
                {
                    annotationsToolStrip1.duplexIQIForm.Close();
                }

                annotationsToolStrip1.cancelBuilding();
                annotationsToolStrip1.isCancel = false;
                annotationsToolStrip1.BuildAnnotation("DuplexIQI");
            }
            else {
                MessageBox.Show(
                        "请输入有效的整数",                // 错误信息
                        "操作异常",                  // 弹窗标题
                        MessageBoxButtons.OK,    // 按钮类型：只有一个“确定”按钮
                        MessageBoxIcon.Information    // 弹窗图标：错误图标
                    );
            }
        }
    }
}
