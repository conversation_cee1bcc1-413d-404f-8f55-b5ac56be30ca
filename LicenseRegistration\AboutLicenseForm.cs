using System;
using System.Drawing;
using System.Windows.Forms;
using License;
using System.Collections;

namespace InduVision.LicenseRegistration
{
    /// <summary>
    /// 关于许可证信息窗体
    /// </summary>
    public partial class AboutLicenseForm : Form
    {
        private readonly LicenseCheckService _licenseCheckService;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public AboutLicenseForm()
        {
            InitializeComponent();
            
            // 初始化许可证服务
            _licenseCheckService = new LicenseCheckService();
            
            // 加载许可证信息
            LoadLicenseInfo();
        }

        private Label labelTitle;
        private Label labelLicenseStatus;
        private Label labelLicenseType;
        private Label labelVersion;
        
        private GroupBox groupBoxHardware;
        private Label labelHardwareID;
        private TextBox textBoxHardwareID;
        private Button btnCopyHardwareID;
        private Label labelHardwareLockStatus;
        private Label labelLicenseHardwareID;
        private Label labelHardwareMatchStatus;
        
        private GroupBox groupBoxTime;
        private Label labelTimeLockStatus;
        private Label labelExpirationDate;
        private Label labelRemainingDays;
        
        private Button btnRefresh;
        private Button buttonRegister;
        private Button buttonOK;
        private Button buttonRegisterFull;
        
        private ToolTip toolTip;


        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.labelTitle = new System.Windows.Forms.Label();
            this.labelLicenseStatus = new System.Windows.Forms.Label();
            this.labelLicenseType = new System.Windows.Forms.Label();
            this.labelVersion = new System.Windows.Forms.Label();
            
            this.groupBoxHardware = new System.Windows.Forms.GroupBox();
            this.labelHardwareMatchStatus = new System.Windows.Forms.Label();
            this.labelLicenseHardwareID = new System.Windows.Forms.Label();
            this.labelHardwareLockStatus = new System.Windows.Forms.Label();
            this.btnCopyHardwareID = new System.Windows.Forms.Button();
            this.textBoxHardwareID = new System.Windows.Forms.TextBox();
            this.labelHardwareID = new System.Windows.Forms.Label();
            
            this.groupBoxTime = new System.Windows.Forms.GroupBox();
            this.labelRemainingDays = new System.Windows.Forms.Label();
            this.labelExpirationDate = new System.Windows.Forms.Label();
            this.labelTimeLockStatus = new System.Windows.Forms.Label();
            
            this.btnRefresh = new System.Windows.Forms.Button();
            this.buttonRegister = new System.Windows.Forms.Button();
            this.buttonOK = new System.Windows.Forms.Button();
            this.buttonRegisterFull = new System.Windows.Forms.Button();
            
            this.toolTip = new System.Windows.Forms.ToolTip();
            
            this.groupBoxHardware.SuspendLayout();
            this.groupBoxTime.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelTitle
            // 
            this.labelTitle.AutoSize = true;
            this.labelTitle.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelTitle.Location = new System.Drawing.Point(12, 9);
            this.labelTitle.Name = "labelTitle";
            this.labelTitle.Size = new System.Drawing.Size(110, 16);
            this.labelTitle.TabIndex = 0;
            this.labelTitle.Text = "软件授权信息";
            // 
            // labelLicenseStatus
            // 
            this.labelLicenseStatus.AutoSize = true;
            this.labelLicenseStatus.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelLicenseStatus.ForeColor = System.Drawing.Color.Red;
            this.labelLicenseStatus.Location = new System.Drawing.Point(12, 35);
            this.labelLicenseStatus.Name = "labelLicenseStatus";
            this.labelLicenseStatus.Size = new System.Drawing.Size(110, 16);
            this.labelLicenseStatus.TabIndex = 1;
            this.labelLicenseStatus.Text = "授权状态：未授权";
            // 
            // labelLicenseType
            // 
            this.labelLicenseType.AutoSize = true;
            this.labelLicenseType.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelLicenseType.Location = new System.Drawing.Point(12, 60);
            this.labelLicenseType.Name = "labelLicenseType";
            this.labelLicenseType.Size = new System.Drawing.Size(98, 14);
            this.labelLicenseType.TabIndex = 2;
            this.labelLicenseType.Text = "授权类型：未授权";
            // 
            // labelVersion
            // 
            this.labelVersion.AutoSize = true;
            this.labelVersion.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelVersion.ForeColor = System.Drawing.Color.Gray;
            this.labelVersion.Location = new System.Drawing.Point(300, 9);
            this.labelVersion.Name = "labelVersion";
            this.labelVersion.Size = new System.Drawing.Size(98, 14);
            this.labelVersion.TabIndex = 3;
            this.labelVersion.Text = "软件版本：";
            // 
            // groupBoxHardware
            // 
            this.groupBoxHardware.Controls.Add(this.labelHardwareMatchStatus);
            this.groupBoxHardware.Controls.Add(this.labelLicenseHardwareID);
            this.groupBoxHardware.Controls.Add(this.labelHardwareLockStatus);
            this.groupBoxHardware.Controls.Add(this.btnCopyHardwareID);
            this.groupBoxHardware.Controls.Add(this.textBoxHardwareID);
            this.groupBoxHardware.Controls.Add(this.labelHardwareID);
            this.groupBoxHardware.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxHardware.Location = new System.Drawing.Point(12, 85);
            this.groupBoxHardware.Name = "groupBoxHardware";
            this.groupBoxHardware.Size = new System.Drawing.Size(420, 120);
            this.groupBoxHardware.TabIndex = 4;
            this.groupBoxHardware.TabStop = false;
            this.groupBoxHardware.Text = "硬件信息";
            // 
            // labelHardwareID
            // 
            this.labelHardwareID.AutoSize = true;
            this.labelHardwareID.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelHardwareID.Location = new System.Drawing.Point(6, 25);
            this.labelHardwareID.Name = "labelHardwareID";
            this.labelHardwareID.Size = new System.Drawing.Size(77, 14);
            this.labelHardwareID.TabIndex = 0;
            this.labelHardwareID.Text = "当前硬件码:";
            // 
            // textBoxHardwareID
            // 
            this.textBoxHardwareID.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBoxHardwareID.Location = new System.Drawing.Point(89, 23);
            this.textBoxHardwareID.Name = "textBoxHardwareID";
            this.textBoxHardwareID.ReadOnly = true;
            this.textBoxHardwareID.Size = new System.Drawing.Size(272, 21);
            this.textBoxHardwareID.TabIndex = 1;
            // 
            // btnCopyHardwareID
            // 
            this.btnCopyHardwareID.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCopyHardwareID.Location = new System.Drawing.Point(367, 22);
            this.btnCopyHardwareID.Name = "btnCopyHardwareID";
            this.btnCopyHardwareID.Size = new System.Drawing.Size(45, 23);
            this.btnCopyHardwareID.TabIndex = 2;
            this.btnCopyHardwareID.Text = "复制";
            this.btnCopyHardwareID.UseVisualStyleBackColor = true;
            this.btnCopyHardwareID.Click += new System.EventHandler(this.btnCopyHardwareID_Click);
            // 
            // labelHardwareLockStatus
            // 
            this.labelHardwareLockStatus.AutoSize = true;
            this.labelHardwareLockStatus.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelHardwareLockStatus.Location = new System.Drawing.Point(6, 55);
            this.labelHardwareLockStatus.Name = "labelHardwareLockStatus";
            this.labelHardwareLockStatus.Size = new System.Drawing.Size(77, 14);
            this.labelHardwareLockStatus.TabIndex = 3;
            this.labelHardwareLockStatus.Text = "硬件绑定：";
            // 
            // labelLicenseHardwareID
            // 
            this.labelLicenseHardwareID.AutoSize = true;
            this.labelLicenseHardwareID.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelLicenseHardwareID.Location = new System.Drawing.Point(6, 80);
            this.labelLicenseHardwareID.Name = "labelLicenseHardwareID";
            this.labelLicenseHardwareID.Size = new System.Drawing.Size(77, 14);
            this.labelLicenseHardwareID.TabIndex = 4;
            this.labelLicenseHardwareID.Text = "授权硬件码:";
            this.labelLicenseHardwareID.Visible = false;
            // 
            // labelHardwareMatchStatus
            // 
            this.labelHardwareMatchStatus.AutoSize = true;
            this.labelHardwareMatchStatus.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelHardwareMatchStatus.Location = new System.Drawing.Point(6, 100);
            this.labelHardwareMatchStatus.Name = "labelHardwareMatchStatus";
            this.labelHardwareMatchStatus.Size = new System.Drawing.Size(105, 14);
            this.labelHardwareMatchStatus.TabIndex = 5;
            this.labelHardwareMatchStatus.Text = "硬件匹配状态:";
            this.labelHardwareMatchStatus.Visible = false;
            // 
            // groupBoxTime
            // 
            this.groupBoxTime.Controls.Add(this.labelRemainingDays);
            this.groupBoxTime.Controls.Add(this.labelExpirationDate);
            this.groupBoxTime.Controls.Add(this.labelTimeLockStatus);
            this.groupBoxTime.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxTime.Location = new System.Drawing.Point(12, 215);
            this.groupBoxTime.Name = "groupBoxTime";
            this.groupBoxTime.Size = new System.Drawing.Size(420, 100);
            this.groupBoxTime.TabIndex = 5;
            this.groupBoxTime.TabStop = false;
            this.groupBoxTime.Text = "有效期信息";
            // 
            // labelTimeLockStatus
            // 
            this.labelTimeLockStatus.AutoSize = true;
            this.labelTimeLockStatus.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelTimeLockStatus.Location = new System.Drawing.Point(6, 25);
            this.labelTimeLockStatus.Name = "labelTimeLockStatus";
            this.labelTimeLockStatus.Size = new System.Drawing.Size(77, 14);
            this.labelTimeLockStatus.TabIndex = 0;
            this.labelTimeLockStatus.Text = "时间限制：";
            // 
            // labelExpirationDate
            // 
            this.labelExpirationDate.AutoSize = true;
            this.labelExpirationDate.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelExpirationDate.Location = new System.Drawing.Point(6, 50);
            this.labelExpirationDate.Name = "labelExpirationDate";
            this.labelExpirationDate.Size = new System.Drawing.Size(77, 14);
            this.labelExpirationDate.TabIndex = 1;
            this.labelExpirationDate.Text = "有效期至：";
            this.labelExpirationDate.Visible = false;
            // 
            // labelRemainingDays
            // 
            this.labelRemainingDays.AutoSize = true;
            this.labelRemainingDays.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelRemainingDays.Location = new System.Drawing.Point(6, 75);
            this.labelRemainingDays.Name = "labelRemainingDays";
            this.labelRemainingDays.Size = new System.Drawing.Size(77, 14);
            this.labelRemainingDays.TabIndex = 2;
            this.labelRemainingDays.Text = "剩余天数：";
            this.labelRemainingDays.Visible = false;
            // 
            // btnRefresh
            // 
            this.btnRefresh.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRefresh.Location = new System.Drawing.Point(12, 325);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(80, 30);
            this.btnRefresh.TabIndex = 6;
            this.btnRefresh.Text = "刷新";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // buttonRegister
            // 
            this.buttonRegister.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonRegister.Location = new System.Drawing.Point(182, 325);
            this.buttonRegister.Name = "buttonRegister";
            this.buttonRegister.Size = new System.Drawing.Size(80, 30);
            this.buttonRegister.TabIndex = 7;
            this.buttonRegister.Text = "注册";
            this.buttonRegister.UseVisualStyleBackColor = true;
            this.buttonRegister.Click += new System.EventHandler(this.buttonRegister_Click);
            // 
            // buttonOK
            // 
            this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.buttonOK.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonOK.Location = new System.Drawing.Point(352, 325);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(80, 30);
            this.buttonOK.TabIndex = 8;
            this.buttonOK.Text = "确定";
            this.buttonOK.UseVisualStyleBackColor = true;
            
            // buttonRegisterFull
            // 
            this.buttonRegisterFull.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonRegisterFull.Location = new System.Drawing.Point(97, 325);
            this.buttonRegisterFull.Name = "buttonRegisterFull";
            this.buttonRegisterFull.Size = new System.Drawing.Size(80, 30);
            this.buttonRegisterFull.TabIndex = 9;
            this.buttonRegisterFull.Text = "注册正式版";
            this.buttonRegisterFull.UseVisualStyleBackColor = false;
            this.buttonRegisterFull.BackColor = System.Drawing.Color.LightBlue;
            this.buttonRegisterFull.Click += new System.EventHandler(this.buttonRegisterFull_Click);
            this.buttonRegisterFull.Visible = false; // 默认不可见
            // 
            // AboutLicenseForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(444, 371);
            this.Controls.Add(this.buttonOK);
            this.Controls.Add(this.buttonRegister);
            this.Controls.Add(this.btnRefresh);
            this.Controls.Add(this.buttonRegisterFull);
            this.Controls.Add(this.groupBoxTime);
            this.Controls.Add(this.groupBoxHardware);
            this.Controls.Add(this.labelVersion);
            this.Controls.Add(this.labelLicenseType);
            this.Controls.Add(this.labelLicenseStatus);
            this.Controls.Add(this.labelTitle);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AboutLicenseForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "InduVision | 软件授权信息";
            this.groupBoxHardware.ResumeLayout(false);
            this.groupBoxHardware.PerformLayout();
            this.groupBoxTime.ResumeLayout(false);
            this.groupBoxTime.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
            
            // 设置版本号
            this.labelVersion.Text = "软件版本：" + MainForm.VERSION;
        }

        /// <summary>
        /// 加载许可证信息
        /// </summary>
        private void LoadLicenseInfo()
        {
            try
            {
                // 获取当前硬件码
                string currentHardwareId = Status.GetHardwareID(true, true, true, false);
                textBoxHardwareID.Text = currentHardwareId;
                
                // 检查授权状态
                bool isLicensed = Status.Licensed;
                bool hardwareLockEnabled = Status.Hardware_Lock_Enabled;
                bool timeLockEnabled = Status.Expiration_Date_Lock_Enable;
                bool evaluationLockEnabled = Status.Evaluation_Lock_Enabled;

                // 检查是否为试用版 - 只有启用评估模式才判断为试用版
                bool isTrial = evaluationLockEnabled;
                              
                // 如果是试用版，显示注册正式版按钮
                buttonRegisterFull.Visible = isTrial;
                
                // 强制重新加载License.dll状态，确保最新状态
                bool _ = Status.Licensed; // 读取此属性会刷新状态

                // 设置授权状态
                if (isLicensed)
                {
                    labelLicenseStatus.Text = "授权状态：已授权";
                    labelLicenseStatus.ForeColor = Color.Green;
                    buttonRegister.Visible = !isTrial; // 如果是试用版，保留注册按钮
                    
                    // 设置授权类型（添加试用版标识）
                    if (isTrial)
                    {
                        labelLicenseType.Text = "授权类型：试用版";
                        labelLicenseType.ForeColor = Color.Blue;
                    }
                    else
                    {
                        SetLicenseTypeLabel(hardwareLockEnabled, timeLockEnabled);
                        labelLicenseType.ForeColor = SystemColors.ControlText;
                    }
                    
                    // 设置硬件信息
                    SetHardwareInfo(hardwareLockEnabled, currentHardwareId);
                    
                    // 设置时间信息
                    SetTimeInfo(timeLockEnabled, evaluationLockEnabled, isTrial);
                    
                    // 启用分组框
                    groupBoxHardware.Enabled = true;
                    groupBoxTime.Enabled = true;
                }
                else
                {
                    labelLicenseStatus.Text = "授权状态：未授权";
                    labelLicenseStatus.ForeColor = Color.Red;
                    buttonRegister.Visible = true;
                    
                    // 隐藏或重置其他信息
                    labelLicenseType.Text = "授权类型：未授权";
                    groupBoxHardware.Enabled = false;
                    groupBoxTime.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载授权信息失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置授权类型标签
        /// </summary>
        private void SetLicenseTypeLabel(bool hardwareLockEnabled, bool timeLockEnabled)
        {
            if (hardwareLockEnabled && timeLockEnabled)
                labelLicenseType.Text = "授权类型：硬件绑定+时间限制授权";
            else if (hardwareLockEnabled && !timeLockEnabled)
                labelLicenseType.Text = "授权类型：硬件绑定授权";
            else if (!hardwareLockEnabled && timeLockEnabled)
                labelLicenseType.Text = "授权类型：时间限制授权";
            else
                labelLicenseType.Text = "授权类型：完全授权";
        }

        /// <summary>
        /// 设置硬件信息
        /// </summary>
        private void SetHardwareInfo(bool hardwareLockEnabled, string currentHardwareId)
        {
            if (hardwareLockEnabled)
            {
                labelHardwareLockStatus.Text = "硬件绑定：已启用";
                labelLicenseHardwareID.Visible = true;
                labelLicenseHardwareID.Text = "授权硬件码：" + Status.License_HardwareID;
                
                labelHardwareMatchStatus.Visible = true;
                bool isMatched = currentHardwareId == Status.License_HardwareID;
                if (isMatched)
                {
                    labelHardwareMatchStatus.Text = "硬件匹配状态：匹配";
                    labelHardwareMatchStatus.ForeColor = Color.Green;
                }
                else
                {
                    labelHardwareMatchStatus.Text = "硬件匹配状态：不匹配";
                    labelHardwareMatchStatus.ForeColor = Color.Red;
                }
            }
            else
            {
                labelHardwareLockStatus.Text = "硬件绑定：未启用";
                labelLicenseHardwareID.Visible = false;
                labelHardwareMatchStatus.Visible = false;
            }
        }

        /// <summary>
        /// 设置时间信息
        /// </summary>
        private void SetTimeInfo(bool timeLockEnabled, bool evaluationLockEnabled, bool isTrial)
        {
            // 重置所有状态，避免旧状态影响
            labelExpirationDate.Visible = false;
            labelRemainingDays.Visible = false;
            // 如果是评估模式
            if (evaluationLockEnabled)
            {
                labelTimeLockStatus.Text = "评估模式：已启用";
                labelExpirationDate.Visible = true;
                
                if (Status.Evaluation_Type == EvaluationType.Trial_Days)
                {
                    int remainingDays = Status.Evaluation_Time - Status.Evaluation_Time_Current;
                    labelExpirationDate.Text = "评估类型：按天数";
                    
                    labelRemainingDays.Visible = true;
                    labelRemainingDays.Text = $"剩余天数：{remainingDays}天";
                    
                    if (remainingDays <= 7)
                    {
                        labelRemainingDays.ForeColor = Color.Red;
                    }
                    else
                    {
                        labelRemainingDays.ForeColor = SystemColors.ControlText;
                    }
                }
                else // Runtime_Minutes
                {
                    int remainingMinutes = Status.Evaluation_Time - Status.Evaluation_Time_Current;
                    labelExpirationDate.Text = "评估类型：按使用时间";
                    
                    labelRemainingDays.Visible = true;
                    labelRemainingDays.Text = $"剩余时间：{remainingMinutes}分钟";
                    
                    if (remainingMinutes <= 60)
                    {
                        labelRemainingDays.ForeColor = Color.Red;
                    }
                    else
                    {
                        labelRemainingDays.ForeColor = SystemColors.ControlText;
                    }
                }
            }
            // 如果是时间限制模式
            else if (timeLockEnabled)
            {
                labelTimeLockStatus.Text = "时间限制：已启用";
                labelExpirationDate.Visible = true;
                labelExpirationDate.Text = "有效期至：" + Status.Expiration_Date.ToString("yyyy-MM-dd");
                
                labelRemainingDays.Visible = true;
                int remainingDays = (Status.Expiration_Date - DateTime.Now).Days;
                
                if (remainingDays < 0)
                {
                    labelRemainingDays.Text = "剩余天数：已过期";
                    labelRemainingDays.ForeColor = Color.Red;
                }
                else if (remainingDays <= 30)
                {
                    labelRemainingDays.Text = $"剩余天数：{remainingDays}天（即将过期）";
                    labelRemainingDays.ForeColor = Color.Orange;
                }
                else
                {
                    labelRemainingDays.Text = $"剩余天数：{remainingDays}天";
                    labelRemainingDays.ForeColor = SystemColors.ControlText;
                }
            }
            else
            {
                labelTimeLockStatus.Text = "时间限制：未启用";
                labelExpirationDate.Visible = false;
                labelRemainingDays.Visible = false;
            }
            
            // 删除此代码块，因为我们只将评估模式视为试用版
        }

        /// <summary>
        /// 复制硬件码按钮点击事件
        /// </summary>
        private void btnCopyHardwareID_Click(object sender, EventArgs e)
        {
            try
            {
                Clipboard.SetText(textBoxHardwareID.Text);
                toolTip.Show("已复制到剪贴板", btnCopyHardwareID, 2000);
            }
            catch (Exception ex)
            {
                MessageBox.Show("复制失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadLicenseInfo();
        }

        /// <summary>
        /// 注册按钮点击事件
        /// </summary>
        private void buttonRegister_Click(object sender, EventArgs e)
        {
            try
            {
                using (var registerForm = new LicenseRegisterForm())
                {
                    if (registerForm.ShowDialog() == DialogResult.OK)
                    {
                        // 刷新显示
                        LoadLicenseInfo();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("打开注册窗口失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// 注册正式版按钮点击事件
        /// </summary>
        private void buttonRegisterFull_Click(object sender, EventArgs e)
        {
            try
            {
                using (var registerForm = new LicenseRegisterForm())
                {
                    if (registerForm.ShowDialog() == DialogResult.OK)
                    {
                        // 注册成功，刷新显示
                        LoadLicenseInfo();
                        MessageBox.Show("注册正式版成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("打开注册窗口失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
} 