using System;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using System.Drawing.Imaging;
using License;

namespace InduVision.LicenseRegistration
{
    /// <summary>
    /// 软件注册窗体
    /// </summary>
    public partial class LicenseRegisterForm : Form
    {
        private readonly LicenseManager _licenseManager;
        private readonly LicenseCheckService _licenseCheckService;
        private readonly NLog.Logger _log = NLog.LogManager.GetLogger("LicenseRegisterForm");

        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseRegisterForm()
        {
            InitializeComponent();
            
            // 初始化许可证管理器
            _licenseManager = new LicenseManager();
            _licenseCheckService = new LicenseCheckService();
            
            // 初始化界面数据
            InitializeFormData();

            // 绑定事件
            this.Load += LicenseRegisterForm_Load;
            btnRegister.Click += BtnRegister_Click;
            btnImport.Click += BtnImport_Click; 
            btnAbout.Click += BtnAbout_Click;
            btnCopyHardwareID.Click += btnCopyHardwareID_Click;
        }

        /// <summary>
        /// 初始化窗体数据
        /// </summary>
        private void InitializeFormData()
        {
            try
            {
                // 设置硬件ID - 直接使用License.dll的API获取
                txtMachineCode.Text = Status.GetHardwareID(true, true, true, false);
                
                // 设置状态文本
                labelStatus.Text = GetStatusText(_licenseManager.State);
                
                // 生成QR码
                if (!string.IsNullOrEmpty(txtMachineCode.Text))
                {
                    pictureBoxQRCode.Image = GenerateQRCode(txtMachineCode.Text);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "初始化窗体数据失败");
                labelStatus.Text = "初始化失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 根据状态获取提示文本
        /// </summary>
        private string GetStatusText(LicenseManager.Status status)
        {
            switch (status)
            {
                case LicenseManager.Status.Success:
                    return "软件已成功注册";
                case LicenseManager.Status.UnRegistry:
                    return "软件未注册，请选择授权文件";
                case LicenseManager.Status.DiffHardware:
                    return "硬件不匹配，请重新获取授权";
                case LicenseManager.Status.RegistryExpire:
                    return "注册已过期，请重新获取授权";
                default:
                    return "未知状态，请重新获取授权";
            }
        }

        /// <summary>
        /// 生成二维码
        /// </summary>
        private Bitmap GenerateQRCode(string content)
        {
            try
            {
                QRCoder.QRCodeGenerator qrGenerator = new QRCoder.QRCodeGenerator();
                QRCoder.QRCodeData qrCodeData = qrGenerator.CreateQrCode(content, QRCoder.QRCodeGenerator.ECCLevel.Q);
                QRCoder.QRCode qrCode = new QRCoder.QRCode(qrCodeData);
                Bitmap qrCodeImage = qrCode.GetGraphic(5); // 5是像素大小
                return qrCodeImage;
            }
            catch (Exception ex)
            {
                _log.Error(ex, "生成二维码失败");
                return null;
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void LicenseRegisterForm_Load(object sender, EventArgs e)
        {
            if (_licenseManager.Licensed)
            {
                // 检查是否为试用版 - 只有启用评估模式才判断为试用版
                bool isTrial = Status.Evaluation_Lock_Enabled;
                
                if (isTrial)
                {
                    // 如果是评估模式
                    if (Status.Evaluation_Lock_Enabled)
                    {
                        if (Status.Evaluation_Type == EvaluationType.Trial_Days)
                        {
                            int remainingDays = Status.Evaluation_Time - Status.Evaluation_Time_Current;
                            labelStatus.Text = $"软件当前为试用版，剩余{remainingDays}天";
                        }
                        else // Runtime_Minutes
                        {
                            int remainingMinutes = Status.Evaluation_Time - Status.Evaluation_Time_Current;
                            labelStatus.Text = $"软件当前为试用版，剩余{remainingMinutes}分钟";
                        }
                    }
                    // 如果是时间限制模式
                    else if (Status.Expiration_Date_Lock_Enable)
                    {
                        int remainingDays = (Status.Expiration_Date - DateTime.Now).Days;
                        labelStatus.Text = $"软件当前为试用版，剩余{remainingDays}天";
                    }
                    
                    labelStatus.ForeColor = Color.Blue;
                }
                else
                {
                    if (Status.Expiration_Date_Lock_Enable)
                    {
                        labelStatus.Text = "软件已成功注册，到期日期：" + Status.Expiration_Date.ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        labelStatus.Text = "软件已成功注册";
                    }
                    labelStatus.ForeColor = Color.Green;
                }
            }
        }

        /// <summary>
        /// 复制硬件码按钮点击事件
        /// </summary>
        private void btnCopyHardwareID_Click(object sender, EventArgs e)
        {
            try
            {
                Clipboard.SetText(txtMachineCode.Text);
                toolTip.Show("已复制到剪贴板", btnCopyHardwareID, 2000);
            }
            catch (Exception ex)
            {
                _log.Error(ex, "复制硬件码失败");
                MessageBox.Show("复制失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 浏览按钮点击事件
        /// </summary>
        private void BtnImport_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "许可证文件 (*.license)|*.license|所有文件 (*.*)|*.*";
                openFileDialog.Title = "选择注册文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxFilePath.Text = openFileDialog.FileName;
                }
            }
        }

        /// <summary>
        /// 注册按钮点击事件
        /// </summary>
        private void BtnRegister_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(textBoxFilePath.Text) || !File.Exists(textBoxFilePath.Text))
            {
                MessageBox.Show("请先选择有效的授权文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                Cursor = Cursors.WaitCursor;

                // 首先检查评估模式状态
                string evalAnalysis = _licenseCheckService.AnalyzeEvaluationMode();
                _log.Info("评估模式分析结果:\n{0}", evalAnalysis);

                // 如果评估模式已过期，提供解决方案
                if (evalAnalysis.Contains("❌ 已过期"))
                {
                    var result = MessageBox.Show(
                        "检测到评估模式已过期，这会导致即使提供有效授权文件也无法通过验证。\n\n" +
                        "是否清除当前授权状态并重新注册？\n\n" +
                        "点击"是"清除授权状态，点击"否"查看详细分析，点击"取消"退出。",
                        "评估模式过期",
                        MessageBoxButtons.YesNoCancel,
                        MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // 清除授权状态
                        string confirmationCode = _licenseCheckService.InvalidateCurrentLicense();
                        if (!string.IsNullOrEmpty(confirmationCode))
                        {
                            MessageBox.Show(
                                $"授权状态已清除。\n\n确认码：{confirmationCode}\n\n请保存此确认码，然后重新选择授权文件进行注册。",
                                "授权状态已清除",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);

                            // 清除文件路径，让用户重新选择
                            textBoxFilePath.Text = "";
                            return;
                        }
                        else
                        {
                            MessageBox.Show("清除授权状态失败，请查看日志了解详细信息。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else if (result == DialogResult.No)
                    {
                        // 显示详细分析
                        var diagForm = new Form()
                        {
                            Text = "评估模式分析报告",
                            Size = new Size(600, 500),
                            StartPosition = FormStartPosition.CenterParent
                        };

                        var textBox = new TextBox()
                        {
                            Multiline = true,
                            ScrollBars = ScrollBars.Both,
                            Dock = DockStyle.Fill,
                            Text = evalAnalysis,
                            ReadOnly = true,
                            Font = new Font("Consolas", 9)
                        };

                        diagForm.Controls.Add(textBox);
                        diagForm.ShowDialog();
                        return;
                    }
                    else
                    {
                        return; // 取消
                    }
                }

                // 进行常规诊断
                string diagnosis = _licenseCheckService.DiagnoseLicenseFile(textBoxFilePath.Text);
                _log.Info("授权文件诊断结果:\n{0}", diagnosis);
                
                // 使用LicenseCheckService注册授权文件
                bool result = _licenseCheckService.RegisterWithLicenseFile(textBoxFilePath.Text);
                
                if (result)
                {
                    // 检查是否为试用版 - 只有启用评估模式才判断为试用版
                    bool isTrial = Status.Evaluation_Lock_Enabled;
                    
                    if (isTrial)
                    {
                        labelStatus.Text = "试用版注册成功";
                        labelStatus.ForeColor = Color.Blue;
                        
                        string message = "试用版注册成功！";
                        
                        // 显示试用信息
                        if (Status.Evaluation_Lock_Enabled)
                        {
                            if (Status.Evaluation_Type == EvaluationType.Trial_Days)
                            {
                                int days = Status.Evaluation_Time - Status.Evaluation_Time_Current;
                                message += $"\n试用期：{days}天";
                            }
                            else // Runtime_Minutes
                            {
                                int minutes = Status.Evaluation_Time - Status.Evaluation_Time_Current;
                                message += $"\n试用时间：{minutes}分钟";
                            }
                        }
                        else if (Status.Expiration_Date_Lock_Enable)
                        {
                            message += $"\n到期日期：{Status.Expiration_Date.ToString("yyyy-MM-dd")}";
                        }
                        
                        MessageBox.Show(message, "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        // 检查是否启用了日期限制
                        if (_licenseCheckService.IsDateLimitEnabled())
                        {
                            // 有日期限制，显示到期日期
                            labelStatus.Text = "注册成功，到期日期：" + _licenseCheckService.GetExpirationDate().ToString("yyyy-MM-dd");
                        }
                        else
                        {
                            // 无日期限制，永久有效
                            labelStatus.Text = "注册成功！";
                        }
                        
                        labelStatus.ForeColor = Color.Green;
                        
                        MessageBox.Show("注册成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    
                    // 设置对话框结果为OK，表示注册成功
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    labelStatus.Text = "授权文件无效";
                    MessageBox.Show("授权文件无效", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "注册失败");
                labelStatus.Text = "注册失败: " + ex.Message;
                MessageBox.Show("注册失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// 关于授权按钮点击事件
        /// </summary>
        private void BtnAbout_Click(object sender, EventArgs e)
        {
            try
            {
                using (var aboutForm = new AboutLicenseForm())
                {
                    aboutForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, "显示关于授权窗口失败");
                MessageBox.Show("显示关于授权窗口失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
} 