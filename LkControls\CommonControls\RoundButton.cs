﻿using System;
using System.Collections.Generic;
using System.Drawing.Drawing2D;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;
using System.Windows.Forms;
using System.Diagnostics;
using System.IO;
using Svg;

namespace LkControls.common
{
    public class RoundButton : Button
    {

        public bool isPressed = false;

        // 按钮状态颜色
        public Color NormalColor { get; set; } = Color.FromArgb(39, 39, 39);      // 默认颜色
        public Color HoverColor { get; set; } = Color.FromArgb(255, 136, 0);    // 鼠标悬停颜色
        public Color PressedColor { get; set; } = Color.FromArgb(255, 136, 0);   // 鼠标按下颜色
        private Color _currentBackColor; // 当前背景色

        public int BorderRadius { get; set; } = 4;  // 圆角半径，与SVG图标匹配
        public Color NormalBorderColor { get; set; } = Color.DimGray; // 边框颜色

        public Color HoverBorderColor { get; set; } = Color.FromArgb(255, 136, 0);

        public Color DisableColor { get; set; } = Color.FromArgb(138, 138, 138);      // 默认颜色

        public Color PressedBorderColor { get; set; } = Color.FromArgb(255, 136, 0);

        private Color _currentBorderColor;

        public int BorderSize { get; set; } = 1;  // 边框大小
        public Color ButtonTextColor { get; set; } = Color.White; // 文字颜色

        private bool _customEnabled;

        private bool _lostFocus;

        // 添加 SVG 图标相关属性
        public System.Drawing.Image IconImage { get; set; } = null;  // SVG 图标（作为 Image 对象）
        public int IconSize { get; set; } = 60;  // 图标大小
        public bool IconVisible { get; set; } = true;  // 是否显示图标
        public ContentAlignment IconAlignment { get; set; } = ContentAlignment.TopCenter;  // 图标对齐方式

        // 添加一个中英文映射字典
        private static readonly Dictionary<string, string> ChineseToEnglishMap = new Dictionary<string, string>
        {
            {"打开项目", "Open Project"}, 
            {"截图", "Screenshot"},
            {"平移", "Pan"},
            {"窗宽窗位", "Window Level"},
            {"导入图像", "Import Image"},
            {"图像导出", "Export Image"},
            {"缩放", "Zoom"},
            {"重置", "Reset"},
            {"顺时针旋转", "Rotate Clockwise"},
            {"逆时针旋转", "Rotate Counterclockwise"},
            {"上下翻转", "Flip Vertical"},
            {"左右翻转", "Flip Horizontal"},
            {"图像负像", "Negative Image"},
            {"图像增强", "Image Enhancement"},
            {"区域关注", "Region Focus"},
            {"定标", "Scaling"},
            {"直线测量", "Line Measurement"},
            {"线段测量", "Segment Measurement"},
            {"放大镜", "Magnifier"},
            {"椭圆形测量", "Ellipse Measurement"},
            {"角度测量", "Angle Measurement"},
            {"SNR", "NSNR"},
            {"像质计", "Duplex IQI"},
            {"点", "Point"},
            {"折线", "Polyline"},
            {"矩形", "Rectangle"},
            {"多行", "Multiline"},
            {"无限线", "Infinite Line"},
            {"箭头", "Arrow"},
            {"尺子", "Ruler"},
            {"圆", "Circle"},
            {"插值", "Interpolation"},
            {"椭圆", "Ellipse"},
            {"测量线", "Measuring Line"},
            {"切割线", "Cut Line"},
            {"基准线", "Baseline"},
            {"十字线", "Crosshair"},
            {"文字", "Text"},
            {"壁厚测量", "WallThickness"},
            {"普通增强", "enhanceBtn1"},
            {"高级增强", "enhanceBtn2"},
            {"超级增强", "enhanceBtn3"},
            {"普通降噪", "denoiseBtn1"},
            {"高级降噪", "denoiseBtn2"},
            {"超级降噪", "denoiseBtn3"},
            {"伪彩", "pseudoColorBtn"},
            {"浮雕", "rilievoBtn"}
        };

        public bool IsPressed {
            get {
                return isPressed;
            }
            set { 
                isPressed = value;

                if (value)
                {
                    _currentBackColor = HoverColor;
                    _currentBorderColor = HoverBorderColor;
                }
                else {
                    _currentBackColor = NormalColor;
                    _currentBorderColor = NormalBorderColor;
                }
                
                Invalidate();
            }
        }

        public bool CustomEnabled
        {
            get
            {
                // 在这里可以添加自定义逻辑
                return _customEnabled;
            }
            set
            {
                // 在设置 Enabled 属性时，可以执行一些额外的逻辑
                if (_customEnabled != value)
                {
                    _customEnabled = value;

                    _currentBackColor = NormalColor;
                    _currentBorderColor = NormalBorderColor;
                    
                    // 触发状态改变的事件或其他逻辑
                    OnEnabledChanged(EventArgs.Empty);
                }

                // 调用基类的 Enabled 属性设置原始的 Enable 状态
                base.Enabled = value;
            }
        }

        public RoundButton()
        {
            _currentBorderColor = NormalBorderColor;
            _currentBackColor = NormalColor;
            ForeColor = NormalColor;
            BackColor = NormalColor;
            
            // 修改为正方形尺寸 - 这是唯一需要改的地方
            Size = new Size(180, 180); // 正方形按钮
            
            Font = new System.Drawing.Font("Microsoft YaHei", 9, FontStyle.Regular);

            FlatStyle = FlatStyle.Flat;
            FlatAppearance.BorderSize = 0; // 取消默认边框
            TextAlign = ContentAlignment.BottomCenter; // 文本在底部，为图标留出空间

            // 监听鼠标事件
            MouseEnter += (s, e) =>
            {
                if (isPressed == true)
                {
                    _currentBackColor = PressedColor;
                    _currentBorderColor = PressedBorderColor;
                }
                else
                {
                    _currentBackColor = HoverColor;
                    _currentBorderColor = HoverColor;
                }

                //Debug.WriteLine("MouseEnter:" + Text + "," + _currentBackColor);

                Invalidate();
            };
            MouseLeave += (s, e) =>
            {
                if (isPressed == true)
                {
                    _currentBackColor = PressedColor;
                    _currentBorderColor = PressedBorderColor;
                }
                else
                {
                    _currentBackColor = NormalColor;
                    _currentBorderColor = NormalBorderColor;
                }

                //Debug.WriteLine("MouseLeave:" + Text + "," + _currentBackColor);

                Invalidate();
            };
            MouseDown += (s, e) =>
            {
                _currentBackColor = PressedColor;
                _currentBorderColor = PressedBorderColor;

                //Debug.WriteLine("MouseDown:" + Text + "," + _currentBackColor);

                Invalidate();
            };
            MouseUp += (s, e) =>
            {
                if (isPressed == true)
                {
                    _currentBackColor = PressedColor;
                    _currentBorderColor = PressedBorderColor;
                }
                else
                {
                    if (_lostFocus == true) {
                        //_currentBackColor = NormalColor;
                        //_currentBorderColor = NormalBorderColor;
                    }
                }

                Invalidate();
            };
        }

        protected override void OnLostFocus(EventArgs e)
        {
            base.OnLostFocus(e);

            _lostFocus = true;

            Invalidate();  // 触发重绘，恢复颜色
        }

        // 当按钮获得焦点时恢复背景颜色
        protected override void OnGotFocus(EventArgs e)
        {
            base.OnGotFocus(e);

            _lostFocus = false;

            Invalidate();  // 触发重绘，恢复颜色
        }

        protected override void OnCreateControl()
        {
            base.OnCreateControl();
            _currentBackColor = NormalColor;
            _currentBorderColor = NormalBorderColor;

            if (this.CustomEnabled == false)
            {
                _currentBackColor = DisableColor;
            }

        }

        public void setIsPressed(bool _isPressed)
        {
            isPressed = _isPressed;

            if (isPressed)
            {
                _currentBackColor = PressedColor;
                _currentBorderColor = PressedBorderColor;
            }
            else
            {
                _currentBackColor = NormalColor;
                _currentBorderColor = NormalBorderColor;
            }

            Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
            e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;

            // 创建圆角矩形路径
            GraphicsPath path = new GraphicsPath();
            Rectangle rect = new Rectangle(0, 0, Width - 2, Height - 2);
            int radius = BorderRadius * 2;

            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.Right - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.Right - radius, rect.Bottom - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Bottom - radius, radius, radius, 90, 90);
            path.CloseFigure();

            // 填充按钮背景
            using (SolidBrush brush = new SolidBrush(_currentBackColor))
            {
                e.Graphics.FillPath(brush, path);
            }

            // 绘制边框
            if (BorderSize > 0)
            {
                using (Pen pen = new Pen(_currentBorderColor, BorderSize))
                {
                    e.Graphics.DrawPath(pen, path);
                }
            }

            // 绘制图标（填充整个按钮区域）
            if (IconVisible && IconImage != null)
            {
                // 计算图标大小，使其占据按钮大部分区域但留出文本空间
                int textHeight = 0; // 文本区域高度
                int padding = 1; // 边距
                int iconSize = Math.Min(Width - padding * 2, Height - textHeight - padding);
                
                // 计算图标位置（顶部居中）
                int iconX = (Width - iconSize) / 2;
                int iconY = padding;
                
                Rectangle iconRect = new Rectangle(iconX, iconY, iconSize, iconSize);
                e.Graphics.DrawImage(IconImage, iconRect);
                
                // 绘制文本（紧贴图标底部）
                Rectangle textRect = new Rectangle(
                    0, 
                    iconRect.Bottom + 2, // 紧贴图标底部，只留2像素间距
                    Width, 
                    textHeight);
                
                // 使用更小的字体
                System.Drawing.Font smallerFont = new System.Drawing.Font(Font.FontFamily, 8.0f);
                TextFormatFlags flags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter | TextFormatFlags.WordBreak;
                TextRenderer.DrawText(e.Graphics, Text, smallerFont, textRect, ButtonTextColor, flags);
            }
            else
            {
                // 没有图标时显示文本（居中）
                TextFormatFlags flags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                TextRenderer.DrawText(e.Graphics, Text, Font, rect, ButtonTextColor, flags);
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            Invalidate(); // 重新绘制
        }

        // 修改SVG加载方法，生成更大尺寸的图像然后再缩小显示
        public void LoadSvgIcon(string iconName)
        {
            try
            {
                // 转换名称：如果是中文，查找对应的英文
                string englishName = iconName;
                if (ChineseToEnglishMap.ContainsKey(iconName))
                {
                    englishName = ChineseToEnglishMap[iconName];
                }
                
                // 使用固定相对路径查找
                // 使用应用程序目录中的Icons文件夹
                string iconDirectory = Path.Combine(System.Windows.Forms.Application.StartupPath, "Icons");
                string svgPath = Path.Combine(iconDirectory, englishName + ".svg");

                Debug.WriteLine($"尝试加载图标: {svgPath}");
                
                if (File.Exists(svgPath))
                {
                    // 加载SVG并渲染为更高分辨率的图像
                    var svgDocument = Svg.SvgDocument.Open(svgPath);
                    if (svgDocument != null)
                    {
                        int renderSize = IconSize * 4;
                        this.IconImage = svgDocument.Draw(renderSize, renderSize);
                        this.IconVisible = true;
                    }
                }
                else
                {
                    Debug.WriteLine($"图标文件不存在: {svgPath}，尝试查找的名称: {englishName}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载图标失败: {ex.Message}");
            }
        }

        // 根据按钮文本加载图标
        public void LoadSvgIconByText()
        {
            LoadSvgIcon(this.Text);
        }
    }
}
