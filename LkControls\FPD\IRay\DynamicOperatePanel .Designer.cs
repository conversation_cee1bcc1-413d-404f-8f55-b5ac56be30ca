﻿using LkControls.common;

namespace InduVision.LkControls.FPD.IRay
{
    partial class DynamicOperatePanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            namePanel = new RoundBorderPanel();
            nameTextBox = new System.Windows.Forms.TextBox();
            panel3 = new System.Windows.Forms.Panel();
            label3 = new System.Windows.Forms.Label();
            saveButton = new RoundButton();
            stopButton = new RoundButton();
            startButton = new RoundButton();
            disconnectButton = new RoundButton();
            flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            flowLayoutPanel2 = new System.Windows.Forms.FlowLayoutPanel();
            roundBorderPanel1 = new RoundBorderPanel();
            isOverlay = new System.Windows.Forms.CheckBox();
            panel1 = new System.Windows.Forms.Panel();
            label1 = new System.Windows.Forms.Label();
            roundBorderPanel2 = new RoundBorderPanel();
            overlayNumber = new System.Windows.Forms.NumericUpDown();
            panel2 = new System.Windows.Forms.Panel();
            label2 = new System.Windows.Forms.Label();
            roundBorderPanel3 = new RoundBorderPanel();
            onlyOverlay = new System.Windows.Forms.CheckBox();
            panel4 = new System.Windows.Forms.Panel();
            label4 = new System.Windows.Forms.Label();
            namePanel.SuspendLayout();
            flowLayoutPanel1.SuspendLayout();
            flowLayoutPanel2.SuspendLayout();
            roundBorderPanel1.SuspendLayout();
            roundBorderPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)overlayNumber).BeginInit();
            roundBorderPanel3.SuspendLayout();
            SuspendLayout();
            // 
            // namePanel
            // 
            namePanel.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            namePanel.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            namePanel.BorderWidth = 1;
            namePanel.Controls.Add(nameTextBox);
            namePanel.Controls.Add(panel3);
            namePanel.Controls.Add(label3);
            namePanel.CornerRadius = 6;
            namePanel.Location = new System.Drawing.Point(3, 11);
            namePanel.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            namePanel.Name = "namePanel";
            namePanel.Size = new System.Drawing.Size(293, 52);
            namePanel.TabIndex = 27;
            // 
            // nameTextBox
            // 
            nameTextBox.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            nameTextBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
            nameTextBox.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            nameTextBox.ForeColor = System.Drawing.Color.White;
            nameTextBox.Location = new System.Drawing.Point(105, 13);
            nameTextBox.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            nameTextBox.Name = "nameTextBox";
            nameTextBox.PlaceholderText = "请输入文件名";
            nameTextBox.Size = new System.Drawing.Size(170, 26);
            nameTextBox.TabIndex = 2;
            // 
            // panel3
            // 
            panel3.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel3.Location = new System.Drawing.Point(90, 0);
            panel3.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel3.Name = "panel3";
            panel3.Size = new System.Drawing.Size(1, 49);
            panel3.TabIndex = 1;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label3.ForeColor = System.Drawing.Color.White;
            label3.Location = new System.Drawing.Point(13, 16);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(54, 19);
            label3.TabIndex = 0;
            label3.Text = "文件名";
            // 
            // saveButton
            // 
            saveButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            saveButton.BorderRadius = 8;
            saveButton.BorderSize = 1;
            saveButton.ButtonTextColor = System.Drawing.Color.White;
            saveButton.CustomEnabled = true;
            saveButton.DisableColor = System.Drawing.Color.FromArgb(60, 60, 60);
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            saveButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            saveButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            saveButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            saveButton.IconImage = null;
            saveButton.IconSize = 60;
            saveButton.IconVisible = true;
            saveButton.IsPressed = false;
            saveButton.Location = new System.Drawing.Point(5, 72);
            saveButton.Margin = new System.Windows.Forms.Padding(5, 11, 5, 4);
            saveButton.Name = "saveButton";
            saveButton.NormalBorderColor = System.Drawing.Color.DimGray;
            saveButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            saveButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            saveButton.Size = new System.Drawing.Size(135, 46);
            saveButton.TabIndex = 35;
            saveButton.Text = "保存图像";
            saveButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            saveButton.UseVisualStyleBackColor = false;
            saveButton.Click += saveButton_Click;
            // 
            // stopButton
            // 
            stopButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            stopButton.BorderRadius = 8;
            stopButton.BorderSize = 1;
            stopButton.ButtonTextColor = System.Drawing.Color.White;
            stopButton.CustomEnabled = true;
            stopButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            stopButton.FlatAppearance.BorderSize = 0;
            stopButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            stopButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            stopButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            stopButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            stopButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            stopButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            stopButton.IconImage = null;
            stopButton.IconSize = 60;
            stopButton.IconVisible = true;
            stopButton.IsPressed = false;
            stopButton.Location = new System.Drawing.Point(150, 11);
            stopButton.Margin = new System.Windows.Forms.Padding(5, 11, 5, 4);
            stopButton.Name = "stopButton";
            stopButton.NormalBorderColor = System.Drawing.Color.DimGray;
            stopButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            stopButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            stopButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            stopButton.Size = new System.Drawing.Size(135, 46);
            stopButton.TabIndex = 34;
            stopButton.Text = "停止采集";
            stopButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            stopButton.UseVisualStyleBackColor = false;
            stopButton.Click += stopButton_Click;
            // 
            // startButton
            // 
            startButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            startButton.BorderRadius = 8;
            startButton.BorderSize = 1;
            startButton.ButtonTextColor = System.Drawing.Color.White;
            startButton.CustomEnabled = true;
            startButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            startButton.FlatAppearance.BorderSize = 0;
            startButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            startButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            startButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            startButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            startButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            startButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            startButton.IconImage = null;
            startButton.IconSize = 60;
            startButton.IconVisible = true;
            startButton.IsPressed = false;
            startButton.Location = new System.Drawing.Point(5, 11);
            startButton.Margin = new System.Windows.Forms.Padding(5, 11, 5, 4);
            startButton.Name = "startButton";
            startButton.NormalBorderColor = System.Drawing.Color.DimGray;
            startButton.NormalColor = System.Drawing.Color.FromArgb(39, 39, 39);
            startButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            startButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            startButton.Size = new System.Drawing.Size(135, 46);
            startButton.TabIndex = 33;
            startButton.Text = "开始采集";
            startButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            startButton.UseVisualStyleBackColor = false;
            startButton.Click += startButton_Click;
            // 
            // disconnectButton
            // 
            disconnectButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            disconnectButton.BorderRadius = 8;
            disconnectButton.BorderSize = 1;
            disconnectButton.ButtonTextColor = System.Drawing.Color.White;
            disconnectButton.CustomEnabled = true;
            disconnectButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            disconnectButton.FlatAppearance.BorderSize = 0;
            disconnectButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            disconnectButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            disconnectButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            disconnectButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            disconnectButton.IconImage = null;
            disconnectButton.IconSize = 60;
            disconnectButton.IconVisible = true;
            disconnectButton.IsPressed = false;
            disconnectButton.Location = new System.Drawing.Point(3, 432);
            disconnectButton.Margin = new System.Windows.Forms.Padding(3, 20, 3, 4);
            disconnectButton.Name = "disconnectButton";
            disconnectButton.NormalBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.NormalColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            disconnectButton.Size = new System.Drawing.Size(293, 60);
            disconnectButton.TabIndex = 34;
            disconnectButton.Text = "断开连接";
            disconnectButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            disconnectButton.UseVisualStyleBackColor = false;
            disconnectButton.Click += disconnectButton_Click;
            // 
            // flowLayoutPanel1
            // 
            flowLayoutPanel1.Controls.Add(startButton);
            flowLayoutPanel1.Controls.Add(stopButton);
            flowLayoutPanel1.Controls.Add(saveButton);
            flowLayoutPanel1.Location = new System.Drawing.Point(3, 279);
            flowLayoutPanel1.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            flowLayoutPanel1.Name = "flowLayoutPanel1";
            flowLayoutPanel1.Size = new System.Drawing.Size(297, 129);
            flowLayoutPanel1.TabIndex = 35;
            // 
            // flowLayoutPanel2
            // 
            flowLayoutPanel2.Controls.Add(namePanel);
            flowLayoutPanel2.Controls.Add(roundBorderPanel1);
            flowLayoutPanel2.Controls.Add(roundBorderPanel3);
            flowLayoutPanel2.Controls.Add(roundBorderPanel2);
            flowLayoutPanel2.Controls.Add(flowLayoutPanel1);
            flowLayoutPanel2.Controls.Add(disconnectButton);
            flowLayoutPanel2.Location = new System.Drawing.Point(3, 0);
            flowLayoutPanel2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            flowLayoutPanel2.Name = "flowLayoutPanel2";
            flowLayoutPanel2.Size = new System.Drawing.Size(320, 431);
            flowLayoutPanel2.TabIndex = 36;
            // 
            // roundBorderPanel1
            // 
            roundBorderPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel1.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel1.BorderWidth = 1;
            roundBorderPanel1.Controls.Add(isOverlay);
            roundBorderPanel1.Controls.Add(panel1);
            roundBorderPanel1.Controls.Add(label1);
            roundBorderPanel1.CornerRadius = 6;
            roundBorderPanel1.Location = new System.Drawing.Point(3, 78);
            roundBorderPanel1.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel1.Name = "roundBorderPanel1";
            roundBorderPanel1.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel1.TabIndex = 37;
            // 
            // isOverlay
            // 
            isOverlay.AutoSize = true;
            isOverlay.ForeColor = System.Drawing.Color.White;
            isOverlay.Location = new System.Drawing.Point(105, 14);
            isOverlay.Name = "isOverlay";
            isOverlay.Size = new System.Drawing.Size(61, 24);
            isOverlay.TabIndex = 2;
            isOverlay.Text = "启用";
            isOverlay.UseVisualStyleBackColor = true;
            // 
            // panel1
            // 
            panel1.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel1.Location = new System.Drawing.Point(90, 0);
            panel1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel1.Name = "panel1";
            panel1.Size = new System.Drawing.Size(1, 49);
            panel1.TabIndex = 1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label1.ForeColor = System.Drawing.Color.White;
            label1.Location = new System.Drawing.Point(13, 16);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(69, 19);
            label1.TabIndex = 0;
            label1.Text = "多帧叠加";
            // 
            // roundBorderPanel2
            // 
            roundBorderPanel2.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel2.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel2.BorderWidth = 1;
            roundBorderPanel2.Controls.Add(overlayNumber);
            roundBorderPanel2.Controls.Add(panel2);
            roundBorderPanel2.Controls.Add(label2);
            roundBorderPanel2.CornerRadius = 6;
            roundBorderPanel2.Location = new System.Drawing.Point(3, 212);
            roundBorderPanel2.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel2.Name = "roundBorderPanel2";
            roundBorderPanel2.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel2.TabIndex = 38;
            // 
            // overlayNumber
            // 
            overlayNumber.Location = new System.Drawing.Point(105, 13);
            overlayNumber.Name = "overlayNumber";
            overlayNumber.Size = new System.Drawing.Size(170, 27);
            overlayNumber.TabIndex = 2;
            // 
            // panel2
            // 
            panel2.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel2.Location = new System.Drawing.Point(90, 0);
            panel2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel2.Name = "panel2";
            panel2.Size = new System.Drawing.Size(1, 49);
            panel2.TabIndex = 1;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label2.ForeColor = System.Drawing.Color.White;
            label2.Location = new System.Drawing.Point(13, 16);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(69, 19);
            label2.TabIndex = 0;
            label2.Text = "叠加数量";
            // 
            // roundBorderPanel3
            // 
            roundBorderPanel3.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel3.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel3.BorderWidth = 1;
            roundBorderPanel3.Controls.Add(onlyOverlay);
            roundBorderPanel3.Controls.Add(panel4);
            roundBorderPanel3.Controls.Add(label4);
            roundBorderPanel3.CornerRadius = 6;
            roundBorderPanel3.Location = new System.Drawing.Point(3, 145);
            roundBorderPanel3.Margin = new System.Windows.Forms.Padding(3, 11, 3, 4);
            roundBorderPanel3.Name = "roundBorderPanel3";
            roundBorderPanel3.Size = new System.Drawing.Size(293, 52);
            roundBorderPanel3.TabIndex = 38;
            // 
            // onlyOverlay
            // 
            onlyOverlay.AutoSize = true;
            onlyOverlay.ForeColor = System.Drawing.Color.White;
            onlyOverlay.Location = new System.Drawing.Point(105, 14);
            onlyOverlay.Name = "onlyOverlay";
            onlyOverlay.Size = new System.Drawing.Size(106, 24);
            onlyOverlay.TabIndex = 2;
            onlyOverlay.Text = "仅显示合成";
            onlyOverlay.UseVisualStyleBackColor = true;
            // 
            // panel4
            // 
            panel4.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel4.Location = new System.Drawing.Point(90, 0);
            panel4.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            panel4.Name = "panel4";
            panel4.Size = new System.Drawing.Size(1, 49);
            panel4.TabIndex = 1;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label4.ForeColor = System.Drawing.Color.White;
            label4.Location = new System.Drawing.Point(13, 16);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(69, 19);
            label4.TabIndex = 0;
            label4.Text = "显示方式";
            // 
            // DynamicOperatePanel
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            Controls.Add(flowLayoutPanel2);
            Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            Name = "DynamicOperatePanel";
            Size = new System.Drawing.Size(324, 459);
            namePanel.ResumeLayout(false);
            namePanel.PerformLayout();
            flowLayoutPanel1.ResumeLayout(false);
            flowLayoutPanel2.ResumeLayout(false);
            roundBorderPanel1.ResumeLayout(false);
            roundBorderPanel1.PerformLayout();
            roundBorderPanel2.ResumeLayout(false);
            roundBorderPanel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)overlayNumber).EndInit();
            roundBorderPanel3.ResumeLayout(false);
            roundBorderPanel3.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private RoundBorderPanel namePanel;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Label label3;
        private RoundButton stopButton;
        private RoundButton startButton;
        private RoundButton disconnectButton;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        public RoundButton saveButton;
        public System.Windows.Forms.TextBox nameTextBox;
        private RoundBorderPanel roundBorderPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private RoundBorderPanel roundBorderPanel2;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label label2;
        public System.Windows.Forms.CheckBox isOverlay;
        public System.Windows.Forms.NumericUpDown overlayNumber;
        private RoundBorderPanel roundBorderPanel3;
        public System.Windows.Forms.CheckBox onlyOverlay;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Label label4;
    }
}
