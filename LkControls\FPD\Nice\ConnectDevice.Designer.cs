﻿using LkControls.common;

namespace InduVision.LkControls.FPD.Nice
{
    partial class ConnectDevice
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            countPanel = new RoundBorderPanel();
            pcIpText = new System.Windows.Forms.TextBox();
            panel3 = new System.Windows.Forms.Panel();
            label3 = new System.Windows.Forms.Label();
            roundBorderPanel1 = new RoundBorderPanel();
            pcPortText = new System.Windows.Forms.TextBox();
            panel1 = new System.Windows.Forms.Panel();
            label1 = new System.Windows.Forms.Label();
            roundBorderPanel2 = new RoundBorderPanel();
            FPDIpText = new System.Windows.Forms.TextBox();
            panel2 = new System.Windows.Forms.Panel();
            label2 = new System.Windows.Forms.Label();
            roundBorderPanel3 = new RoundBorderPanel();
            FPDPortText = new System.Windows.Forms.TextBox();
            panel4 = new System.Windows.Forms.Panel();
            label5 = new System.Windows.Forms.Label();
            connectButton = new RoundButton();
            roundBorderPanel4 = new RoundBorderPanel();
            deviceComboBox = new System.Windows.Forms.ComboBox();
            panel5 = new System.Windows.Forms.Panel();
            label6 = new System.Windows.Forms.Label();
            captureModePanel = new RoundBorderPanel();
            captureComboBox = new System.Windows.Forms.ComboBox();
            panel6 = new System.Windows.Forms.Panel();
            label4 = new System.Windows.Forms.Label();
            flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            countPanel.SuspendLayout();
            roundBorderPanel1.SuspendLayout();
            roundBorderPanel2.SuspendLayout();
            roundBorderPanel3.SuspendLayout();
            roundBorderPanel4.SuspendLayout();
            captureModePanel.SuspendLayout();
            flowLayoutPanel1.SuspendLayout();
            SuspendLayout();
            // 
            // countPanel
            // 
            countPanel.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            countPanel.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            countPanel.BorderWidth = 1;
            countPanel.Controls.Add(pcIpText);
            countPanel.Controls.Add(panel3);
            countPanel.Controls.Add(label3);
            countPanel.CornerRadius = 6;
            countPanel.Location = new System.Drawing.Point(3, 124);
            countPanel.Margin = new System.Windows.Forms.Padding(3, 10, 3, 3);
            countPanel.Name = "countPanel";
            countPanel.Size = new System.Drawing.Size(284, 52);
            countPanel.TabIndex = 26;
            // 
            // pcIpText
            // 
            pcIpText.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            pcIpText.BorderStyle = System.Windows.Forms.BorderStyle.None;
            pcIpText.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            pcIpText.ForeColor = System.Drawing.Color.White;
            pcIpText.Location = new System.Drawing.Point(106, 13);
            pcIpText.Name = "pcIpText";
            pcIpText.Size = new System.Drawing.Size(149, 26);
            pcIpText.TabIndex = 2;
            pcIpText.Text = "***********";
            // 
            // panel3
            // 
            panel3.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel3.Location = new System.Drawing.Point(90, 0);
            panel3.Name = "panel3";
            panel3.Size = new System.Drawing.Size(1, 49);
            panel3.TabIndex = 1;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label3.ForeColor = System.Drawing.Color.White;
            label3.Location = new System.Drawing.Point(12, 17);
            label3.Name = "label3";
            label3.Size = new System.Drawing.Size(54, 19);
            label3.TabIndex = 0;
            label3.Text = "本机IP";
            // 
            // roundBorderPanel1
            // 
            roundBorderPanel1.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel1.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel1.BorderWidth = 1;
            roundBorderPanel1.Controls.Add(pcPortText);
            roundBorderPanel1.Controls.Add(panel1);
            roundBorderPanel1.Controls.Add(label1);
            roundBorderPanel1.CornerRadius = 6;
            roundBorderPanel1.Location = new System.Drawing.Point(3, 189);
            roundBorderPanel1.Margin = new System.Windows.Forms.Padding(3, 10, 3, 3);
            roundBorderPanel1.Name = "roundBorderPanel1";
            roundBorderPanel1.Size = new System.Drawing.Size(284, 52);
            roundBorderPanel1.TabIndex = 27;
            // 
            // pcPortText
            // 
            pcPortText.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            pcPortText.BorderStyle = System.Windows.Forms.BorderStyle.None;
            pcPortText.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            pcPortText.ForeColor = System.Drawing.Color.White;
            pcPortText.Location = new System.Drawing.Point(106, 12);
            pcPortText.Name = "pcPortText";
            pcPortText.Size = new System.Drawing.Size(149, 26);
            pcPortText.TabIndex = 2;
            pcPortText.Text = "28000";
            // 
            // panel1
            // 
            panel1.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel1.Location = new System.Drawing.Point(90, 0);
            panel1.Name = "panel1";
            panel1.Size = new System.Drawing.Size(1, 49);
            panel1.TabIndex = 1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label1.ForeColor = System.Drawing.Color.White;
            label1.Location = new System.Drawing.Point(12, 17);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(69, 19);
            label1.TabIndex = 0;
            label1.Text = "本机端口";
            // 
            // roundBorderPanel2
            // 
            roundBorderPanel2.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel2.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel2.BorderWidth = 1;
            roundBorderPanel2.Controls.Add(FPDIpText);
            roundBorderPanel2.Controls.Add(panel2);
            roundBorderPanel2.Controls.Add(label2);
            roundBorderPanel2.CornerRadius = 6;
            roundBorderPanel2.Location = new System.Drawing.Point(3, 254);
            roundBorderPanel2.Margin = new System.Windows.Forms.Padding(3, 10, 3, 3);
            roundBorderPanel2.Name = "roundBorderPanel2";
            roundBorderPanel2.Size = new System.Drawing.Size(284, 52);
            roundBorderPanel2.TabIndex = 28;
            // 
            // FPDIpText
            // 
            FPDIpText.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            FPDIpText.BorderStyle = System.Windows.Forms.BorderStyle.None;
            FPDIpText.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            FPDIpText.ForeColor = System.Drawing.Color.White;
            FPDIpText.Location = new System.Drawing.Point(106, 14);
            FPDIpText.Name = "FPDIpText";
            FPDIpText.Size = new System.Drawing.Size(149, 26);
            FPDIpText.TabIndex = 2;
            FPDIpText.Text = "***********";
            // 
            // panel2
            // 
            panel2.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel2.Location = new System.Drawing.Point(90, 0);
            panel2.Name = "panel2";
            panel2.Size = new System.Drawing.Size(1, 49);
            panel2.TabIndex = 1;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label2.ForeColor = System.Drawing.Color.White;
            label2.Location = new System.Drawing.Point(12, 18);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(54, 19);
            label2.TabIndex = 0;
            label2.Text = "平板IP";
            // 
            // roundBorderPanel3
            // 
            roundBorderPanel3.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            roundBorderPanel3.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel3.BorderWidth = 1;
            roundBorderPanel3.Controls.Add(FPDPortText);
            roundBorderPanel3.Controls.Add(panel4);
            roundBorderPanel3.Controls.Add(label5);
            roundBorderPanel3.CornerRadius = 6;
            roundBorderPanel3.Location = new System.Drawing.Point(3, 319);
            roundBorderPanel3.Margin = new System.Windows.Forms.Padding(3, 10, 3, 3);
            roundBorderPanel3.Name = "roundBorderPanel3";
            roundBorderPanel3.Size = new System.Drawing.Size(284, 52);
            roundBorderPanel3.TabIndex = 28;
            // 
            // FPDPortText
            // 
            FPDPortText.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            FPDPortText.BorderStyle = System.Windows.Forms.BorderStyle.None;
            FPDPortText.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            FPDPortText.ForeColor = System.Drawing.Color.White;
            FPDPortText.Location = new System.Drawing.Point(106, 13);
            FPDPortText.Name = "FPDPortText";
            FPDPortText.Size = new System.Drawing.Size(149, 26);
            FPDPortText.TabIndex = 2;
            FPDPortText.Text = "27999";
            // 
            // panel4
            // 
            panel4.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel4.Location = new System.Drawing.Point(90, 0);
            panel4.Name = "panel4";
            panel4.Size = new System.Drawing.Size(1, 49);
            panel4.TabIndex = 1;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label5.ForeColor = System.Drawing.Color.White;
            label5.Location = new System.Drawing.Point(12, 17);
            label5.Name = "label5";
            label5.Size = new System.Drawing.Size(69, 19);
            label5.TabIndex = 0;
            label5.Text = "平板端口";
            // 
            // connectButton
            // 
            connectButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            connectButton.BorderRadius = 8;
            connectButton.BorderSize = 1;
            connectButton.ButtonTextColor = System.Drawing.Color.White;
            connectButton.CustomEnabled = true;
            connectButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            connectButton.FlatAppearance.BorderSize = 0;
            connectButton.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            connectButton.Font = new System.Drawing.Font("微软雅黑", 9F);
            connectButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            connectButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            connectButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            connectButton.IconAlignment = System.Drawing.ContentAlignment.TopCenter;
            connectButton.IconImage = null;
            connectButton.IconSize = 60;
            connectButton.IconVisible = true;
            connectButton.IsPressed = false;
            connectButton.Location = new System.Drawing.Point(3, 394);
            connectButton.Margin = new System.Windows.Forms.Padding(3, 20, 3, 3);
            connectButton.Name = "connectButton";
            connectButton.NormalBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            connectButton.NormalColor = System.Drawing.Color.FromArgb(255, 136, 0);
            connectButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            connectButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            connectButton.Size = new System.Drawing.Size(284, 60);
            connectButton.TabIndex = 29;
            connectButton.Text = "连接设备";
            connectButton.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            connectButton.UseVisualStyleBackColor = false;
            connectButton.Click += connectButton_Click;
            // 
            // roundBorderPanel4
            // 
            roundBorderPanel4.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            roundBorderPanel4.BorderWidth = 1;
            roundBorderPanel4.Controls.Add(deviceComboBox);
            roundBorderPanel4.Controls.Add(panel5);
            roundBorderPanel4.Controls.Add(label6);
            roundBorderPanel4.CornerRadius = 6;
            roundBorderPanel4.Location = new System.Drawing.Point(3, 3);
            roundBorderPanel4.Name = "roundBorderPanel4";
            roundBorderPanel4.Size = new System.Drawing.Size(284, 51);
            roundBorderPanel4.TabIndex = 30;
            // 
            // deviceComboBox
            // 
            deviceComboBox.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            deviceComboBox.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            deviceComboBox.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            deviceComboBox.ForeColor = System.Drawing.Color.White;
            deviceComboBox.FormattingEnabled = true;
            deviceComboBox.Items.AddRange(new object[] { "静态板", "动态板" });
            deviceComboBox.Location = new System.Drawing.Point(106, 9);
            deviceComboBox.Name = "deviceComboBox";
            deviceComboBox.Size = new System.Drawing.Size(164, 35);
            deviceComboBox.TabIndex = 3;
            deviceComboBox.SelectedIndexChanged += deviceComboBox_SelectedIndexChanged;
            deviceComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            // 
            // panel5
            // 
            panel5.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel5.Location = new System.Drawing.Point(90, 2);
            panel5.Name = "panel5";
            panel5.Size = new System.Drawing.Size(1, 49);
            panel5.TabIndex = 2;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label6.ForeColor = System.Drawing.Color.White;
            label6.Location = new System.Drawing.Point(12, 18);
            label6.Name = "label6";
            label6.Size = new System.Drawing.Size(69, 19);
            label6.TabIndex = 1;
            label6.Text = "设备选择";
            // 
            // captureModePanel
            // 
            captureModePanel.BorderColor = System.Drawing.Color.FromArgb(122, 122, 122);
            captureModePanel.BorderWidth = 1;
            captureModePanel.Controls.Add(captureComboBox);
            captureModePanel.Controls.Add(panel6);
            captureModePanel.Controls.Add(label4);
            captureModePanel.CornerRadius = 6;
            captureModePanel.Location = new System.Drawing.Point(3, 60);
            captureModePanel.Name = "captureModePanel";
            captureModePanel.Size = new System.Drawing.Size(284, 51);
            captureModePanel.TabIndex = 31;
            captureModePanel.Visible = false;
            // 
            // captureComboBox
            // 
            captureComboBox.BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            captureComboBox.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            captureComboBox.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F);
            captureComboBox.ForeColor = System.Drawing.Color.White;
            captureComboBox.FormattingEnabled = true;
            captureComboBox.Items.AddRange(new object[] { "手动", "半自动" });
            captureComboBox.Location = new System.Drawing.Point(106, 9);
            captureComboBox.Name = "captureComboBox";
            captureComboBox.Size = new System.Drawing.Size(165, 35);
            captureComboBox.TabIndex = 3;
            captureComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            // 
            // panel6
            // 
            panel6.BackColor = System.Drawing.Color.FromArgb(122, 122, 122);
            panel6.Location = new System.Drawing.Point(90, 2);
            panel6.Name = "panel6";
            panel6.Size = new System.Drawing.Size(1, 49);
            panel6.TabIndex = 2;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F, System.Drawing.FontStyle.Bold);
            label4.ForeColor = System.Drawing.Color.White;
            label4.Location = new System.Drawing.Point(12, 18);
            label4.Name = "label4";
            label4.Size = new System.Drawing.Size(69, 19);
            label4.TabIndex = 1;
            label4.Text = "采集模式";
            // 
            // flowLayoutPanel1
            // 
            flowLayoutPanel1.Controls.Add(roundBorderPanel4);
            flowLayoutPanel1.Controls.Add(captureModePanel);
            flowLayoutPanel1.Controls.Add(countPanel);
            flowLayoutPanel1.Controls.Add(roundBorderPanel1);
            flowLayoutPanel1.Controls.Add(roundBorderPanel2);
            flowLayoutPanel1.Controls.Add(roundBorderPanel3);
            flowLayoutPanel1.Controls.Add(connectButton);
            flowLayoutPanel1.Location = new System.Drawing.Point(13, 15);
            flowLayoutPanel1.Name = "flowLayoutPanel1";
            flowLayoutPanel1.Size = new System.Drawing.Size(315, 505);
            flowLayoutPanel1.TabIndex = 32;
            // 
            // ConnectDevice
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(9F, 20F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            BackColor = System.Drawing.Color.FromArgb(32, 32, 32);
            Controls.Add(flowLayoutPanel1);
            Name = "ConnectDevice";
            Size = new System.Drawing.Size(336, 538);
            countPanel.ResumeLayout(false);
            countPanel.PerformLayout();
            roundBorderPanel1.ResumeLayout(false);
            roundBorderPanel1.PerformLayout();
            roundBorderPanel2.ResumeLayout(false);
            roundBorderPanel2.PerformLayout();
            roundBorderPanel3.ResumeLayout(false);
            roundBorderPanel3.PerformLayout();
            roundBorderPanel4.ResumeLayout(false);
            roundBorderPanel4.PerformLayout();
            captureModePanel.ResumeLayout(false);
            captureModePanel.PerformLayout();
            flowLayoutPanel1.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        private RoundBorderPanel countPanel;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Label label3;
        private RoundBorderPanel roundBorderPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private RoundBorderPanel roundBorderPanel2;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label label2;
        private RoundBorderPanel roundBorderPanel3;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Label label5;
        private RoundButton connectButton;
        private RoundBorderPanel roundBorderPanel4;
        private System.Windows.Forms.ComboBox deviceComboBox;
        private System.Windows.Forms.Panel panel5;
        private System.Windows.Forms.Label label6;
        private RoundBorderPanel captureModePanel;
        private System.Windows.Forms.ComboBox captureComboBox;
        private System.Windows.Forms.Panel panel6;
        private System.Windows.Forms.Label label4;
        public System.Windows.Forms.TextBox pcIpText;
        public System.Windows.Forms.TextBox pcPortText;
        public System.Windows.Forms.TextBox FPDIpText;
        public System.Windows.Forms.TextBox FPDPortText;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
    }
}
