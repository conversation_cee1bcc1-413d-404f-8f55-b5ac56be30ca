using LkControls.common;

namespace LkControls.SetPanel
{
    partial class MagnifierToolSettingsPanel
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MagnifierToolSettingsPanel));
            label1 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            widthNumericUpDown = new System.Windows.Forms.NumericUpDown();
            heightNumericUpDown = new System.Windows.Forms.NumericUpDown();
            zoomNumericUpDown = new System.Windows.Forms.NumericUpDown();
            borderWidthNumericUpDown = new System.Windows.Forms.NumericUpDown();
            borderColorPanelControl = new DemosCommonCode.CustomControls.ColorPanelControl();
            label4 = new System.Windows.Forms.Label();
            label3 = new System.Windows.Forms.Label();
            ellipticalOutlineCheckBox = new System.Windows.Forms.CheckBox();
            invertCheckBox = new System.Windows.Forms.CheckBox();
            grayscaleCheckBox = new System.Windows.Forms.CheckBox();
            posterizeCheckBox = new System.Windows.Forms.CheckBox();
            oilPaintingCheckBox = new System.Windows.Forms.CheckBox();
            label5 = new System.Windows.Forms.Label();
            label6 = new System.Windows.Forms.Label();
            label7 = new System.Windows.Forms.Label();
            label8 = new System.Windows.Forms.Label();
            groupBox3 = new System.Windows.Forms.GroupBox();
            groupBox4 = new System.Windows.Forms.GroupBox();
            applyButton = new RoundButton();
            groupBox1 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)widthNumericUpDown).BeginInit();
            ((System.ComponentModel.ISupportInitialize)heightNumericUpDown).BeginInit();
            ((System.ComponentModel.ISupportInitialize)zoomNumericUpDown).BeginInit();
            ((System.ComponentModel.ISupportInitialize)borderWidthNumericUpDown).BeginInit();
            groupBox3.SuspendLayout();
            groupBox4.SuspendLayout();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // label1
            // 
            resources.ApplyResources(label1, "label1");
            label1.ForeColor = System.Drawing.Color.White;
            label1.Name = "label1";
            // 
            // label2
            // 
            resources.ApplyResources(label2, "label2");
            label2.ForeColor = System.Drawing.Color.White;
            label2.Name = "label2";
            // 
            // widthNumericUpDown
            // 
            resources.ApplyResources(widthNumericUpDown, "widthNumericUpDown");
            widthNumericUpDown.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            widthNumericUpDown.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            widthNumericUpDown.Name = "widthNumericUpDown";
            widthNumericUpDown.Value = new decimal(new int[] { 1, 0, 0, 0 });
            // 
            // heightNumericUpDown
            // 
            resources.ApplyResources(heightNumericUpDown, "heightNumericUpDown");
            heightNumericUpDown.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            heightNumericUpDown.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            heightNumericUpDown.Name = "heightNumericUpDown";
            heightNumericUpDown.Value = new decimal(new int[] { 1, 0, 0, 0 });
            // 
            // zoomNumericUpDown
            // 
            resources.ApplyResources(zoomNumericUpDown, "zoomNumericUpDown");
            zoomNumericUpDown.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            zoomNumericUpDown.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            zoomNumericUpDown.Name = "zoomNumericUpDown";
            zoomNumericUpDown.Value = new decimal(new int[] { 100, 0, 0, 0 });
            // 
            // borderWidthNumericUpDown
            // 
            resources.ApplyResources(borderWidthNumericUpDown, "borderWidthNumericUpDown");
            borderWidthNumericUpDown.Name = "borderWidthNumericUpDown";
            // 
            // borderColorPanelControl
            // 
            resources.ApplyResources(borderColorPanelControl, "borderColorPanelControl");
            borderColorPanelControl.Color = System.Drawing.Color.Transparent;
            borderColorPanelControl.DefaultColor = System.Drawing.Color.Empty;
            borderColorPanelControl.Name = "borderColorPanelControl";
            // 
            // label4
            // 
            resources.ApplyResources(label4, "label4");
            label4.Name = "label4";
            // 
            // label3
            // 
            resources.ApplyResources(label3, "label3");
            label3.Name = "label3";
            // 
            // ellipticalOutlineCheckBox
            // 
            resources.ApplyResources(ellipticalOutlineCheckBox, "ellipticalOutlineCheckBox");
            ellipticalOutlineCheckBox.ForeColor = System.Drawing.Color.White;
            ellipticalOutlineCheckBox.Name = "ellipticalOutlineCheckBox";
            ellipticalOutlineCheckBox.UseVisualStyleBackColor = true;
            // 
            // invertCheckBox
            // 
            resources.ApplyResources(invertCheckBox, "invertCheckBox");
            invertCheckBox.Name = "invertCheckBox";
            invertCheckBox.UseVisualStyleBackColor = true;
            // 
            // grayscaleCheckBox
            // 
            resources.ApplyResources(grayscaleCheckBox, "grayscaleCheckBox");
            grayscaleCheckBox.Name = "grayscaleCheckBox";
            grayscaleCheckBox.UseVisualStyleBackColor = true;
            // 
            // posterizeCheckBox
            // 
            resources.ApplyResources(posterizeCheckBox, "posterizeCheckBox");
            posterizeCheckBox.Name = "posterizeCheckBox";
            posterizeCheckBox.UseVisualStyleBackColor = true;
            // 
            // oilPaintingCheckBox
            // 
            resources.ApplyResources(oilPaintingCheckBox, "oilPaintingCheckBox");
            oilPaintingCheckBox.Name = "oilPaintingCheckBox";
            oilPaintingCheckBox.UseVisualStyleBackColor = true;
            // 
            // label5
            // 
            resources.ApplyResources(label5, "label5");
            label5.ForeColor = System.Drawing.Color.White;
            label5.Name = "label5";
            // 
            // label6
            // 
            resources.ApplyResources(label6, "label6");
            label6.ForeColor = System.Drawing.Color.White;
            label6.Name = "label6";
            // 
            // label7
            // 
            resources.ApplyResources(label7, "label7");
            label7.ForeColor = System.Drawing.Color.White;
            label7.Name = "label7";
            // 
            // label8
            // 
            resources.ApplyResources(label8, "label8");
            label8.ForeColor = System.Drawing.Color.White;
            label8.Name = "label8";
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(borderWidthNumericUpDown);
            groupBox3.Controls.Add(label3);
            groupBox3.Controls.Add(label4);
            groupBox3.Controls.Add(borderColorPanelControl);
            groupBox3.ForeColor = System.Drawing.Color.White;
            resources.ApplyResources(groupBox3, "groupBox3");
            groupBox3.Name = "groupBox3";
            groupBox3.TabStop = false;
            // 
            // groupBox4
            // 
            groupBox4.Controls.Add(grayscaleCheckBox);
            groupBox4.Controls.Add(invertCheckBox);
            groupBox4.Controls.Add(ellipticalOutlineCheckBox);
            groupBox4.Controls.Add(posterizeCheckBox);
            groupBox4.Controls.Add(oilPaintingCheckBox);
            groupBox4.ForeColor = System.Drawing.Color.White;
            resources.ApplyResources(groupBox4, "groupBox4");
            groupBox4.Name = "groupBox4";
            groupBox4.TabStop = false;
            // 
            // applyButton
            // 
            applyButton.BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            applyButton.BorderRadius = 8;
            applyButton.BorderSize = 1;
            applyButton.ButtonTextColor = System.Drawing.Color.White;
            applyButton.CustomEnabled = true;
            applyButton.DisableColor = System.Drawing.Color.FromArgb(138, 138, 138);
            applyButton.FlatAppearance.BorderSize = 0;
            resources.ApplyResources(applyButton, "applyButton");
            applyButton.ForeColor = System.Drawing.Color.FromArgb(39, 39, 39);
            applyButton.HoverBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.HoverColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.IsPressed = true;
            applyButton.Name = "applyButton";
            applyButton.NormalBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.NormalColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.PressedBorderColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.PressedColor = System.Drawing.Color.FromArgb(255, 136, 0);
            applyButton.UseVisualStyleBackColor = false;
            applyButton.Click += applyButton_Click;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(label6);
            groupBox1.Controls.Add(applyButton);
            groupBox1.Controls.Add(heightNumericUpDown);
            groupBox1.Controls.Add(groupBox4);
            groupBox1.Controls.Add(widthNumericUpDown);
            groupBox1.Controls.Add(groupBox3);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(label8);
            groupBox1.Controls.Add(label7);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(zoomNumericUpDown);
            groupBox1.ForeColor = System.Drawing.Color.White;
            resources.ApplyResources(groupBox1, "groupBox1");
            groupBox1.Name = "groupBox1";
            groupBox1.TabStop = false;
            // 
            // MagnifierToolSettingsPanel
            // 
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            resources.ApplyResources(this, "$this");
            BackColor = System.Drawing.Color.FromArgb(39, 39, 39);
            Controls.Add(groupBox1);
            Name = "MagnifierToolSettingsPanel";
            ((System.ComponentModel.ISupportInitialize)widthNumericUpDown).EndInit();
            ((System.ComponentModel.ISupportInitialize)heightNumericUpDown).EndInit();
            ((System.ComponentModel.ISupportInitialize)zoomNumericUpDown).EndInit();
            ((System.ComponentModel.ISupportInitialize)borderWidthNumericUpDown).EndInit();
            groupBox3.ResumeLayout(false);
            groupBox3.PerformLayout();
            groupBox4.ResumeLayout(false);
            groupBox4.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown widthNumericUpDown;
        private System.Windows.Forms.NumericUpDown heightNumericUpDown;
        private System.Windows.Forms.NumericUpDown zoomNumericUpDown;
        private System.Windows.Forms.CheckBox ellipticalOutlineCheckBox;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private DemosCommonCode.CustomControls.ColorPanelControl borderColorPanelControl;
        private System.Windows.Forms.NumericUpDown borderWidthNumericUpDown;
        private System.Windows.Forms.CheckBox oilPaintingCheckBox;
        private System.Windows.Forms.CheckBox posterizeCheckBox;
        private System.Windows.Forms.CheckBox invertCheckBox;
        private System.Windows.Forms.CheckBox grayscaleCheckBox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox4;
        private RoundButton applyButton;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}